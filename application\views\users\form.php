<!-- Page Header -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">
                <i class="fas fa-users"></i>
                <?php echo $action == 'create' ? 'Tambah Pengguna' : 'Edit Pengguna'; ?>
            </h1>
            <p class="page-subtitle">
                <?php echo $action == 'create' ? 'Tambah pengguna baru ke sistem' : 'Perbarui data pengguna'; ?>
            </p>
        </div>
        <div class="page-actions">
            <a href="<?php echo site_url('users'); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                Kembali
            </a>
        </div>
    </div>
</div>

<!-- Form Card -->
<div class="card">
    <div class="card-header">
        <h3>
            <i class="fas fa-<?php echo $action == 'create' ? 'user-plus' : 'user-edit'; ?>"></i>
            <?php echo $action == 'create' ? 'Data Pengguna Baru' : 'Edit Data Pengguna'; ?>
        </h3>
        <?php if ($action == 'edit' && $user): ?>
        <small class="text-muted">
            <i class="fas fa-calendar"></i>
            Terdaftar sejak: <?php echo date('d M Y H:i', strtotime($user->created_at)); ?>
        </small>
        <?php endif; ?>
    </div>
    <div class="card-body">
        
        <!-- Form -->
        <?php if ($action == 'create'): ?>
            <?php echo form_open('users/store', array('id' => 'user-form', 'class' => 'needs-validation', 'novalidate' => '')); ?>
        <?php else: ?>
            <?php echo form_open('users/update/' . $user->id, array('id' => 'user-form', 'class' => 'needs-validation', 'novalidate' => '')); ?>
        <?php endif; ?>
        
            <div class="row">
                <!-- Left Column -->
                <div class="col-md-8">
                    
                    <!-- Username -->
                    <div class="form-group">
                        <label for="username" class="form-label required">
                            <i class="fas fa-user"></i>
                            Username
                        </label>
                        <input type="text" 
                               id="username" 
                               name="username" 
                               class="form-control <?php echo form_error('username') ? 'is-invalid' : ''; ?>"
                               value="<?php echo set_value('username', $user ? $user->username : ''); ?>"
                               placeholder="Username untuk login"
                               required
                               <?php echo ($action == 'edit' && $user && $user->id == $this->session->userdata('user_id')) ? 'readonly' : ''; ?>>
                        
                        <!-- Real-time validation -->
                        <div class="invalid-feedback" id="username-feedback">
                            <?php echo form_error('username'); ?>
                        </div>
                        
                        <div class="valid-feedback">
                            Username tersedia!
                        </div>
                        
                        <small class="form-text text-muted">
                            Username harus unik, minimal 3 karakter, hanya huruf, angka, dan underscore
                        </small>
                    </div>
                    
                    <!-- Full Name -->
                    <div class="form-group">
                        <label for="full_name" class="form-label required">
                            <i class="fas fa-id-card"></i>
                            Nama Lengkap
                        </label>
                        <input type="text" 
                               id="full_name" 
                               name="full_name" 
                               class="form-control <?php echo form_error('full_name') ? 'is-invalid' : ''; ?>"
                               value="<?php echo set_value('full_name', $user ? $user->full_name : ''); ?>"
                               placeholder="Nama lengkap pengguna"
                               required>
                        
                        <?php if (form_error('full_name')): ?>
                        <div class="invalid-feedback">
                            <?php echo form_error('full_name'); ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Email -->
                    <div class="form-group">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope"></i>
                            Email
                        </label>
                        <input type="email" 
                               id="email" 
                               name="email" 
                               class="form-control <?php echo form_error('email') ? 'is-invalid' : ''; ?>"
                               value="<?php echo set_value('email', $user ? $user->email : ''); ?>"
                               placeholder="<EMAIL>">
                        
                        <!-- Real-time validation -->
                        <div class="invalid-feedback" id="email-feedback">
                            <?php echo form_error('email'); ?>
                        </div>
                        
                        <div class="valid-feedback">
                            Email tersedia!
                        </div>
                        
                        <small class="form-text text-muted">
                            Email opsional, digunakan untuk notifikasi
                        </small>
                    </div>
                    
                    <!-- Phone -->
                    <div class="form-group">
                        <label for="phone" class="form-label">
                            <i class="fas fa-phone"></i>
                            Nomor Telepon
                        </label>
                        <input type="tel" 
                               id="phone" 
                               name="phone" 
                               class="form-control <?php echo form_error('phone') ? 'is-invalid' : ''; ?>"
                               value="<?php echo set_value('phone', $user ? $user->phone : ''); ?>"
                               placeholder="08123456789">
                        
                        <?php if (form_error('phone')): ?>
                        <div class="invalid-feedback">
                            <?php echo form_error('phone'); ?>
                        </div>
                        <?php endif; ?>
                        
                        <small class="form-text text-muted">
                            Nomor telepon untuk komunikasi
                        </small>
                    </div>
                    
                </div>
                
                <!-- Right Column -->
                <div class="col-md-4">
                    
                    <!-- Role -->
                    <div class="form-group">
                        <label for="role" class="form-label required">
                            <i class="fas fa-user-tag"></i>
                            Role
                        </label>
                        <select id="role" 
                                name="role" 
                                class="form-control <?php echo form_error('role') ? 'is-invalid' : ''; ?>"
                                required>
                            <option value="">Pilih Role</option>
                            <option value="admin" <?php echo set_select('role', 'admin', $user && $user->role == 'admin'); ?>>
                                🔧 Admin
                            </option>
                            <option value="kasir" <?php echo set_select('role', 'kasir', $user && $user->role == 'kasir'); ?>>
                                💰 Kasir
                            </option>
                            <option value="pimpinan" <?php echo set_select('role', 'pimpinan', $user && $user->role == 'pimpinan'); ?>>
                                📊 Pimpinan
                            </option>
                        </select>
                        
                        <?php if (form_error('role')): ?>
                        <div class="invalid-feedback">
                            <?php echo form_error('role'); ?>
                        </div>
                        <?php endif; ?>
                        
                        <small class="form-text text-muted">
                            Menentukan hak akses pengguna
                        </small>
                    </div>
                    
                    <!-- Status -->
                    <div class="form-group">
                        <label for="status" class="form-label required">
                            <i class="fas fa-toggle-on"></i>
                            Status
                        </label>
                        <select id="status" 
                                name="status" 
                                class="form-control <?php echo form_error('status') ? 'is-invalid' : ''; ?>"
                                required>
                            <option value="">Pilih Status</option>
                            <option value="aktif" <?php echo set_select('status', 'aktif', ($user && $user->status == 'aktif') || (!$user)); ?>>
                                ✅ Aktif
                            </option>
                            <option value="tidak_aktif" <?php echo set_select('status', 'tidak_aktif', $user && $user->status == 'tidak_aktif'); ?>>
                                ❌ Tidak Aktif
                            </option>
                        </select>
                        
                        <?php if (form_error('status')): ?>
                        <div class="invalid-feedback">
                            <?php echo form_error('status'); ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Password Section -->
                    <div class="password-section">
                        <h5 class="section-title">
                            <i class="fas fa-key"></i>
                            Password
                        </h5>
                        
                        <!-- Password -->
                        <div class="form-group">
                            <label for="password" class="form-label <?php echo $action == 'create' ? 'required' : ''; ?>">
                                Password <?php echo $action == 'edit' ? '(Kosongkan jika tidak diubah)' : ''; ?>
                            </label>
                            <div class="password-input-group">
                                <input type="password" 
                                       id="password" 
                                       name="password" 
                                       class="form-control <?php echo form_error('password') ? 'is-invalid' : ''; ?>"
                                       placeholder="Minimal 6 karakter"
                                       <?php echo $action == 'create' ? 'required' : ''; ?>>
                                <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                    <i class="fas fa-eye" id="password-icon"></i>
                                </button>
                            </div>
                            
                            <?php if (form_error('password')): ?>
                            <div class="invalid-feedback">
                                <?php echo form_error('password'); ?>
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Password Confirm -->
                        <div class="form-group">
                            <label for="password_confirm" class="form-label <?php echo $action == 'create' ? 'required' : ''; ?>">
                                Konfirmasi Password
                            </label>
                            <div class="password-input-group">
                                <input type="password" 
                                       id="password_confirm" 
                                       name="password_confirm" 
                                       class="form-control <?php echo form_error('password_confirm') ? 'is-invalid' : ''; ?>"
                                       placeholder="Ulangi password"
                                       <?php echo $action == 'create' ? 'required' : ''; ?>>
                                <button type="button" class="password-toggle" onclick="togglePassword('password_confirm')">
                                    <i class="fas fa-eye" id="password_confirm-icon"></i>
                                </button>
                            </div>
                            
                            <?php if (form_error('password_confirm')): ?>
                            <div class="invalid-feedback">
                                <?php echo form_error('password_confirm'); ?>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Role Info -->
                    <div class="role-info mt-4">
                        <div class="role-info-header">
                            <i class="fas fa-info-circle"></i>
                            Informasi Role
                        </div>
                        <div class="role-info-body">
                            <div class="role-item" data-role="admin" style="display: none;">
                                <strong>Admin:</strong><br>
                                • Kelola pengguna<br>
                                • Kelola lapangan<br>
                                • Buat booking<br>
                                • Akses penuh sistem
                            </div>
                            <div class="role-item" data-role="kasir" style="display: none;">
                                <strong>Kasir:</strong><br>
                                • Proses pembayaran<br>
                                • Lihat booking<br>
                                • Transaksi kasir
                            </div>
                            <div class="role-item" data-role="pimpinan" style="display: none;">
                                <strong>Pimpinan:</strong><br>
                                • Lihat laporan<br>
                                • Dashboard analitik<br>
                                • Monitoring bisnis
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
            
            <!-- Form Actions -->
            <div class="form-actions">
                <hr>
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <?php if ($action == 'edit' && $user): ?>
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            Terakhir diperbarui: <?php echo date('d M Y H:i', strtotime($user->updated_at)); ?>
                        </small>
                        <?php endif; ?>
                    </div>
                    <div>
                        <a href="<?php echo site_url('users'); ?>" class="btn btn-secondary mr-2">
                            <i class="fas fa-times"></i>
                            Batal
                        </a>
                        <button type="submit" class="btn btn-primary" id="submit-btn">
                            <i class="fas fa-save"></i>
                            <?php echo $action == 'create' ? 'Simpan Pengguna' : 'Perbarui Pengguna'; ?>
                        </button>
                    </div>
                </div>
            </div>
            
        <?php echo form_close(); ?>
        
    </div>
</div>

<style>
.password-section {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

.section-title {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 1.1rem;
}

.password-input-group {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    font-size: 14px;
    padding: 0;
    z-index: 3;
}

.password-toggle:hover {
    color: #3498db;
}

.role-info {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
}

.role-info-header {
    background: #e9ecef;
    padding: 10px 15px;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    border-radius: 7px 7px 0 0;
}

.role-info-body {
    padding: 15px;
}

.role-item {
    font-size: 14px;
    line-height: 1.6;
    color: #495057;
}

.form-label.required::after {
    content: ' *';
    color: #e74c3c;
}

.is-invalid {
    border-color: #e74c3c;
}

.is-valid {
    border-color: #28a745;
}

.valid-feedback,
.invalid-feedback {
    display: block;
    font-size: 14px;
    margin-top: 5px;
}

.valid-feedback {
    color: #28a745;
}

.invalid-feedback {
    color: #e74c3c;
}

.form-actions {
    margin-top: 30px;
}

@media (max-width: 768px) {
    .password-section {
        margin-top: 15px;
        padding: 15px;
    }
    
    .role-info {
        margin-top: 15px;
    }
    
    .form-actions .d-flex {
        flex-direction: column;
        align-items: stretch !important;
    }
    
    .form-actions .d-flex > div:last-child {
        margin-top: 15px;
        display: flex;
        gap: 10px;
    }
    
    .form-actions .btn {
        flex: 1;
    }
}
</style>

<script>
$(document).ready(function() {
    // Real-time username validation
    let usernameCheckTimeout;
    $('#username').on('input', function() {
        const username = $(this).val().trim();
        const excludeId = <?php echo $user ? $user->id : 'null'; ?>;
        
        clearTimeout(usernameCheckTimeout);
        
        if (username.length >= 3) {
            usernameCheckTimeout = setTimeout(function() {
                checkUsername(username, excludeId);
            }, 500);
        } else {
            $('#username').removeClass('is-valid is-invalid');
        }
    });
    
    // Real-time email validation
    let emailCheckTimeout;
    $('#email').on('input', function() {
        const email = $(this).val().trim();
        const excludeId = <?php echo $user ? $user->id : 'null'; ?>;
        
        clearTimeout(emailCheckTimeout);
        
        if (email.length > 0 && isValidEmail(email)) {
            emailCheckTimeout = setTimeout(function() {
                checkEmail(email, excludeId);
            }, 500);
        } else {
            $('#email').removeClass('is-valid is-invalid');
        }
    });
    
    // Password confirmation validation
    $('#password_confirm').on('input', function() {
        const password = $('#password').val();
        const confirmPassword = $(this).val();
        
        if (confirmPassword.length > 0) {
            if (password === confirmPassword) {
                $(this).removeClass('is-invalid').addClass('is-valid');
            } else {
                $(this).removeClass('is-valid').addClass('is-invalid');
            }
        } else {
            $(this).removeClass('is-valid is-invalid');
        }
    });
    
    // Role info display
    $('#role').on('change', function() {
        const selectedRole = $(this).val();
        $('.role-item').hide();
        if (selectedRole) {
            $(`.role-item[data-role="${selectedRole}"]`).show();
        }
    });
    
    // Initialize role info
    $('#role').trigger('change');
    
    // Form submission
    $('#user-form').on('submit', function(e) {
        e.preventDefault();
        
        if (validateForm()) {
            showLoading($('#submit-btn'));
            this.submit();
        }
    });
});

function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '-icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

function checkUsername(username, excludeId) {
    $.ajax({
        url: '<?php echo site_url("users/check_username"); ?>',
        type: 'POST',
        data: {
            username: username,
            exclude_id: excludeId
        },
        dataType: 'json',
        success: function(response) {
            const input = $('#username');
            const feedback = $('#username-feedback');
            
            if (response.exists) {
                input.removeClass('is-valid').addClass('is-invalid');
                feedback.text('Username sudah digunakan');
            } else {
                input.removeClass('is-invalid').addClass('is-valid');
                feedback.text('');
            }
        },
        error: function() {
            console.log('Error checking username');
        }
    });
}

function checkEmail(email, excludeId) {
    $.ajax({
        url: '<?php echo site_url("users/check_email"); ?>',
        type: 'POST',
        data: {
            email: email,
            exclude_id: excludeId
        },
        dataType: 'json',
        success: function(response) {
            const input = $('#email');
            const feedback = $('#email-feedback');
            
            if (response.exists) {
                input.removeClass('is-valid').addClass('is-invalid');
                feedback.text('Email sudah digunakan');
            } else {
                input.removeClass('is-invalid').addClass('is-valid');
                feedback.text('');
            }
        },
        error: function() {
            console.log('Error checking email');
        }
    });
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function validateForm() {
    let isValid = true;
    
    // Check required fields
    $('#user-form [required]').each(function() {
        const field = $(this);
        const value = field.val().trim();
        
        if (!value) {
            field.addClass('is-invalid');
            isValid = false;
        } else {
            field.removeClass('is-invalid');
        }
    });
    
    // Check if username is unique
    if ($('#username').hasClass('is-invalid')) {
        isValid = false;
    }
    
    // Check if email is unique (if provided)
    if ($('#email').val() && $('#email').hasClass('is-invalid')) {
        isValid = false;
    }
    
    // Check password confirmation
    const password = $('#password').val();
    const passwordConfirm = $('#password_confirm').val();
    
    <?php if ($action == 'create'): ?>
    // For create, password is required
    if (!password || password.length < 6) {
        $('#password').addClass('is-invalid');
        isValid = false;
    }
    
    if (password !== passwordConfirm) {
        $('#password_confirm').addClass('is-invalid');
        isValid = false;
    }
    <?php else: ?>
    // For edit, password is optional but if provided must match
    if (password && password.length < 6) {
        $('#password').addClass('is-invalid');
        isValid = false;
    }
    
    if (password && password !== passwordConfirm) {
        $('#password_confirm').addClass('is-invalid');
        isValid = false;
    }
    <?php endif; ?>
    
    if (!isValid) {
        showAlert('error', 'Mohon perbaiki kesalahan pada form');
        
        // Scroll to first error
        const firstError = $('.is-invalid:first');
        if (firstError.length) {
            $('html, body').animate({
                scrollTop: firstError.offset().top - 100
            }, 500);
        }
    }
    
    return isValid;
}

function showLoading(element) {
    element.prop('disabled', true);
    const originalText = element.html();
    element.data('original-text', originalText);
    element.html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');
}
</script>