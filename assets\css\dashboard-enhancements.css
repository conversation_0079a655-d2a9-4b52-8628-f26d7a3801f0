/* =============================================================================
   DASHBOARD ENHANCEMENTS
   Specific improvements for dashboard pages
   ============================================================================= */

/* Dashboard Header */
.dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 24px 0;
    margin-bottom: 24px;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
}

.dashboard-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s infinite linear;
    pointer-events: none;
}

@keyframes float {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.dashboard-header .container {
    position: relative;
    z-index: 1;
}

.dashboard-header h1 {
    color: white;
    margin-bottom: 8px;
    font-size: 28px;
    font-weight: 700;
}

.dashboard-header p {
    color: rgba(255,255,255,0.9);
    margin: 0;
    font-size: 16px;
}

/* Enhanced Stats Cards */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
}

.enhanced-stat-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.enhanced-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3498db, #2980b9);
    transition: height 0.3s ease;
}

.enhanced-stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.enhanced-stat-card:hover::before {
    height: 6px;
}

.enhanced-stat-card.primary::before {
    background: linear-gradient(90deg, #3498db, #2980b9);
}

.enhanced-stat-card.success::before {
    background: linear-gradient(90deg, #27ae60, #229954);
}

.enhanced-stat-card.warning::before {
    background: linear-gradient(90deg, #f39c12, #e67e22);
}

.enhanced-stat-card.danger::before {
    background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.enhanced-stat-card.info::before {
    background: linear-gradient(90deg, #17a2b8, #138496);
}

.stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.stat-icon.success {
    background: linear-gradient(135deg, #27ae60, #229954);
}

.stat-icon.warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.stat-icon.danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.stat-icon.info {
    background: linear-gradient(135deg, #17a2b8, #138496);
}

.stat-value {
    font-size: 32px;
    font-weight: 700;
    color: #2c3e50;
    line-height: 1;
    margin-bottom: 4px;
}

.stat-label {
    color: #6c757d;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 12px;
}

.stat-change {
    display: flex;
    align-items: center;
    font-size: 12px;
    font-weight: 600;
}

.stat-change.positive {
    color: #27ae60;
}

.stat-change.negative {
    color: #e74c3c;
}

.stat-change i {
    margin-right: 4px;
    font-size: 10px;
}

/* Quick Actions Panel */
.quick-actions {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
    margin-bottom: 24px;
}

.quick-actions h5 {
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
}

.quick-actions h5 i {
    color: #3498db;
    margin-right: 8px;
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.action-btn {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    text-decoration: none;
    color: #495057;
    transition: all 0.2s ease;
    font-size: 13px;
    font-weight: 500;
}

.action-btn:hover {
    background: #e9ecef;
    color: #2c3e50;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    text-decoration: none;
}

.action-btn i {
    margin-right: 8px;
    font-size: 14px;
    color: #3498db;
}

/* Recent Activity */
.recent-activity {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
}

.recent-activity h5 {
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
}

.recent-activity h5 i {
    color: #3498db;
    margin-right: 8px;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
    transition: all 0.2s ease;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-item:hover {
    background: #f8f9fa;
    margin: 0 -20px;
    padding: 12px 20px;
    border-radius: 6px;
}

.activity-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: white;
    margin-right: 12px;
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.activity-icon.success {
    background: linear-gradient(135deg, #27ae60, #229954);
}

.activity-icon.warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.activity-icon.danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-size: 13px;
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 2px;
}

.activity-time {
    font-size: 11px;
    color: #6c757d;
}

/* Chart Container */
.chart-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
    margin-bottom: 24px;
}

.chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.chart-title {
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.chart-title i {
    color: #3498db;
    margin-right: 8px;
}

.chart-controls {
    display: flex;
    gap: 8px;
}

.chart-btn {
    padding: 6px 12px;
    font-size: 12px;
    border: 1px solid #e9ecef;
    background: white;
    color: #6c757d;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.chart-btn.active,
.chart-btn:hover {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

/* Responsive Design */
@media (max-width: 768px) {
    .stats-container {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .enhanced-stat-card {
        padding: 20px 16px;
    }
    
    .stat-value {
        font-size: 28px;
    }
    
    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 18px;
    }
    
    .action-grid {
        grid-template-columns: 1fr;
        gap: 8px;
    }
    
    .dashboard-header {
        padding: 20px 0;
        margin-bottom: 20px;
    }
    
    .dashboard-header h1 {
        font-size: 24px;
    }
    
    .dashboard-header p {
        font-size: 14px;
    }
}
