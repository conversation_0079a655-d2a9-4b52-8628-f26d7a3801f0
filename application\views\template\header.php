<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($title) ? $title : 'Padel Booking System'; ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="<?php echo base_url('assets/css/style.css'); ?>">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- CSRF Token untuk AJAX -->
    <meta name="csrf-token" content="<?php echo $this->security->get_csrf_hash(); ?>">
    
    <?php if (isset($extra_css)): ?>
        <?php foreach ($extra_css as $css): ?>
            <link rel="stylesheet" href="<?php echo base_url($css); ?>">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body>
    
    <?php if (isset($page) && $page == 'login'): ?>
        <!-- Login page tidak perlu header/sidebar -->
    <?php else: ?>
        <!-- Header -->
        <header class="header">
            <div class="container-fluid">
                <div class="header-content">
                    <div class="header-left">
                        <button class="sidebar-toggle" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                        <h1>
                            <i class="fas fa-table-tennis"></i>
                            Padel Booking System
                        </h1>
                    </div>
                    
                    <div class="header-right">
                        <div class="user-info">
                            <span>
                                <i class="fas fa-user"></i>
                                <?php echo $this->session->userdata('full_name'); ?>
                            </span>
                            <span class="user-role">
                                (<?php echo ucfirst($this->session->userdata('role')); ?>)
                            </span>
                            <a href="<?php echo site_url('auth/logout'); ?>" onclick="return confirm('Yakin ingin logout?')">
                                <i class="fas fa-sign-out-alt"></i>
                                Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- Sidebar -->
        <?php $this->load->view('template/sidebar'); ?>
        
        <!-- Main Content -->
        <main class="main-content" id="main-content">
    <?php endif; ?>