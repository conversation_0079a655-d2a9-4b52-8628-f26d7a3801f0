/**
 * PADEL BOOKING SYSTEM - MAIN JAVASCRIPT
 * Updated for production environment
 */

// Global variables
let sidebarCollapsed = false;
const base_url = 'https://test-apps.site/padel/';

// Document ready
$(document).ready(function() {
    initializeApp();
});

/**
 * Initialize application
 */
function initializeApp() {
    // Setup CSRF token for AJAX
    setupCSRFToken();

    // Initialize sidebar
    initializeSidebar();

    // Initialize form validations
    initializeFormValidations();

    // Initialize tooltips
    initializeTooltips();

    // Initialize auto-hide alerts
    initializeAlerts();

    // Initialize responsive tables
    initializeResponsiveTables();

    // Initialize datetime pickers
    initializeDateTimePickers();

    // Initialize number formatting
    initializeNumberFormatting();

    // Initialize enhanced UI features
    initializeEnhancedUI();

    // Initialize animations
    initializeAnimations();

    // Initialize smooth scrolling
    initializeSmoothScrolling();
}

/**
 * Setup CSRF token for AJAX requests
 */
function setupCSRFToken() {
    const token = $('meta[name="csrf-token"]').attr('content');
    
    if (token) {
        $.ajaxSetup({
            beforeSend: function(xhr, settings) {
                if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                    xhr.setRequestHeader("X-CSRF-TOKEN", token);
                }
            }
        });
    }
}

/**
 * Initialize sidebar functionality
 */
function initializeSidebar() {
    // Check saved sidebar state
    const savedState = localStorage.getItem('sidebarCollapsed');
    if (savedState === 'true') {
        toggleSidebar();
    }
    
    // Sidebar toggle button
    $('.sidebar-toggle').on('click', function() {
        toggleSidebar();
    });
    
    // Close sidebar on mobile when clicking menu item
    $('.sidebar-nav a').on('click', function() {
        if (window.innerWidth <= 768) {
            toggleSidebar();
        }
    });
    
    // Auto-collapse sidebar on mobile
    if (window.innerWidth <= 768) {
        $('#sidebar').addClass('collapsed');
        $('#main-content').addClass('expanded');
        sidebarCollapsed = true;
    }
}

/**
 * Toggle sidebar
 */
function toggleSidebar() {
    const sidebar = $('#sidebar');
    const mainContent = $('#main-content');
    
    if (window.innerWidth <= 768) {
        // Mobile behavior
        sidebar.toggleClass('show');
    } else {
        // Desktop behavior
        sidebar.toggleClass('collapsed');
        mainContent.toggleClass('expanded');
    }
    
    sidebarCollapsed = !sidebarCollapsed;
    
    // Save state to localStorage
    localStorage.setItem('sidebarCollapsed', sidebarCollapsed);
    
    // Update toggle button icon
    const toggleBtn = $('.sidebar-toggle i');
    if (sidebarCollapsed) {
        toggleBtn.removeClass('fa-bars').addClass('fa-times');
    } else {
        toggleBtn.removeClass('fa-times').addClass('fa-bars');
    }
}

/**
 * Initialize form validations
 */
function initializeFormValidations() {
    // Real-time form validation
    $('form').on('submit', function(e) {
        const form = $(this);
        let isValid = true;
        
        // Check required fields
        form.find('[required]').each(function() {
            const field = $(this);
            const value = field.val().trim();
            
            if (!value) {
                showFieldError(field, 'Field ini wajib diisi');
                isValid = false;
            } else {
                clearFieldError(field);
            }
        });
        
        // Email validation
        form.find('input[type="email"]').each(function() {
            const field = $(this);
            const value = field.val().trim();
            
            if (value && !isValidEmail(value)) {
                showFieldError(field, 'Format email tidak valid');
                isValid = false;
            }
        });
        
        // Phone validation
        form.find('input[name="phone"], input[name="telepon"], input[name="telepon_pemesan"]').each(function() {
            const field = $(this);
            const value = field.val().trim();
            
            if (value && !isValidPhone(value)) {
                showFieldError(field, 'Format nomor telepon tidak valid');
                isValid = false;
            }
        });
        
        // Password confirmation
        const password = form.find('input[name="password"]').val();
        const confirmPassword = form.find('input[name="password_confirm"]').val();
        
        if (password && confirmPassword && password !== confirmPassword) {
            showFieldError(form.find('input[name="password_confirm"]'), 'Konfirmasi password tidak sama');
            isValid = false;
        }
        
        // Time validation for booking
        const jamMulai = form.find('input[name="jam_mulai"]').val();
        const jamSelesai = form.find('input[name="jam_selesai"]').val();
        
        if (jamMulai && jamSelesai && jamMulai >= jamSelesai) {
            showFieldError(form.find('input[name="jam_selesai"]'), 'Jam selesai harus lebih besar dari jam mulai');
            isValid = false;
        }
        
        if (!isValid) {
            e.preventDefault();
            showAlert('error', 'Mohon perbaiki kesalahan pada form');
            $('html, body').animate({
                scrollTop: $('.field-error:first').offset().top - 100
            }, 500);
        }
    });
    
    // Clear errors on input
    $('input, select, textarea').on('input change', function() {
        clearFieldError($(this));
    });
}

/**
 * Show field error
 */
function showFieldError(field, message) {
    field.addClass('error');
    
    // Remove existing error message
    field.siblings('.field-error').remove();
    
    // Add error message
    field.after('<div class="field-error">' + message + '</div>');
}

/**
 * Clear field error
 */
function clearFieldError(field) {
    field.removeClass('error');
    field.siblings('.field-error').remove();
}

/**
 * Email validation
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Phone validation
 */
function isValidPhone(phone) {
    const phoneRegex = /^[\+]?[0-9\-\s\(\)]{10,15}$/;
    return phoneRegex.test(phone);
}

/**
 * Initialize tooltips
 */
function initializeTooltips() {
    $('[data-toggle="tooltip"]').each(function() {
        const element = $(this);
        const title = element.attr('title') || element.data('title');
        
        if (title) {
            element.on('mouseenter', function() {
                showTooltip(element, title);
            });
            
            element.on('mouseleave', function() {
                hideTooltip();
            });
        }
    });
}

/**
 * Show tooltip
 */
function showTooltip(element, text) {
    const tooltip = $('<div class="tooltip">' + text + '</div>');
    $('body').append(tooltip);
    
    const elementOffset = element.offset();
    const elementHeight = element.outerHeight();
    
    tooltip.css({
        position: 'absolute',
        top: elementOffset.top + elementHeight + 5,
        left: elementOffset.left,
        zIndex: 9999
    });
}

/**
 * Hide tooltip
 */
function hideTooltip() {
    $('.tooltip').remove();
}

/**
 * Initialize alerts
 */
function initializeAlerts() {
    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
    
    // Close button for alerts
    $('.alert').each(function() {
        const alert = $(this);
        if (!alert.find('.alert-close').length) {
            alert.append('<button type="button" class="alert-close">&times;</button>');
        }
    });
    
    // Handle alert close button
    $(document).on('click', '.alert-close', function() {
        $(this).parent().fadeOut();
    });
}

/**
 * Show alert message
 */
function showAlert(type, message) {
    const alertClass = 'alert-' + type;
    const iconClass = getAlertIcon(type);
    
    const alertHtml = `
        <div class="alert ${alertClass} fade-in">
            <i class="${iconClass}"></i>
            ${message}
            <button type="button" class="alert-close">&times;</button>
        </div>
    `;
    
    // Remove existing alerts
    $('.alert').remove();
    
    // Add new alert
    if ($('.main-content').length) {
        $('.main-content').prepend(alertHtml);
    } else {
        $('body').prepend(alertHtml);
    }
    
    // Auto-hide after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}

/**
 * Get alert icon based on type
 */
function getAlertIcon(type) {
    const icons = {
        'success': 'fas fa-check-circle',
        'error': 'fas fa-exclamation-circle',
        'warning': 'fas fa-exclamation-triangle',
        'info': 'fas fa-info-circle'
    };
    
    return icons[type] || icons['info'];
}

/**
 * Initialize responsive tables
 */
function initializeResponsiveTables() {
    $('.table').each(function() {
        const table = $(this);
        
        if (!table.parent().hasClass('table-responsive')) {
            table.wrap('<div class="table-responsive"></div>');
        }
    });
}

/**
 * Initialize date time pickers
 */
function initializeDateTimePickers() {
    // Date inputs
    $('input[type="date"]').each(function() {
        const input = $(this);
        
        // Set min date to today
        if (!input.attr('min')) {
            input.attr('min', getCurrentDate());
        }
        
        // Set max date if booking_advance_days is set
        const maxDays = 30; // Default 30 days
        const maxDate = new Date();
        maxDate.setDate(maxDate.getDate() + maxDays);
        
        if (!input.attr('max')) {
            input.attr('max', formatDate(maxDate));
        }
    });
    
    // Time inputs
    $('input[type="time"]').each(function() {
        const input = $(this);
        
        // Set default step to 60 minutes
        if (!input.attr('step')) {
            input.attr('step', '3600');
        }
    });
}

/**
 * Get current date in YYYY-MM-DD format
 */
function getCurrentDate() {
    const today = new Date();
    return formatDate(today);
}

/**
 * Format date to YYYY-MM-DD
 */
function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

/**
 * Initialize number formatting
 */
function initializeNumberFormatting() {
    // Format currency inputs
    $('input[data-currency]').on('input', function() {
        let value = $(this).val().replace(/[^\d]/g, '');
        if (value) {
            $(this).val(formatNumber(value));
        }
    });
    
    // Format number inputs
    $('input[data-number]').on('input', function() {
        let value = $(this).val().replace(/[^\d]/g, '');
        if (value) {
            $(this).val(formatNumber(value));
        }
    });
}

/**
 * Format number with thousand separators
 */
function formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
}

/**
 * Confirm delete action
 */
function confirmDelete(message) {
    return confirm(message || 'Yakin ingin menghapus data ini?');
}

/**
 * Show loading state
 */
function showLoading(element) {
    element = $(element);
    element.prop('disabled', true);
    
    const originalText = element.text();
    element.data('original-text', originalText);
    element.html('<i class="fas fa-spinner fa-spin"></i> Loading...');
}

/**
 * Hide loading state
 */
function hideLoading(element) {
    element = $(element);
    element.prop('disabled', false);
    
    const originalText = element.data('original-text');
    if (originalText) {
        element.text(originalText);
    }
}

/**
 * Format currency (Indonesian Rupiah)
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0
    }).format(amount);
}

/**
 * Format date (Indonesian format)
 */
function formatDateIndonesian(date) {
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    };
    
    return new Date(date).toLocaleDateString('id-ID', options);
}

/**
 * Format time (24 hour format)
 */
function formatTime(time) {
    return new Date('1970-01-01T' + time + 'Z').toLocaleTimeString('id-ID', {
        timeZone: 'UTC',
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
    });
}

/**
 * Debounce function for search inputs
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Simple search functionality
 */
function initializeSearch(searchInput, targetTable) {
    const searchFunction = debounce(function() {
        const searchTerm = $(searchInput).val().toLowerCase();
        const rows = $(targetTable + ' tbody tr');
        
        rows.each(function() {
            const row = $(this);
            const text = row.text().toLowerCase();
            
            if (text.includes(searchTerm)) {
                row.show();
            } else {
                row.hide();
            }
        });
        
        // Update row count
        const visibleRows = $(targetTable + ' tbody tr:visible').length;
        updateRowCount(targetTable, visibleRows);
    }, 300);
    
    $(searchInput).on('input', searchFunction);
}

/**
 * Update row count display
 */
function updateRowCount(tableId, count) {
    const countElement = $(tableId).closest('.card').find('.row-count');
    if (countElement.length) {
        countElement.text(`Menampilkan ${count} data`);
    }
}

/**
 * Print table
 */
function printTable(tableId, title) {
    const table = $(tableId).clone();
    
    // Remove action columns
    table.find('.no-print').remove();
    
    const printWindow = window.open('', '_blank');
    
    printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>${title}</title>
            <style>
                body { 
                    font-family: Arial, sans-serif; 
                    margin: 20px;
                }
                h1 {
                    text-align: center;
                    margin-bottom: 30px;
                    color: #333;
                }
                table { 
                    width: 100%; 
                    border-collapse: collapse;
                    margin-top: 20px;
                }
                th, td { 
                    border: 1px solid #ddd; 
                    padding: 8px; 
                    text-align: left;
                    font-size: 12px;
                }
                th { 
                    background-color: #f5f5f5;
                    font-weight: bold;
                }
                .print-info {
                    margin-top: 30px;
                    font-size: 10px;
                    color: #666;
                    text-align: center;
                }
                @media print {
                    body { margin: 0; }
                }
            </style>
        </head>
        <body>
            <h1>${title}</h1>
            <div class="print-info">
                Dicetak pada: ${new Date().toLocaleString('id-ID')}
            </div>
            ${table.prop('outerHTML')}
            <div class="print-info">
                © ${new Date().getFullYear()} Padel Booking System
            </div>
        </body>
        </html>
    `);
    
    printWindow.document.close();
    printWindow.print();
}

/**
 * Export table to CSV
 */
function exportTableToCSV(tableId, filename) {
    const table = $(tableId);
    let csv = [];
    
    // Get headers
    const headers = [];
    table.find('thead th').not('.no-print').each(function() {
        headers.push($(this).text().trim());
    });
    csv.push(headers.join(','));
    
    // Get data rows
    table.find('tbody tr').each(function() {
        const row = [];
        $(this).find('td').not('.no-print').each(function() {
            let cellText = $(this).text().trim();
            // Handle cells with commas
            cellText = '"' + cellText.replace(/"/g, '""') + '"';
            row.push(cellText);
        });
        if (row.length > 0) {
            csv.push(row.join(','));
        }
    });
    
    // Download CSV
    const csvContent = csv.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = window.URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = filename + '_' + getCurrentDate() + '.csv';
    a.style.display = 'none';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    
    window.URL.revokeObjectURL(url);
}

/**
 * Check booking availability (for booking form)
 */
function checkBookingAvailability(lapanganId, tanggal, jamMulai, jamSelesai, bookingId = null) {
    return $.ajax({
        url: base_url + 'api/check-availability',
        method: 'POST',
        data: {
            lapangan_id: lapanganId,
            tanggal: tanggal,
            jam_mulai: jamMulai,
            jam_selesai: jamSelesai,
            booking_id: bookingId
        },
        dataType: 'json'
    });
}

/**
 * Get lapangan price
 */
function getLapanganHarga(lapanganId) {
    return $.ajax({
        url: base_url + 'api/get-lapangan-harga/' + lapanganId,
        method: 'GET',
        dataType: 'json'
    });
}

/**
 * Calculate booking duration and price
 */
function calculateBookingPrice(jamMulai, jamSelesai, hargaPerJam) {
    const start = new Date('1970-01-01T' + jamMulai + ':00');
    const end = new Date('1970-01-01T' + jamSelesai + ':00');
    
    if (end <= start) {
        return { duration: 0, price: 0 };
    }
    
    const duration = (end - start) / (1000 * 60 * 60); // in hours
    const price = duration * hargaPerJam;
    
    return {
        duration: duration,
        price: price
    };
}

/**
 * Auto-calculate price when time changes
 */
function initializeBookingCalculator() {
    const lapanganSelect = $('select[name="lapangan_id"]');
    const jamMulai = $('input[name="jam_mulai"]');
    const jamSelesai = $('input[name="jam_selesai"]');
    const durasiDisplay = $('#durasi-display');
    const hargaDisplay = $('#harga-display');
    const hargaTotalInput = $('input[name="harga_total"]');
    
    function updateCalculation() {
        const lapanganId = lapanganSelect.val();
        const mulai = jamMulai.val();
        const selesai = jamSelesai.val();
        
        if (!lapanganId || !mulai || !selesai) {
            durasiDisplay.text('-');
            hargaDisplay.text('-');
            hargaTotalInput.val('');
            return;
        }
        
        // Get lapangan price
        getLapanganHarga(lapanganId).done(function(response) {
            if (response.success) {
                const hargaPerJam = response.data.harga_per_jam;
                const calculation = calculateBookingPrice(mulai, selesai, hargaPerJam);
                
                if (calculation.duration > 0) {
                    durasiDisplay.text(calculation.duration + ' jam');
                    hargaDisplay.text(formatCurrency(calculation.price));
                    hargaTotalInput.val(calculation.price);
                } else {
                    durasiDisplay.text('-');
                    hargaDisplay.text('-');
                    hargaTotalInput.val('');
                }
            }
        });
    }
    
    lapanganSelect.on('change', updateCalculation);
    jamMulai.on('change', updateCalculation);
    jamSelesai.on('change', updateCalculation);
}

/**
 * Show modal
 */
function showModal(modalId) {
    $(modalId).fadeIn();
}

/**
 * Hide modal
 */
function hideModal(modalId) {
    $(modalId).fadeOut();
}

/**
 * Initialize modal handlers
 */
function initializeModals() {
    // Close modal when clicking close button
    $('.modal-close').on('click', function() {
        $(this).closest('.modal').fadeOut();
    });
    
    // Close modal when clicking outside
    $('.modal').on('click', function(e) {
        if (e.target === this) {
            $(this).fadeOut();
        }
    });
    
    // Close modal with ESC key
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape') {
            $('.modal:visible').fadeOut();
        }
    });
}

/**
 * Refresh page data
 */
function refreshData() {
    location.reload();
}

/**
 * Copy text to clipboard
 */
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showAlert('success', 'Teks berhasil disalin ke clipboard');
    }).catch(function() {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showAlert('success', 'Teks berhasil disalin ke clipboard');
    });
}

// Global error handler for AJAX
$(document).ajaxError(function(event, xhr, settings, thrownError) {
    if (xhr.status === 401) {
        showAlert('error', 'Sesi Anda telah berakhir. Silakan login kembali.');
        setTimeout(function() {
            window.location.href = base_url + 'auth/login';
        }, 2000);
    } else if (xhr.status === 403) {
        showAlert('error', 'Anda tidak memiliki akses untuk melakukan aksi ini.');
    } else if (xhr.status >= 500) {
        showAlert('error', 'Terjadi kesalahan server. Silakan coba lagi.');
    } else if (xhr.status === 0) {
        showAlert('error', 'Tidak dapat terhubung ke server. Periksa koneksi internet Anda.');
    }
});

// Responsive sidebar for mobile
$(window).resize(function() {
    if (window.innerWidth <= 768) {
        if (!$('#sidebar').hasClass('collapsed')) {
            $('#sidebar').addClass('collapsed');
            $('#main-content').addClass('expanded');
            sidebarCollapsed = true;
        }
    }
});

/**
 * Initialize enhanced UI features
 */
function initializeEnhancedUI() {
    // Add ripple effect to buttons
    $('.btn').on('click', function(e) {
        createRippleEffect(e, $(this));
    });

    // Add hover effects to cards
    $('.card').addClass('hover-lift');

    // Add interactive class to clickable elements
    $('.btn, .card, .table tbody tr').addClass('interactive');

    // Initialize loading states
    initializeLoadingStates();

    // Initialize enhanced dropdowns
    initializeEnhancedDropdowns();
}

/**
 * Create ripple effect
 */
function createRippleEffect(e, $element) {
    const ripple = $('<span class="ripple"></span>');
    const rect = $element[0].getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = e.clientX - rect.left - size / 2;
    const y = e.clientY - rect.top - size / 2;

    ripple.css({
        width: size,
        height: size,
        left: x,
        top: y,
        position: 'absolute',
        borderRadius: '50%',
        background: 'rgba(255,255,255,0.3)',
        transform: 'scale(0)',
        animation: 'ripple-animation 0.6s linear',
        pointerEvents: 'none'
    });

    $element.append(ripple);

    setTimeout(() => {
        ripple.remove();
    }, 600);
}

/**
 * Initialize animations
 */
function initializeAnimations() {
    // Animate elements on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in-up');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe cards and stats
    $('.card, .stats-card, .nav-card').each(function() {
        observer.observe(this);
    });

    // Stagger animation for table rows
    $('.table tbody tr').each(function(index) {
        $(this).css('animation-delay', (index * 0.05) + 's');
        $(this).addClass('fade-in');
    });
}

/**
 * Initialize smooth scrolling
 */
function initializeSmoothScrolling() {
    $('a[href^="#"]').on('click', function(e) {
        e.preventDefault();
        const target = $($(this).attr('href'));

        if (target.length) {
            $('html, body').animate({
                scrollTop: target.offset().top - 100
            }, 800, 'easeInOutCubic');
        }
    });
}

/**
 * Initialize loading states
 */
function initializeLoadingStates() {
    // Add loading state to forms on submit
    $('form').on('submit', function() {
        const $form = $(this);
        const $submitBtn = $form.find('button[type="submit"]');

        if (!$submitBtn.hasClass('no-loading')) {
            $submitBtn.addClass('loading-state');
            $submitBtn.prop('disabled', true);

            const originalText = $submitBtn.html();
            $submitBtn.html('<i class="fas fa-spinner fa-spin"></i> Memproses...');

            // Reset after 10 seconds if no response
            setTimeout(() => {
                $submitBtn.removeClass('loading-state');
                $submitBtn.prop('disabled', false);
                $submitBtn.html(originalText);
            }, 10000);
        }
    });
}

/**
 * Initialize enhanced dropdowns
 */
function initializeEnhancedDropdowns() {
    $('.dropdown-toggle').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const $dropdown = $(this).next('.dropdown-menu');
        const $allDropdowns = $('.dropdown-menu');

        // Close all other dropdowns
        $allDropdowns.not($dropdown).removeClass('show').fadeOut(200);

        // Toggle current dropdown
        if ($dropdown.hasClass('show')) {
            $dropdown.removeClass('show').fadeOut(200);
        } else {
            $dropdown.addClass('show').fadeIn(200);
        }
    });

    // Close dropdowns when clicking outside
    $(document).on('click', function() {
        $('.dropdown-menu.show').removeClass('show').fadeOut(200);
    });
}

/**
 * Enhanced alert system
 */
function showEnhancedAlert(type, message, duration = 5000) {
    const alertId = 'alert-' + Date.now();
    const iconClass = getAlertIcon(type);

    const alertHtml = `
        <div id="${alertId}" class="alert alert-${type} enhanced-alert fade-in-down">
            <div class="alert-content">
                <i class="${iconClass}"></i>
                <span>${message}</span>
            </div>
            <button type="button" class="alert-close" onclick="closeAlert('${alertId}')">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    // Remove existing alerts of same type
    $(`.alert-${type}`).fadeOut(300, function() {
        $(this).remove();
    });

    // Add new alert
    if ($('#alert-container').length === 0) {
        $('body').prepend('<div id="alert-container" style="position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;"></div>');
    }

    $('#alert-container').prepend(alertHtml);

    // Auto-hide after duration
    setTimeout(() => {
        closeAlert(alertId);
    }, duration);
}

/**
 * Close alert
 */
function closeAlert(alertId) {
    $(`#${alertId}`).addClass('fade-out').fadeOut(300, function() {
        $(this).remove();
    });
}

// Initialize when document is ready
$(document).ready(function() {
    initializeModals();
    initializeBookingCalculator();

    // Auto-refresh every 5 minutes for dashboard
    if (window.location.pathname.includes('dashboard')) {
        setInterval(function() {
            if (document.visibilityState === 'visible') {
                // Refresh dashboard stats
                refreshDashboardStats();
            }
        }, 300000); // 5 minutes
    }

    // Initialize page-specific enhancements
    initializePageEnhancements();
});

/**
 * Initialize page-specific enhancements
 */
function initializePageEnhancements() {
    // Dashboard enhancements
    if (window.location.pathname.includes('dashboard')) {
        initializeDashboardEnhancements();
    }

    // Booking page enhancements
    if (window.location.pathname.includes('booking')) {
        initializeBookingEnhancements();
    }

    // Table enhancements
    if ($('.table').length > 0) {
        initializeTableEnhancements();
    }
}

/**
 * Initialize dashboard enhancements
 */
function initializeDashboardEnhancements() {
    // Animate stats cards
    $('.stats-card').each(function(index) {
        $(this).css('animation-delay', (index * 0.1) + 's');
        $(this).addClass('scale-in');
    });

    // Add hover effects to quick action buttons
    $('.action-btn').addClass('hover-lift');

    // Initialize chart animations if charts exist
    if (typeof Chart !== 'undefined') {
        Chart.defaults.animation.duration = 1000;
        Chart.defaults.animation.easing = 'easeInOutCubic';
    }
}

/**
 * Initialize booking enhancements
 */
function initializeBookingEnhancements() {
    // Add smooth transitions to booking cards
    $('.booking-card').addClass('hover-lift');

    // Enhanced status badge interactions
    $('.badge').on('mouseenter', function() {
        $(this).addClass('pulse');
    }).on('mouseleave', function() {
        $(this).removeClass('pulse');
    });

    // Smooth checkbox animations
    $('.booking-checkbox').on('change', function() {
        const $row = $(this).closest('tr');
        if ($(this).is(':checked')) {
            $row.addClass('selected-row fade-in');
        } else {
            $row.removeClass('selected-row');
        }
    });
}

/**
 * Initialize table enhancements
 */
function initializeTableEnhancements() {
    // Add hover effects to table rows
    $('.table tbody tr').on('mouseenter', function() {
        $(this).addClass('hover-lift');
    }).on('mouseleave', function() {
        $(this).removeClass('hover-lift');
    });

    // Smooth sorting animations
    $('.table th[data-sort]').on('click', function() {
        $(this).addClass('glow');
        setTimeout(() => {
            $(this).removeClass('glow');
        }, 1000);
    });
}

/**
 * Refresh dashboard stats
 */
function refreshDashboardStats() {
    $.ajax({
        url: base_url + 'api/dashboard-stats',
        method: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                updateDashboardStats(response.data);
            }
        },
        error: function() {
            // Silent error - don't show alert for background refresh
            console.log('Failed to refresh dashboard stats');
        }
    });
}

/**
 * Update dashboard stats display
 */
function updateDashboardStats(data) {
    // Update stat numbers
    $('.stat-number').each(function() {
        const statType = $(this).data('stat');
        if (data[statType] !== undefined) {
            $(this).text(data[statType]);
        }
    });
}