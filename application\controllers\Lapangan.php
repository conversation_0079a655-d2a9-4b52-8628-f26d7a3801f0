<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON><PERSON>gan extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        
        // Check if user is logged in
        if (!$this->session->userdata('logged_in')) {
            redirect('auth/login');
        }
        
        // Check if user is admin
        if ($this->session->userdata('role') !== 'admin') {
            $this->session->set_flashdata('error', 'Anda tidak memiliki akses ke halaman ini.');
            redirect('dashboard');
        }
        
        // Load models and libraries
        $this->load->model('Lapangan_model');
        $this->load->library('form_validation');
    }

    /**
     * Display lapangan list
     */
    public function index()
    {
        $data['title'] = 'Manajemen Lapangan - Padel Booking System';
        $data['page'] = 'lapangan_index';
        
        // Get search parameter
        $search = $this->input->get('search');
        
        // Get lapangan data
        $data['lapangan'] = $this->Lapangan_model->get_all_lapangan($search);
        $data['search'] = $search;
        
        // Load views
        $this->load->view('template/header', $data);
        $this->load->view('lapangan/index', $data);
        $this->load->view('template/footer', $data);
    }

    /**
     * Show create lapangan form
     */
    public function create()
    {
        $data['title'] = 'Tambah Lapangan - Padel Booking System';
        $data['page'] = 'lapangan_create';
        $data['action'] = 'create';
        $data['lapangan'] = null;
        
        // Load views
        $this->load->view('template/header', $data);
        $this->load->view('lapangan/form', $data);
        $this->load->view('template/footer', $data);
    }

    /**
     * Store new lapangan
     */
    public function store()
    {
        // Set validation rules
        $this->_set_validation_rules();
        
        if ($this->form_validation->run() == FALSE) {
            // Validation failed
            $this->create();
        } else {
            // Validation passed
            $data = array(
                'nama_lapangan' => $this->input->post('nama_lapangan'),
                'deskripsi' => $this->input->post('deskripsi'),
                'harga_per_jam' => $this->input->post('harga_per_jam'),
                'status' => $this->input->post('status')
            );
            
            if ($this->Lapangan_model->create_lapangan($data)) {
                $this->session->set_flashdata('success', 'Lapangan berhasil ditambahkan.');
                redirect('lapangan');
            } else {
                $this->session->set_flashdata('error', 'Gagal menambahkan lapangan.');
                $this->create();
            }
        }
    }

    /**
     * Show edit lapangan form
     */
    public function edit($id)
    {
        $lapangan = $this->Lapangan_model->get_lapangan_by_id($id);
        
        if (!$lapangan) {
            $this->session->set_flashdata('error', 'Lapangan tidak ditemukan.');
            redirect('lapangan');
        }
        
        $data['title'] = 'Edit Lapangan - Padel Booking System';
        $data['page'] = 'lapangan_edit';
        $data['action'] = 'edit';
        $data['lapangan'] = $lapangan;
        
        // Load views
        $this->load->view('template/header', $data);
        $this->load->view('lapangan/form', $data);
        $this->load->view('template/footer', $data);
    }

    /**
     * Update lapangan
     */
    public function update($id)
    {
        $lapangan = $this->Lapangan_model->get_lapangan_by_id($id);
        
        if (!$lapangan) {
            $this->session->set_flashdata('error', 'Lapangan tidak ditemukan.');
            redirect('lapangan');
        }
        
        // Set validation rules for update
        $this->_set_validation_rules($id);
        
        if ($this->form_validation->run() == FALSE) {
            // Validation failed
            $this->edit($id);
        } else {
            // Validation passed
            $data = array(
                'nama_lapangan' => $this->input->post('nama_lapangan'),
                'deskripsi' => $this->input->post('deskripsi'),
                'harga_per_jam' => $this->input->post('harga_per_jam'),
                'status' => $this->input->post('status')
            );
            
            if ($this->Lapangan_model->update_lapangan($id, $data)) {
                $this->session->set_flashdata('success', 'Lapangan berhasil diperbarui.');
                redirect('lapangan');
            } else {
                $this->session->set_flashdata('error', 'Gagal memperbarui lapangan.');
                $this->edit($id);
            }
        }
    }

    /**
     * Delete lapangan
     */
    public function delete($id)
    {
        $lapangan = $this->Lapangan_model->get_lapangan_by_id($id);
        
        if (!$lapangan) {
            $this->session->set_flashdata('error', 'Lapangan tidak ditemukan.');
            redirect('lapangan');
        }
        
        // Check if lapangan can be deleted (no bookings)
        if ($this->Lapangan_model->can_be_deleted($id)) {
            if ($this->Lapangan_model->delete_lapangan($id)) {
                $this->session->set_flashdata('success', 'Lapangan berhasil dihapus.');
            } else {
                $this->session->set_flashdata('error', 'Gagal menghapus lapangan.');
            }
        } else {
            $this->session->set_flashdata('error', 'Tidak dapat menghapus lapangan yang memiliki riwayat booking.');
        }
        
        redirect('lapangan');
    }

    /**
     * Toggle lapangan status
     */
    public function toggle_status($id)
    {
        $lapangan = $this->Lapangan_model->get_lapangan_by_id($id);
        
        if (!$lapangan) {
            echo json_encode(array('success' => false, 'message' => 'Lapangan tidak ditemukan.'));
            return;
        }
        
        if ($this->Lapangan_model->toggle_status($id)) {
            $new_status = ($lapangan->status == 'aktif') ? 'tidak_aktif' : 'aktif';
            echo json_encode(array(
                'success' => true, 
                'message' => 'Status lapangan berhasil diubah.',
                'new_status' => $new_status
            ));
        } else {
            echo json_encode(array('success' => false, 'message' => 'Gagal mengubah status lapangan.'));
        }
    }

    /**
     * View lapangan detail
     */
    public function detail($id)
    {
        $lapangan = $this->Lapangan_model->get_lapangan_by_id($id);
        
        if (!$lapangan) {
            $this->session->set_flashdata('error', 'Lapangan tidak ditemukan.');
            redirect('lapangan');
        }
        
        $data['title'] = 'Detail Lapangan - ' . $lapangan->nama_lapangan;
        $data['page'] = 'lapangan_detail';
        $data['lapangan'] = $lapangan;
        
        // Get utilization rate and other stats
        $data['utilization_rate'] = $this->Lapangan_model->get_utilization_rate($id);
        $data['peak_hours'] = $this->Lapangan_model->get_peak_hours($id);
        
        // Get booking history
        $data['booking_history'] = $this->Lapangan_model->get_booking_history($id, 10);
        $data['total_history'] = $this->Lapangan_model->count_booking_history($id);
        
        // Load views
        $this->load->view('template/header', $data);
        $this->load->view('lapangan/detail', $data);
        $this->load->view('template/footer', $data);
    }

    /**
     * AJAX: Get lapangan harga
     */
    public function get_harga($id)
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $lapangan = $this->Lapangan_model->get_lapangan_by_id($id);
        
        if ($lapangan) {
            echo json_encode(array(
                'success' => true,
                'data' => array(
                    'id' => $lapangan->id,
                    'nama_lapangan' => $lapangan->nama_lapangan,
                    'harga_per_jam' => $lapangan->harga_per_jam
                )
            ));
        } else {
            echo json_encode(array('success' => false, 'message' => 'Lapangan tidak ditemukan.'));
        }
    }

    /**
     * AJAX: Check if lapangan name exists
     */
    public function check_name()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $nama_lapangan = $this->input->post('nama_lapangan');
        $exclude_id = $this->input->post('exclude_id');
        
        $exists = $this->Lapangan_model->name_exists($nama_lapangan, $exclude_id);
        
        echo json_encode(array('exists' => $exists));
    }

    /**
     * Get statistics
     */
    public function statistics()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $stats = array();
        
        // Total lapangan
        $stats['total'] = $this->Lapangan_model->count_all_courts();
        
        // Active lapangan
        $stats['active'] = $this->Lapangan_model->count_active_courts();
        
        // Inactive lapangan
        $stats['inactive'] = $stats['total'] - $stats['active'];
        
        // Price range
        $price_range = $this->Lapangan_model->get_price_range();
        if ($price_range) {
            $stats['min_price'] = $price_range->min_price;
            $stats['max_price'] = $price_range->max_price;
            $stats['avg_price'] = $price_range->avg_price;
        } else {
            $stats['min_price'] = 0;
            $stats['max_price'] = 0;
            $stats['avg_price'] = 0;
        }
        
        echo json_encode(array(
            'success' => true,
            'data' => $stats
        ));
    }

    /**
     * Export lapangan to CSV
     */
    public function export_csv()
    {
        $lapangan = $this->Lapangan_model->get_all_lapangan();
        
        $filename = 'lapangan_export_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // CSV headers
        fputcsv($output, array('ID', 'Nama Lapangan', 'Deskripsi', 'Harga per Jam', 'Status', 'Dibuat', 'Diperbarui'));
        
        // CSV data
        foreach ($lapangan as $lap) {
            fputcsv($output, array(
                $lap->id,
                $lap->nama_lapangan,
                $lap->deskripsi,
                $lap->harga_per_jam,
                ucfirst($lap->status),
                date('d/m/Y H:i', strtotime($lap->created_at)),
                date('d/m/Y H:i', strtotime($lap->updated_at))
            ));
        }
        
        fclose($output);
    }

    /**
     * Set validation rules
     */
    private function _set_validation_rules($lapangan_id = null)
    {
        // Nama lapangan validation
        if ($lapangan_id) {
            $this->form_validation->set_rules('nama_lapangan', 'Nama Lapangan', 'required|trim|callback__check_name_unique[' . $lapangan_id . ']');
        } else {
            $this->form_validation->set_rules('nama_lapangan', 'Nama Lapangan', 'required|trim|callback__check_name_unique');
        }
        
        // Other fields
        $this->form_validation->set_rules('deskripsi', 'Deskripsi', 'trim');
        $this->form_validation->set_rules('harga_per_jam', 'Harga per Jam', 'required|numeric|greater_than[0]');
        $this->form_validation->set_rules('status', 'Status', 'required|in_list[aktif,tidak_aktif]');
        
        // Custom validation messages
        $this->form_validation->set_message('required', '{field} wajib diisi.');
        $this->form_validation->set_message('numeric', '{field} harus berupa angka.');
        $this->form_validation->set_message('greater_than', '{field} harus lebih besar dari {param}.');
        $this->form_validation->set_message('in_list', '{field} tidak valid.');
    }

    /**
     * Custom validation callback for unique lapangan name
     */
    public function _check_name_unique($nama_lapangan, $lapangan_id = null)
    {
        if ($this->Lapangan_model->name_exists($nama_lapangan, $lapangan_id)) {
            $this->form_validation->set_message('_check_name_unique', 'Nama lapangan sudah digunakan.');
            return FALSE;
        }
        return TRUE;
    }
}