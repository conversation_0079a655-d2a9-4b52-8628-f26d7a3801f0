<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Auth extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        $this->load->model('User_model');
        $this->load->library('form_validation');
    }

    /**
     * <PERSON><PERSON> login
     */
    public function index()
    {
        // Jika sudah login, redirect ke dashboard
        if ($this->session->userdata('logged_in')) {
            redirect('dashboard');
        }
        
        $this->login();
    }

    /**
     * Tampilkan form login
     */
    public function login()
    {
        // Jika sudah login, redirect ke dashboard
        if ($this->session->userdata('logged_in')) {
            redirect('dashboard');
        }

        $data['title'] = 'Login - Padel Booking System';
        $data['page'] = 'login';
        
        $this->load->view('auth/login', $data);
    }

    /**
     * Proses login
     */
    public function process_login()
    {
        // Set validation rules
        $this->form_validation->set_rules('username', 'Username', 'required|trim');
        $this->form_validation->set_rules('password', 'Password', 'required');

        if ($this->form_validation->run() == FALSE) {
            // Validation failed
            $data['title'] = 'Login - Padel Booking System';
            $data['page'] = 'login';
            $this->load->view('auth/login', $data);
        } else {
            // Validation passed
            $username = $this->input->post('username');
            $password = $this->input->post('password');

            // Verify credentials
            $user = $this->User_model->verify_login($username, $password);

            if ($user) {
                // Check if user is active
                if ($user->status != 'aktif') {
                    $this->session->set_flashdata('error', 'Akun Anda tidak aktif. Silakan hubungi administrator.');
                    redirect('auth/login');
                }

                // Login successful
                $user_data = array(
                    'user_id' => $user->id,
                    'username' => $user->username,
                    'full_name' => $user->full_name,
                    'email' => $user->email,
                    'role' => $user->role,
                    'logged_in' => TRUE
                );

                $this->session->set_userdata($user_data);
                
                // Log login activity
                $this->User_model->log_login($user->id);

                // Set success message
                $this->session->set_flashdata('success', 'Selamat datang, ' . $user->full_name . '!');

                // Redirect based on role
                $this->_redirect_by_role($user->role);
            } else {
                // Login failed
                $this->session->set_flashdata('error', 'Username atau password salah!');
                redirect('auth/login');
            }
        }
    }

    /**
     * Logout
     */
    public function logout()
    {
        // Log logout activity
        if ($this->session->userdata('user_id')) {
            $this->User_model->log_logout($this->session->userdata('user_id'));
        }

        // Destroy session
        $this->session->sess_destroy();
        
        // Set message
        $this->session->set_flashdata('success', 'Anda berhasil logout.');
        
        // Redirect to login
        redirect('auth/login');
    }

    /**
     * Redirect user based on role
     */
    private function _redirect_by_role($role)
    {
        switch ($role) {
            case 'admin':
                redirect('dashboard');
                break;
            case 'kasir':
                redirect('dashboard');
                break;
            case 'pimpinan':
                redirect('dashboard');
                break;
            default:
                redirect('auth/login');
                break;
        }
    }

    /**
     * Check if user is logged in (untuk AJAX)
     */
    public function check_login()
    {
        if ($this->session->userdata('logged_in')) {
            echo json_encode([
                'status' => 'success',
                'logged_in' => true,
                'user' => [
                    'id' => $this->session->userdata('user_id'),
                    'username' => $this->session->userdata('username'),
                    'full_name' => $this->session->userdata('full_name'),
                    'role' => $this->session->userdata('role')
                ]
            ]);
        } else {
            echo json_encode([
                'status' => 'error',
                'logged_in' => false,
                'message' => 'User not logged in'
            ]);
        }
    }

    /**
     * Forgot Password (Optional - untuk pengembangan selanjutnya)
     */
    public function forgot_password()
    {
        $data['title'] = 'Lupa Password - Padel Booking System';
        $data['page'] = 'forgot_password';
        
        $this->load->view('auth/forgot_password', $data);
    }
}