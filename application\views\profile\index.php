<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h1><i class="fas fa-user-circle"></i> Profil Saya</h1>
            <p class="text-muted">Kelola informasi akun dan pengaturan profil Anda</p>
        </div>
        <div class="col-auto">
            <a href="<?php echo site_url('profile/edit'); ?>" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit Profil
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- Profile Card -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-body text-center">
                <!-- Avatar -->
                <div class="profile-avatar mb-3">
                    <?php if ($user->avatar && file_exists('./uploads/avatars/' . $user->avatar)): ?>
                        <img src="<?php echo base_url('uploads/avatars/' . $user->avatar); ?>" 
                             class="rounded-circle" width="120" height="120" alt="Avatar">
                    <?php else: ?>
                        <div class="avatar-placeholder">
                            <i class="fas fa-user-circle fa-7x text-muted"></i>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Upload Avatar -->
                <div class="mb-3">
                    <?php if ($user->avatar): ?>
                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                onclick="deleteAvatar()" title="Hapus Avatar">
                            <i class="fas fa-trash"></i>
                        </button>
                    <?php endif; ?>
                    <button type="button" class="btn btn-sm btn-outline-primary" 
                            onclick="document.getElementById('avatar-upload').click()">
                        <i class="fas fa-camera"></i> Upload Avatar
                    </button>
                </div>
                
                <!-- Hidden file input -->
                <?php echo form_open_multipart('profile/upload_avatar', array('id' => 'avatar-form', 'style' => 'display:none')); ?>
                    <input type="file" id="avatar-upload" name="avatar" accept="image/*" onchange="uploadAvatar()">
                <?php echo form_close(); ?>
                
                <!-- User Info -->
                <h4 class="mb-1"><?php echo $user->full_name; ?></h4>
                <p class="text-muted mb-2">@<?php echo $user->username; ?></p>
                
                <!-- Role Badge -->
                <?php
                $role_class = array(
                    'admin' => 'bg-danger',
                    'kasir' => 'bg-info', 
                    'pimpinan' => 'bg-warning'
                );
                ?>
                <span class="badge <?php echo $role_class[$user->role]; ?> mb-3">
                    <i class="fas fa-<?php echo ($user->role == 'admin') ? 'crown' : (($user->role == 'kasir') ? 'cash-register' : 'chart-line'); ?>"></i>
                    <?php echo ucfirst($user->role); ?>
                </span>
                
                <!-- Status -->
                <div class="mb-3">
                    <span class="badge bg-<?php echo ($user->status == 'aktif') ? 'success' : 'secondary'; ?>">
                        <i class="fas fa-<?php echo ($user->status == 'aktif') ? 'check-circle' : 'pause-circle'; ?>"></i>
                        <?php echo ucfirst($user->status); ?>
                    </span>
                </div>
                
                <!-- Member Since -->
                <small class="text-muted">
                    <i class="fas fa-calendar"></i>
                    Bergabung <?php echo date('d F Y', strtotime($user->created_at)); ?>
                </small>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt"></i> Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?php echo site_url('profile/edit'); ?>" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-edit"></i> Edit Profil
                    </a>
                    <a href="<?php echo site_url('profile/change_password'); ?>" class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-key"></i> Ubah Password
                    </a>
                    <a href="<?php echo site_url('profile/security_settings'); ?>" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-shield-alt"></i> Keamanan
                    </a>
                    <a href="<?php echo site_url('profile/activity_log'); ?>" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-history"></i> Log Aktivitas
                    </a>
                    <hr>
                    <a href="<?php echo site_url('profile/export_data'); ?>" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-download"></i> Export Data
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Profile Details -->
    <div class="col-lg-8">
        <!-- Personal Information -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> Informasi Personal
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold" width="40%">Username:</td>
                                <td><?php echo $user->username; ?></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Nama Lengkap:</td>
                                <td><?php echo $user->full_name; ?></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Email:</td>
                                <td>
                                    <?php if ($user->email): ?>
                                        <a href="mailto:<?php echo $user->email; ?>" class="text-decoration-none">
                                            <?php echo $user->email; ?>
                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">Belum diisi</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Telepon:</td>
                                <td>
                                    <?php if ($user->phone): ?>
                                        <a href="tel:<?php echo $user->phone; ?>" class="text-decoration-none">
                                            <?php echo $user->phone; ?>
                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">Belum diisi</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold" width="40%">Role:</td>
                                <td>
                                    <span class="badge <?php echo $role_class[$user->role]; ?>">
                                        <?php echo ucfirst($user->role); ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Status:</td>
                                <td>
                                    <span class="badge bg-<?php echo ($user->status == 'aktif') ? 'success' : 'secondary'; ?>">
                                        <?php echo ucfirst($user->status); ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Bergabung:</td>
                                <td><?php echo date('d F Y', strtotime($user->created_at)); ?></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Terakhir Update:</td>
                                <td><?php echo date('d F Y H:i', strtotime($user->updated_at)); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Account Statistics -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar"></i> Statistik Akun
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <?php if ($user->role == 'admin'): ?>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <h3 class="text-primary">
                                    <?php 
                                    echo $this->Booking_model->count_bookings();
                                    ?>
                                </h3>
                                <p class="text-muted mb-0">Total Booking</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <h3 class="text-success">
                                    <?php echo $this->Booking_model->count_bookings(null, null, 'selesai'); ?>
                                </h3>
                                <p class="text-muted mb-0">Booking Selesai</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <h3 class="text-warning">
                                    <?php echo $this->Booking_model->count_bookings(null, null, 'pending'); ?>
                                </h3>
                                <p class="text-muted mb-0">Booking Pending</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <h3 class="text-info">
                                    <?php 
                                    $this->load->model('User_model');
                                    echo $this->User_model->count_users();
                                    ?>
                                </h3>
                                <p class="text-muted mb-0">Total User</p>
                            </div>
                        </div>
                    <?php elseif ($user->role == 'kasir'): ?>
                        <div class="col-md-4">
                            <div class="stat-card">
                                <h3 class="text-primary">
                                    <?php 
                                    $this->load->model('Booking_model');
                                    echo $this->Booking_model->count_bookings(date('Y-m-d'), date('Y-m-d'));
                                    ?>
                                </h3>
                                <p class="text-muted mb-0">Booking Hari Ini</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stat-card">
                                <h3 class="text-success">
                                    <?php 
                                    $this->db->where('kasir_id', $user->id);
                                    echo $this->db->count_all_results('transaksi');
                                    ?>
                                </h3>
                                <p class="text-muted mb-0">Transaksi Saya</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stat-card">
                                <h3 class="text-info">
                                    <?php echo $this->Booking_model->count_bookings(null, null, 'dikonfirmasi'); ?>
                                </h3>
                                <p class="text-muted mb-0">Siap Bayar</p>
                            </div>
                        </div>
                    <?php else: // pimpinan ?>
                        <div class="col-md-4">
                            <div class="stat-card">
                                <h3 class="text-primary">
                                    <?php 
                                    $this->load->model('Booking_model');
                                    echo $this->Booking_model->count_bookings(date('Y-m-01'), date('Y-m-d'));
                                    ?>
                                </h3>
                                <p class="text-muted mb-0">Booking Bulan Ini</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stat-card">
                                <h3 class="text-success">
                                    <?php 
                                    $stats = $this->Booking_model->get_booking_stats(date('Y-m-01'), date('Y-m-d'));
                                    echo 'Rp ' . number_format($stats['total_revenue'], 0, ',', '.');
                                    ?>
                                </h3>
                                <p class="text-muted mb-0">Revenue Bulan Ini</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stat-card">
                                <h3 class="text-info">
                                    <?php echo $this->Booking_model->count_bookings(date('Y-m-01'), date('Y-m-d'), 'selesai'); ?>
                                </h3>
                                <p class="text-muted mb-0">Booking Selesai</p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Recent Activity -->
        <div class="card mt-4">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-history"></i> Aktivitas Terakhir
                        </h5>
                    </div>
                    <div class="col-auto">
                        <a href="<?php echo site_url('profile/activity_log'); ?>" class="btn btn-sm btn-outline-primary">
                            Lihat Semua
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <?php 
                $this->load->model('User_model');
                $recent_logs = $this->User_model->get_user_logs($user->id, 5);
                ?>
                
                <?php if (empty($recent_logs)): ?>
                    <div class="text-center py-3">
                        <i class="fas fa-history fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">Belum ada aktivitas</p>
                    </div>
                <?php else: ?>
                    <div class="activity-timeline">
                        <?php foreach ($recent_logs as $log): ?>
                            <div class="activity-item">
                                <div class="activity-icon">
                                    <?php
                                    $icon = 'fas fa-circle';
                                    $color = 'text-muted';
                                    
                                    switch ($log->activity) {
                                        case 'login':
                                            $icon = 'fas fa-sign-in-alt';
                                            $color = 'text-success';
                                            break;
                                        case 'logout':
                                            $icon = 'fas fa-sign-out-alt';
                                            $color = 'text-warning';
                                            break;
                                        case 'create':
                                            $icon = 'fas fa-plus';
                                            $color = 'text-primary';
                                            break;
                                        case 'update':
                                            $icon = 'fas fa-edit';
                                            $color = 'text-info';
                                            break;
                                        case 'delete':
                                            $icon = 'fas fa-trash';
                                            $color = 'text-danger';
                                            break;
                                    }
                                    ?>
                                    <i class="<?php echo $icon; ?> <?php echo $color; ?>"></i>
                                </div>
                                <div class="activity-content">
                                    <strong><?php echo ucfirst(str_replace('_', ' ', $log->activity)); ?></strong>
                                    <br>
                                    <small class="text-muted">
                                        <i class="fas fa-clock"></i> 
                                        <?php echo date('d M Y H:i', strtotime($log->created_at)); ?>
                                    </small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
.profile-avatar {
    position: relative;
    display: inline-block;
}

.avatar-placeholder {
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 50%;
    margin: 0 auto;
}

.stat-card {
    padding: 20px;
    border-radius: 8px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    margin-bottom: 20px;
}

.stat-card h3 {
    font-weight: 700;
    margin-bottom: 8px;
}

.activity-timeline {
    position: relative;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
}

.activity-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.activity-content {
    flex-grow: 1;
    padding-top: 8px;
}
</style>

<script>
function uploadAvatar() {
    if (confirm('Upload avatar baru?')) {
        document.getElementById('avatar-form').submit();
    }
}

function deleteAvatar() {
    if (confirm('Yakin ingin menghapus avatar?')) {
        window.location.href = '<?php echo site_url("profile/delete_avatar"); ?>';
    }
}
</script>