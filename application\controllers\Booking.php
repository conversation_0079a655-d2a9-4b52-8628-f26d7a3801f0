<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Booking extends CI_Controller {

    public function __construct() {
        parent::__construct();
        
        // Check if user is logged in
        if (!$this->session->userdata('user_id')) {
            redirect('auth/login');
        }
        
        // Load required models and libraries
        $this->load->model('Booking_model');
        $this->load->model('Lapangan_model');
        $this->load->model('Member_model');
        $this->load->library('form_validation');
        
        // Check role access - admin dan kasir bisa akses semua fitur booking
        $allowed_roles = array('admin', 'kasir');
        if (!in_array($this->session->userdata('role'), $allowed_roles)) {
            show_error('Access denied. You do not have permission to access this page.', 403, 'Access Denied');
        }
    }

    public function index() {
        $data['title'] = 'Daftar Booking';
        
        // Get filter parameters
        $filter_tanggal = $this->input->get('tanggal');
        $filter_status = $this->input->get('status');
        $filter_lapangan = $this->input->get('lapangan');
        $filter_customer_type = $this->input->get('customer_type');
        
        // Get booking list with filters
        $bookings = $this->Booking_model->get_bookings_with_filters($filter_tanggal, $filter_status, $filter_lapangan, $filter_customer_type);
        
        // Get lapangan list for filter dropdown
        $lapangan_list = $this->Lapangan_model->get_active_lapangan();
        
        $data['bookings'] = $bookings;
        $data['lapangan_list'] = $lapangan_list;
        $data['filter_tanggal'] = $filter_tanggal;
        $data['filter_status'] = $filter_status;
        $data['filter_lapangan'] = $filter_lapangan;
        $data['filter_customer_type'] = $filter_customer_type;
        
        $this->load->view('template/header', $data);
        $this->load->view('booking/index', $data);
        $this->load->view('template/footer');
    }

    public function create() {
        // Admin dan kasir bisa create booking
        if (!in_array($this->session->userdata('role'), ['admin', 'kasir'])) {
            show_error('Access denied. Only admin and kasir can create booking.', 403, 'Access Denied');
        }
        
        $data['title'] = 'Buat Booking Baru';
        
        if ($this->input->post()) {
            $this->_process_create_booking();
        } else {
            // Get active lapangan for dropdown
            $data['lapangan_list'] = $this->Lapangan_model->get_active_lapangan();
            
            $this->load->view('template/header', $data);
            $this->load->view('booking/create', $data);
            $this->load->view('template/footer');
        }
    }

    private function _process_create_booking() {
        // Set validation rules
        $this->form_validation->set_rules('customer_type', 'Tipe Customer', 'required|in_list[member,non_member]');
        $this->form_validation->set_rules('lapangan_id', 'Lapangan', 'required|numeric');
        $this->form_validation->set_rules('tanggal_booking', 'Tanggal Booking', 'required');
        $this->form_validation->set_rules('jam_mulai', 'Jam Mulai', 'required');
        $this->form_validation->set_rules('jam_selesai', 'Jam Selesai', 'required');
        
        $customer_type = $this->input->post('customer_type');
        
        if ($customer_type == 'member') {
            $this->form_validation->set_rules('member_code', 'Member Code', 'required');
        } else {
            $this->form_validation->set_rules('nama_pemesan', 'Nama Pemesan', 'required|max_length[100]');
            $this->form_validation->set_rules('telepon_pemesan', 'Telepon Pemesan', 'required|max_length[20]');
        }

        if ($this->form_validation->run() === FALSE) {
            $data['title'] = 'Buat Booking Baru';
            $data['lapangan_list'] = $this->Lapangan_model->get_active_lapangan();
            
            $this->load->view('template/header', $data);
            $this->load->view('booking/create', $data);
            $this->load->view('template/footer');
            return;
        }

        // Get form data
        $lapangan_id = $this->input->post('lapangan_id');
        $tanggal_booking = $this->input->post('tanggal_booking');
        $jam_mulai = $this->input->post('jam_mulai');
        $jam_selesai = $this->input->post('jam_selesai');
        
        // Validate time
        if ($jam_mulai >= $jam_selesai) {
            $this->session->set_flashdata('error', 'Jam selesai harus lebih besar dari jam mulai');
            redirect('booking/create');
            return;
        }
        
        // Check if date is not in the past
        if ($tanggal_booking < date('Y-m-d')) {
            $this->session->set_flashdata('error', 'Tanggal booking tidak boleh di masa lalu');
            redirect('booking/create');
            return;
        }
        
        // Check schedule conflict
        if ($this->Booking_model->check_schedule_conflict($lapangan_id, $tanggal_booking, $jam_mulai, $jam_selesai)) {
            $this->session->set_flashdata('error', 'Jadwal bertabrakan dengan booking lain. Silakan pilih waktu yang berbeda.');
            redirect('booking/create');
            return;
        }
        
        // Get lapangan info
        $lapangan = $this->Lapangan_model->get_lapangan_by_id($lapangan_id);
        if (!$lapangan) {
            $this->session->set_flashdata('error', 'Lapangan tidak ditemukan');
            redirect('booking/create');
            return;
        }
        
        // Calculate duration and price
        $jam_mulai_timestamp = strtotime($jam_mulai);
        $jam_selesai_timestamp = strtotime($jam_selesai);
        $durasi_detik = $jam_selesai_timestamp - $jam_mulai_timestamp;
        $durasi_jam = $durasi_detik / 3600; // Convert to hours
        
        $harga_per_jam = $lapangan->harga_per_jam;
        $price_before_discount = $durasi_jam * $harga_per_jam;
        
        // Prepare booking data
        $booking_data = array(
            'kode_booking' => $this->_generate_booking_code(),
            'lapangan_id' => $lapangan_id,
            'tanggal_booking' => $tanggal_booking,
            'jam_mulai' => $jam_mulai,
            'jam_selesai' => $jam_selesai,
            'durasi_jam' => $durasi_jam,
            'price_before_discount' => $price_before_discount,
            'customer_type' => $customer_type,
            'created_by' => $this->session->userdata('user_id'),
            'status_booking' => 'pending'
        );
        
        if ($customer_type == 'member') {
            $member_code = $this->input->post('member_code');
            
            // Validate member
            $member = $this->Member_model->get_member_by_code($member_code);
            if (!$member) {
                $this->session->set_flashdata('error', 'Member code tidak ditemukan');
                redirect('booking/create');
                return;
            }
            
            if ($member->status != 'aktif') {
                $this->session->set_flashdata('error', 'Member tidak aktif');
                redirect('booking/create');
                return;
            }
            
            // Calculate discount
            $discount_percent = $member->discount_percent;
            $discount_amount = ($price_before_discount * $discount_percent) / 100;
            $harga_total = $price_before_discount - $discount_amount;
            
            $booking_data['member_code'] = $member_code;
            $booking_data['nama_pemesan'] = $member->full_name;
            $booking_data['telepon_pemesan'] = $member->phone;
            $booking_data['discount_percent'] = $discount_percent;
            $booking_data['harga_total'] = $harga_total;
            
        } else {
            // Non-member booking
            $booking_data['nama_pemesan'] = $this->input->post('nama_pemesan');
            $booking_data['telepon_pemesan'] = $this->input->post('telepon_pemesan');
            $booking_data['discount_percent'] = 0;
            $booking_data['harga_total'] = $price_before_discount;
        }
        
        // Insert booking
        $booking_id = $this->Booking_model->insert_booking($booking_data);
        
        if ($booking_id) {
            $this->session->set_flashdata('success', 'Booking berhasil dibuat dengan kode: ' . $booking_data['kode_booking']);
            redirect('booking');
        } else {
            $this->session->set_flashdata('error', 'Gagal membuat booking. Silakan coba lagi.');
            redirect('booking/create');
        }
    }

    public function view($id) {
        $booking = $this->Booking_model->get_booking_detail($id);
        
        if (!$booking) {
            show_404();
        }
        
        $data['title'] = 'Detail Booking';
        $data['booking'] = $booking;
        
        $this->load->view('template/header', $data);
        $this->load->view('booking/view', $data);
        $this->load->view('template/footer');
    }

    public function edit($id) {
        // Admin dan kasir bisa edit booking
        if (!in_array($this->session->userdata('role'), ['admin', 'kasir'])) {
            show_error('Access denied. Only admin and kasir can edit booking.', 403, 'Access Denied');
        }
        
        $booking = $this->Booking_model->get_booking_by_id($id);
        
        if (!$booking) {
            show_404();
        }
        
        // Can only edit pending bookings
        if ($booking->status_booking != 'pending') {
            $this->session->set_flashdata('error', 'Hanya booking dengan status pending yang dapat diedit');
            redirect('booking');
        }
        
        $data['title'] = 'Edit Booking';
        
        if ($this->input->post()) {
            $this->_process_edit_booking($id);
        } else {
            $data['booking'] = $booking;
            $data['lapangan_list'] = $this->Lapangan_model->get_active_lapangan();
            
            $this->load->view('template/header', $data);
            $this->load->view('booking/edit', $data);
            $this->load->view('template/footer');
        }
    }

    private function _process_edit_booking($id) {
        // Set validation rules
        $this->form_validation->set_rules('customer_type', 'Tipe Customer', 'required|in_list[member,non_member]');
        $this->form_validation->set_rules('lapangan_id', 'Lapangan', 'required|numeric');
        $this->form_validation->set_rules('tanggal_booking', 'Tanggal Booking', 'required');
        $this->form_validation->set_rules('jam_mulai', 'Jam Mulai', 'required');
        $this->form_validation->set_rules('jam_selesai', 'Jam Selesai', 'required');
        
        $customer_type = $this->input->post('customer_type');
        
        if ($customer_type == 'member') {
            $this->form_validation->set_rules('member_code', 'Member Code', 'required');
        } else {
            $this->form_validation->set_rules('nama_pemesan', 'Nama Pemesan', 'required|max_length[100]');
            $this->form_validation->set_rules('telepon_pemesan', 'Telepon Pemesan', 'required|max_length[20]');
        }

        if ($this->form_validation->run() === FALSE) {
            $data['title'] = 'Edit Booking';
            $data['booking'] = $this->Booking_model->get_booking_by_id($id);
            $data['lapangan_list'] = $this->Lapangan_model->get_active_lapangan();
            
            $this->load->view('template/header', $data);
            $this->load->view('booking/edit', $data);
            $this->load->view('template/footer');
            return;
        }

        // Get form data
        $lapangan_id = $this->input->post('lapangan_id');
        $tanggal_booking = $this->input->post('tanggal_booking');
        $jam_mulai = $this->input->post('jam_mulai');
        $jam_selesai = $this->input->post('jam_selesai');
        
        // Validate time
        if ($jam_mulai >= $jam_selesai) {
            $this->session->set_flashdata('error', 'Jam selesai harus lebih besar dari jam mulai');
            redirect('booking/edit/' . $id);
            return;
        }
        
        // Check schedule conflict (exclude current booking)
        if ($this->Booking_model->check_schedule_conflict($lapangan_id, $tanggal_booking, $jam_mulai, $jam_selesai, $id)) {
            $this->session->set_flashdata('error', 'Jadwal bertabrakan dengan booking lain. Silakan pilih waktu yang berbeda.');
            redirect('booking/edit/' . $id);
            return;
        }
        
        // Get lapangan info
        $lapangan = $this->Lapangan_model->get_lapangan_by_id($lapangan_id);
        if (!$lapangan) {
            $this->session->set_flashdata('error', 'Lapangan tidak ditemukan');
            redirect('booking/edit/' . $id);
            return;
        }
        
        // Calculate duration and price
        $jam_mulai_timestamp = strtotime($jam_mulai);
        $jam_selesai_timestamp = strtotime($jam_selesai);
        $durasi_detik = $jam_selesai_timestamp - $jam_mulai_timestamp;
        $durasi_jam = $durasi_detik / 3600;
        
        $harga_per_jam = $lapangan->harga_per_jam;
        $price_before_discount = $durasi_jam * $harga_per_jam;
        
        // Prepare booking data
        $booking_data = array(
            'lapangan_id' => $lapangan_id,
            'tanggal_booking' => $tanggal_booking,
            'jam_mulai' => $jam_mulai,
            'jam_selesai' => $jam_selesai,
            'durasi_jam' => $durasi_jam,
            'price_before_discount' => $price_before_discount,
            'customer_type' => $customer_type
        );
        
        if ($customer_type == 'member') {
            $member_code = $this->input->post('member_code');
            
            // Validate member
            $member = $this->Member_model->get_member_by_code($member_code);
            if (!$member) {
                $this->session->set_flashdata('error', 'Member code tidak ditemukan');
                redirect('booking/edit/' . $id);
                return;
            }
            
            if ($member->status != 'aktif') {
                $this->session->set_flashdata('error', 'Member tidak aktif');
                redirect('booking/edit/' . $id);
                return;
            }
            
            // Calculate discount
            $discount_percent = $member->discount_percent;
            $discount_amount = ($price_before_discount * $discount_percent) / 100;
            $harga_total = $price_before_discount - $discount_amount;
            
            $booking_data['member_code'] = $member_code;
            $booking_data['nama_pemesan'] = $member->full_name;
            $booking_data['telepon_pemesan'] = $member->phone;
            $booking_data['discount_percent'] = $discount_percent;
            $booking_data['harga_total'] = $harga_total;
            
        } else {
            // Non-member booking
            $booking_data['nama_pemesan'] = $this->input->post('nama_pemesan');
            $booking_data['telepon_pemesan'] = $this->input->post('telepon_pemesan');
            $booking_data['member_code'] = null;
            $booking_data['discount_percent'] = 0;
            $booking_data['harga_total'] = $price_before_discount;
        }
        
        // Update booking
        if ($this->Booking_model->update_booking($id, $booking_data)) {
            $this->session->set_flashdata('success', 'Booking berhasil diupdate');
            redirect('booking');
        } else {
            $this->session->set_flashdata('error', 'Gagal mengupdate booking. Silakan coba lagi.');
            redirect('booking/edit/' . $id);
        }
    }

    public function update_status($id) {
        // Admin dan kasir bisa update status
        if (!in_array($this->session->userdata('role'), ['admin', 'kasir'])) {
            show_error('Access denied. Only admin and kasir can update booking status.', 403, 'Access Denied');
        }
        
        $booking = $this->Booking_model->get_booking_by_id($id);
        
        if (!$booking) {
            show_404();
        }
        
        $status = $this->input->post('status');
        $allowed_status = array('pending', 'dikonfirmasi', 'selesai', 'dibatalkan');
        
        if (!in_array($status, $allowed_status)) {
            $this->session->set_flashdata('error', 'Status tidak valid');
            redirect('booking');
            return;
        }
        
        if ($this->Booking_model->update_status($id, $status)) {
            $this->session->set_flashdata('success', 'Status booking berhasil diupdate');
        } else {
            $this->session->set_flashdata('error', 'Gagal mengupdate status booking');
        }
        
        redirect('booking');
    }

    public function delete($id) {
        // Admin dan kasir bisa delete booking
        if (!in_array($this->session->userdata('role'), ['admin', 'kasir'])) {
            show_error('Access denied. Only admin and kasir can delete booking.', 403, 'Access Denied');
        }

        $booking = $this->Booking_model->get_booking_by_id($id);

        if (!$booking) {
            show_404();
        }

        // Can only delete pending bookings
        if ($booking->status_booking != 'pending') {
            $this->session->set_flashdata('error', 'Hanya booking dengan status pending yang dapat dihapus');
            redirect('booking');
            return;
        }

        if ($this->Booking_model->delete_booking($id)) {
            $this->session->set_flashdata('success', 'Booking berhasil dihapus');
        } else {
            $this->session->set_flashdata('error', 'Gagal menghapus booking');
        }

        redirect('booking');
    }

    public function bulk_update_status() {
        // Admin dan kasir bisa bulk update status
        if (!in_array($this->session->userdata('role'), ['admin', 'kasir'])) {
            show_error('Access denied. Only admin and kasir can update booking status.', 403, 'Access Denied');
        }

        $booking_ids = $this->input->post('booking_ids');
        $status = $this->input->post('status');

        if (empty($booking_ids) || !is_array($booking_ids)) {
            $this->session->set_flashdata('error', 'Pilih booking yang ingin diupdate');
            redirect('booking');
            return;
        }

        $allowed_status = array('pending', 'dikonfirmasi', 'selesai', 'dibatalkan');

        if (!in_array($status, $allowed_status)) {
            $this->session->set_flashdata('error', 'Status tidak valid');
            redirect('booking');
            return;
        }

        $success_count = 0;
        $error_count = 0;

        foreach ($booking_ids as $booking_id) {
            // Validate booking exists
            $booking = $this->Booking_model->get_booking_by_id($booking_id);
            if (!$booking) {
                $error_count++;
                continue;
            }

            // Update status
            if ($this->Booking_model->update_status($booking_id, $status)) {
                $success_count++;
            } else {
                $error_count++;
            }
        }

        if ($success_count > 0) {
            $this->session->set_flashdata('success', "Berhasil mengupdate status {$success_count} booking");
        }

        if ($error_count > 0) {
            $this->session->set_flashdata('error', "Gagal mengupdate {$error_count} booking");
        }

        redirect('booking');
    }

    // AJAX Methods
    public function get_member_info() {
        $member_code = $this->input->post('member_code');
        
        if (!$member_code) {
            echo json_encode(array('success' => false, 'message' => 'Member code is required'));
            return;
        }
        
        $member = $this->Member_model->get_member_by_code($member_code);
        
        if ($member && $member->status == 'aktif') {
            echo json_encode(array(
                'success' => true,
                'data' => array(
                    'full_name' => $member->full_name,
                    'phone' => $member->phone,
                    'email' => $member->email,
                    'discount_percent' => $member->discount_percent
                )
            ));
        } else {
            echo json_encode(array('success' => false, 'message' => 'Member not found or inactive'));
        }
    }

    public function calculate_price() {
        $lapangan_id = $this->input->post('lapangan_id');
        $jam_mulai = $this->input->post('jam_mulai');
        $jam_selesai = $this->input->post('jam_selesai');
        $discount_percent = $this->input->post('discount_percent') ?: 0;
        
        if (!$lapangan_id || !$jam_mulai || !$jam_selesai) {
            echo json_encode(array('success' => false, 'message' => 'Missing required parameters'));
            return;
        }
        
        // Validate time
        if ($jam_mulai >= $jam_selesai) {
            echo json_encode(array('success' => false, 'message' => 'Jam selesai harus lebih besar dari jam mulai'));
            return;
        }
        
        // Get lapangan info
        $lapangan = $this->Lapangan_model->get_lapangan_by_id($lapangan_id);
        if (!$lapangan) {
            echo json_encode(array('success' => false, 'message' => 'Lapangan tidak ditemukan'));
            return;
        }
        
        // Calculate duration and price
        $jam_mulai_timestamp = strtotime($jam_mulai);
        $jam_selesai_timestamp = strtotime($jam_selesai);
        $durasi_detik = $jam_selesai_timestamp - $jam_mulai_timestamp;
        $durasi_jam = $durasi_detik / 3600;
        
        $harga_per_jam = $lapangan->harga_per_jam;
        $price_before_discount = $durasi_jam * $harga_per_jam;
        $discount_amount = ($price_before_discount * $discount_percent) / 100;
        $harga_total = $price_before_discount - $discount_amount;
        
        echo json_encode(array(
            'success' => true,
            'data' => array(
                'durasi_jam' => $durasi_jam,
                'harga_per_jam' => $harga_per_jam,
                'price_before_discount' => $price_before_discount,
                'discount_percent' => $discount_percent,
                'discount_amount' => $discount_amount,
                'harga_total' => $harga_total
            )
        ));
    }

    public function check_availability() {
        $lapangan_id = $this->input->post('lapangan_id');
        $tanggal_booking = $this->input->post('tanggal_booking');
        $jam_mulai = $this->input->post('jam_mulai');
        $jam_selesai = $this->input->post('jam_selesai');
        $exclude_id = $this->input->post('exclude_id'); // For edit mode
        
        if (!$lapangan_id || !$tanggal_booking || !$jam_mulai || !$jam_selesai) {
            echo json_encode(array('success' => false, 'message' => 'Missing required parameters'));
            return;
        }
        
        $is_available = !$this->Booking_model->check_schedule_conflict($lapangan_id, $tanggal_booking, $jam_mulai, $jam_selesai, $exclude_id);
        
        echo json_encode(array(
            'success' => true,
            'available' => $is_available,
            'message' => $is_available ? 'Jadwal tersedia' : 'Jadwal bertabrakan dengan booking lain'
        ));
    }

    private function _generate_booking_code() {
        $date = date('ymd');
        $last_booking = $this->Booking_model->get_last_booking_code($date);
        
        if ($last_booking) {
            $last_number = intval(substr($last_booking->kode_booking, -2));
            $new_number = str_pad($last_number + 1, 2, '0', STR_PAD_LEFT);
        } else {
            $new_number = '01';
        }
        
        return 'BK' . $date . $new_number;
    }
}