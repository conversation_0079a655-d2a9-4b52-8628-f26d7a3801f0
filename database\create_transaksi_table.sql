-- ============================================================================
-- TABEL TRANSAKSI - SISTEM PEMBAYARAN PADEL BOOKING
-- ============================================================================
-- Dibuat untuk mengelola transaksi pembayaran booking lapangan padel
-- Terintegrasi dengan tabel booking dan users
-- ============================================================================

-- Drop table jika sudah ada (hati-hati di production!)
-- DROP TABLE IF EXISTS `transaksi`;

-- Buat tabel transaksi
CREATE TABLE `transaksi` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'Primary key transaksi',
  `booking_id` int(11) NOT NULL COMMENT 'ID booking yang dibayar',
  `kode_transaksi` varchar(20) NOT NULL COMMENT 'Kode unik transaksi (TRX + tanggal + nomor urut)',
  `total_harga` decimal(12,2) NOT NULL COMMENT 'Total harga yang harus dibayar',
  `jumlah_bayar` decimal(12,2) NOT NULL COMMENT 'Jumlah uang yang dibayarkan customer',
  `kembalian` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT 'Kembalian untuk customer',
  `metode_pembayaran` enum('tunai','transfer','kartu','qris','ovo','gopay','dana') NOT NULL DEFAULT 'tunai' COMMENT 'Metode pembayaran yang digunakan',
  `kasir_id` int(11) NOT NULL COMMENT 'ID user yang melakukan transaksi (kasir)',
  `tanggal_transaksi` datetime NOT NULL COMMENT 'Tanggal dan waktu transaksi',
  `nomor_referensi` varchar(50) DEFAULT NULL COMMENT 'Nomor referensi untuk pembayaran non-tunai',
  `catatan` text DEFAULT NULL COMMENT 'Catatan tambahan transaksi',
  `status_transaksi` enum('pending','berhasil','gagal','dibatalkan') NOT NULL DEFAULT 'berhasil' COMMENT 'Status transaksi',
  `bukti_pembayaran` varchar(255) DEFAULT NULL COMMENT 'Path file bukti pembayaran (untuk transfer)',
  `diskon_tambahan` decimal(10,2) DEFAULT 0.00 COMMENT 'Diskon tambahan di luar member discount',
  `pajak` decimal(10,2) DEFAULT 0.00 COMMENT 'Pajak yang dikenakan',
  `biaya_admin` decimal(10,2) DEFAULT 0.00 COMMENT 'Biaya admin untuk pembayaran non-tunai',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Waktu record dibuat',
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT 'Waktu record terakhir diupdate',
  `deleted_at` datetime DEFAULT NULL COMMENT 'Soft delete timestamp',

  -- Primary Key
  PRIMARY KEY (`id`),

  -- Unique Constraints
  UNIQUE KEY `uk_kode_transaksi` (`kode_transaksi`),
  UNIQUE KEY `uk_booking_id` (`booking_id`),

  -- Indexes untuk performance
  KEY `idx_tanggal_transaksi` (`tanggal_transaksi`),
  KEY `idx_metode_pembayaran` (`metode_pembayaran`),
  KEY `idx_kasir_id` (`kasir_id`),
  KEY `idx_status_transaksi` (`status_transaksi`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_soft_delete` (`deleted_at`),

  -- Composite Indexes untuk query yang sering digunakan
  KEY `idx_kasir_tanggal` (`kasir_id`, `tanggal_transaksi`),
  KEY `idx_tanggal_metode` (`tanggal_transaksi`, `metode_pembayaran`),
  KEY `idx_tanggal_status` (`tanggal_transaksi`, `status_transaksi`),
  KEY `idx_metode_status` (`metode_pembayaran`, `status_transaksi`),

  -- Foreign Key Constraints
  CONSTRAINT `fk_transaksi_booking`
    FOREIGN KEY (`booking_id`)
    REFERENCES `booking` (`id`)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,

  CONSTRAINT `fk_transaksi_kasir`
    FOREIGN KEY (`kasir_id`)
    REFERENCES `users` (`id`)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,

  -- Check Constraints untuk validasi data
  CONSTRAINT `chk_total_harga_positive` CHECK (`total_harga` > 0),
  CONSTRAINT `chk_jumlah_bayar_positive` CHECK (`jumlah_bayar` >= 0),
  CONSTRAINT `chk_kembalian_non_negative` CHECK (`kembalian` >= 0),
  CONSTRAINT `chk_diskon_non_negative` CHECK (`diskon_tambahan` >= 0),
  CONSTRAINT `chk_pajak_non_negative` CHECK (`pajak` >= 0),
  CONSTRAINT `chk_biaya_admin_non_negative` CHECK (`biaya_admin` >= 0),
  CONSTRAINT `chk_kembalian_logic` CHECK (`kembalian` = `jumlah_bayar` - `total_harga` + `diskon_tambahan` - `pajak` - `biaya_admin`)

) ENGINE=InnoDB
  DEFAULT CHARSET=utf8mb4
  COLLATE=utf8mb4_unicode_ci
  COMMENT='Tabel untuk menyimpan data transaksi pembayaran booking';

-- ============================================================================
-- TRIGGERS UNTUK AUDIT DAN VALIDASI
-- ============================================================================

-- Trigger untuk generate kode transaksi otomatis
DELIMITER $$
CREATE TRIGGER `tr_transaksi_before_insert`
BEFORE INSERT ON `transaksi`
FOR EACH ROW
BEGIN
    DECLARE next_number INT DEFAULT 1;
    DECLARE date_part VARCHAR(6);
    DECLARE last_code VARCHAR(20);

    -- Generate date part (YYMMDD)
    SET date_part = DATE_FORMAT(NEW.tanggal_transaksi, '%y%m%d');

    -- Get last transaction code for today
    SELECT kode_transaksi INTO last_code
    FROM transaksi
    WHERE kode_transaksi LIKE CONCAT('TRX', date_part, '%')
    ORDER BY id DESC
    LIMIT 1;

    -- Calculate next number
    IF last_code IS NOT NULL THEN
        SET next_number = CAST(SUBSTRING(last_code, -3) AS UNSIGNED) + 1;
    END IF;

    -- Set kode_transaksi if not provided
    IF NEW.kode_transaksi IS NULL OR NEW.kode_transaksi = '' THEN
        SET NEW.kode_transaksi = CONCAT('TRX', date_part, LPAD(next_number, 3, '0'));
    END IF;

    -- Validate kembalian calculation
    SET NEW.kembalian = NEW.jumlah_bayar - NEW.total_harga + NEW.diskon_tambahan - NEW.pajak - NEW.biaya_admin;

    -- Ensure kembalian is not negative
    IF NEW.kembalian < 0 THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Jumlah bayar tidak mencukupi';
    END IF;
END$$
DELIMITER ;

-- Trigger untuk update booking status setelah transaksi berhasil
DELIMITER $$
CREATE TRIGGER `tr_transaksi_after_insert`
AFTER INSERT ON `transaksi`
FOR EACH ROW
BEGIN
    -- Update booking status to 'selesai' jika transaksi berhasil
    IF NEW.status_transaksi = 'berhasil' THEN
        UPDATE booking
        SET status_booking = 'selesai',
            updated_at = NOW()
        WHERE id = NEW.booking_id;
    END IF;
END$$
DELIMITER ;

-- ============================================================================
-- VIEWS UNTUK REPORTING
-- ============================================================================

-- View untuk laporan transaksi harian
CREATE VIEW `v_transaksi_harian` AS
SELECT
    DATE(tanggal_transaksi) as tanggal,
    COUNT(*) as total_transaksi,
    SUM(total_harga) as total_pendapatan,
    SUM(kembalian) as total_kembalian,
    AVG(total_harga) as rata_rata_transaksi,
    metode_pembayaran,
    status_transaksi
FROM transaksi
WHERE deleted_at IS NULL
GROUP BY DATE(tanggal_transaksi), metode_pembayaran, status_transaksi
ORDER BY tanggal DESC, metode_pembayaran;

-- View untuk laporan kinerja kasir
CREATE VIEW `v_kinerja_kasir` AS
SELECT
    u.id as kasir_id,
    u.full_name as nama_kasir,
    DATE(t.tanggal_transaksi) as tanggal,
    COUNT(*) as jumlah_transaksi,
    SUM(t.total_harga) as total_pendapatan,
    AVG(t.total_harga) as rata_rata_transaksi,
    MIN(t.tanggal_transaksi) as transaksi_pertama,
    MAX(t.tanggal_transaksi) as transaksi_terakhir
FROM transaksi t
JOIN users u ON t.kasir_id = u.id
WHERE t.deleted_at IS NULL
  AND t.status_transaksi = 'berhasil'
GROUP BY u.id, u.full_name, DATE(t.tanggal_transaksi)
ORDER BY tanggal DESC, total_pendapatan DESC;

-- View untuk detail transaksi lengkap
CREATE VIEW `v_transaksi_detail` AS
SELECT
    t.id,
    t.kode_transaksi,
    t.tanggal_transaksi,
    t.total_harga,
    t.jumlah_bayar,
    t.kembalian,
    t.metode_pembayaran,
    t.status_transaksi,
    b.kode_booking,
    b.customer_name,
    b.customer_phone,
    b.tanggal_booking,
    b.jam_mulai,
    b.jam_selesai,
    l.nama_lapangan,
    u.full_name as nama_kasir,
    mc.full_name as nama_member,
    mc.member_code
FROM transaksi t
LEFT JOIN booking b ON t.booking_id = b.id
LEFT JOIN lapangan l ON b.lapangan_id = l.id
LEFT JOIN users u ON t.kasir_id = u.id
LEFT JOIN member_codes mc ON b.member_code = mc.member_code
WHERE t.deleted_at IS NULL;

-- ============================================================================
-- STORED PROCEDURES
-- ============================================================================

-- Procedure untuk mendapatkan statistik transaksi
DELIMITER $$
CREATE PROCEDURE `sp_get_transaksi_stats`(
    IN p_start_date DATE,
    IN p_end_date DATE,
    IN p_kasir_id INT
)
BEGIN
    SELECT
        COUNT(*) as total_transaksi,
        SUM(CASE WHEN status_transaksi = 'berhasil' THEN 1 ELSE 0 END) as transaksi_berhasil,
        SUM(CASE WHEN status_transaksi = 'gagal' THEN 1 ELSE 0 END) as transaksi_gagal,
        SUM(CASE WHEN status_transaksi = 'berhasil' THEN total_harga ELSE 0 END) as total_pendapatan,
        AVG(CASE WHEN status_transaksi = 'berhasil' THEN total_harga ELSE NULL END) as rata_rata_transaksi,
        SUM(kembalian) as total_kembalian,
        COUNT(DISTINCT kasir_id) as jumlah_kasir_aktif
    FROM transaksi
    WHERE deleted_at IS NULL
      AND DATE(tanggal_transaksi) BETWEEN p_start_date AND p_end_date
      AND (p_kasir_id IS NULL OR kasir_id = p_kasir_id);
END$$
DELIMITER ;

-- ============================================================================
-- SAMPLE DATA (OPTIONAL)
-- ============================================================================

-- Insert sample data untuk testing
INSERT INTO `transaksi` (
    `booking_id`,
    `total_harga`,
    `jumlah_bayar`,
    `metode_pembayaran`,
    `kasir_id`,
    `tanggal_transaksi`,
    `status_transaksi`
) VALUES
(1, 150000.00, 150000.00, 'tunai', 2, '2025-01-14 10:30:00', 'berhasil'),
(2, 200000.00, 200000.00, 'transfer', 2, '2025-01-14 14:15:00', 'berhasil'),
(3, 180000.00, 200000.00, 'tunai', 2, '2025-01-14 16:45:00', 'berhasil'),
(4, 220000.00, 220000.00, 'qris', 2, '2025-01-14 19:20:00', 'berhasil');

-- ============================================================================
-- INDEXES TAMBAHAN UNTUK OPTIMASI QUERY
-- ============================================================================

-- Index untuk query laporan bulanan
CREATE INDEX `idx_monthly_report` ON `transaksi` (
    YEAR(`tanggal_transaksi`),
    MONTH(`tanggal_transaksi`),
    `status_transaksi`
);

-- Index untuk query top customers
CREATE INDEX `idx_customer_analysis` ON `transaksi` (
    `booking_id`,
    `total_harga`,
    `status_transaksi`
);

-- ============================================================================
-- GRANTS DAN PERMISSIONS (SESUAIKAN DENGAN KEBUTUHAN)
-- ============================================================================

-- Grant permissions untuk user aplikasi
-- GRANT SELECT, INSERT, UPDATE ON `transaksi` TO 'padel_app'@'localhost';
-- GRANT SELECT ON `v_transaksi_detail` TO 'padel_app'@'localhost';
-- GRANT SELECT ON `v_transaksi_harian` TO 'padel_app'@'localhost';
-- GRANT SELECT ON `v_kinerja_kasir` TO 'padel_app'@'localhost';
-- GRANT EXECUTE ON PROCEDURE `sp_get_transaksi_stats` TO 'padel_app'@'localhost';

-- ============================================================================
-- SELESAI
-- ============================================================================
