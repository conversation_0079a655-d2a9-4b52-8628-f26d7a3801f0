<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h1><i class="fas fa-plus-circle"></i> Buat Booking Baru</h1>
            <p class="text-muted">Tambahkan booking lapangan untuk pelanggan</p>
        </div>
        <div class="col-auto">
            <a href="<?php echo site_url('booking'); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-plus"></i> Formulir Booking
                </h5>
            </div>
            <div class="card-body">
                <?php echo form_open('booking/create', array('id' => 'bookingForm')); ?>
                
                <!-- Customer Type Selection -->
                <div class="row mb-4">
                    <div class="col-12">
                        <label class="form-label">Tipe Customer <span class="text-danger">*</span></label>
                        <div class="btn-group w-100" role="group" data-bs-toggle="buttons">
                            <input type="radio" class="btn-check" name="customer_type" id="member_type" value="member" 
                                   <?php echo set_radio('customer_type', 'member'); ?>>
                            <label class="btn btn-outline-primary" for="member_type">
                                <i class="fas fa-star"></i> Member
                            </label>
                            
                            <input type="radio" class="btn-check" name="customer_type" id="non_member_type" value="non_member" 
                                   <?php echo set_radio('customer_type', 'non_member', true); ?>>
                            <label class="btn btn-outline-secondary" for="non_member_type">
                                <i class="fas fa-user"></i> Non-Member
                            </label>
                        </div>
                        <?php echo form_error('customer_type', '<div class="invalid-feedback d-block">', '</div>'); ?>
                    </div>
                </div>

                <!-- Member Section -->
                <div id="memberSection" class="mb-4" style="display: none;">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-star text-warning"></i> Informasi Member
                            </h6>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="member_code" class="form-label">Member Code <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="member_code" name="member_code" 
                                               placeholder="Masukkan member code" value="<?php echo set_value('member_code'); ?>">
                                        <button type="button" class="btn btn-primary" id="checkMemberBtn">
                                            <i class="fas fa-search"></i> Cek
                                        </button>
                                    </div>
                                    <?php echo form_error('member_code', '<div class="invalid-feedback d-block">', '</div>'); ?>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Discount</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="discount_percent" readonly>
                                        <span class="input-group-text">%</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <label class="form-label">Nama Member</label>
                                    <input type="text" class="form-control" id="member_name" readonly>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Telepon Member</label>
                                    <input type="text" class="form-control" id="member_phone" readonly>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Non-Member Section -->
                <div id="nonMemberSection" class="mb-4">
                    <div class="row">
                        <div class="col-md-6">
                            <label for="nama_pemesan" class="form-label">Nama Pemesan <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="nama_pemesan" name="nama_pemesan" 
                                   placeholder="Masukkan nama pemesan" value="<?php echo set_value('nama_pemesan'); ?>">
                            <?php echo form_error('nama_pemesan', '<div class="invalid-feedback d-block">', '</div>'); ?>
                        </div>
                        <div class="col-md-6">
                            <label for="telepon_pemesan" class="form-label">Telepon Pemesan <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="telepon_pemesan" name="telepon_pemesan" 
                                   placeholder="Masukkan nomor telepon" value="<?php echo set_value('telepon_pemesan'); ?>">
                            <?php echo form_error('telepon_pemesan', '<div class="invalid-feedback d-block">', '</div>'); ?>
                        </div>
                    </div>
                </div>

                <!-- Booking Details -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="lapangan_id" class="form-label">Lapangan <span class="text-danger">*</span></label>
                        <select class="form-control" id="lapangan_id" name="lapangan_id">
                            <option value="">Pilih Lapangan</option>
                            <?php foreach ($lapangan_list as $lapangan): ?>
                                <option value="<?php echo $lapangan->id; ?>" 
                                        data-harga="<?php echo $lapangan->harga_per_jam; ?>"
                                        <?php echo set_select('lapangan_id', $lapangan->id); ?>>
                                    <?php echo $lapangan->nama_lapangan; ?> 
                                    (Rp <?php echo number_format($lapangan->harga_per_jam, 0, ',', '.'); ?>/jam)
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <?php echo form_error('lapangan_id', '<div class="invalid-feedback d-block">', '</div>'); ?>
                    </div>
                    <div class="col-md-6">
                        <label for="tanggal_booking" class="form-label">Tanggal Booking <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="tanggal_booking" name="tanggal_booking" 
                               min="<?php echo date('Y-m-d'); ?>" value="<?php echo set_value('tanggal_booking'); ?>">
                        <?php echo form_error('tanggal_booking', '<div class="invalid-feedback d-block">', '</div>'); ?>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="jam_mulai" class="form-label">Jam Mulai <span class="text-danger">*</span></label>
                        <input type="time" class="form-control" id="jam_mulai" name="jam_mulai" 
                               value="<?php echo set_value('jam_mulai'); ?>">
                        <?php echo form_error('jam_mulai', '<div class="invalid-feedback d-block">', '</div>'); ?>
                    </div>
                    <div class="col-md-6">
                        <label for="jam_selesai" class="form-label">Jam Selesai <span class="text-danger">*</span></label>
                        <input type="time" class="form-control" id="jam_selesai" name="jam_selesai" 
                               value="<?php echo set_value('jam_selesai'); ?>">
                        <?php echo form_error('jam_selesai', '<div class="invalid-feedback d-block">', '</div>'); ?>
                    </div>
                </div>

                <!-- Availability Check -->
                <div class="mb-3">
                    <button type="button" class="btn btn-info btn-sm" id="checkAvailabilityBtn">
                        <i class="fas fa-calendar-check"></i> Cek Ketersediaan Jadwal
                    </button>
                    <div id="availabilityResult" class="mt-2"></div>
                </div>

                <div class="d-flex justify-content-end gap-2">
                    <a href="<?php echo site_url('booking'); ?>" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Batal
                    </a>
                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <i class="fas fa-save"></i> Simpan Booking
                    </button>
                </div>

                <?php echo form_close(); ?>
            </div>
        </div>
    </div>

    <!-- Price Calculator -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calculator"></i> Kalkulasi Harga
                </h5>
            </div>
            <div class="card-body">
                <div id="priceCalculator">
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-calculator fa-2x mb-3"></i>
                        <p>Pilih lapangan dan waktu untuk melihat kalkulasi harga</p>
                    </div>
                </div>

                <div id="priceDetails" style="display: none;">
                    <table class="table table-sm">
                        <tbody>
                            <tr>
                                <td>Lapangan:</td>
                                <td id="selectedLapangan">-</td>
                            </tr>
                            <tr>
                                <td>Durasi:</td>
                                <td><span id="duration">0</span> jam</td>
                            </tr>
                            <tr>
                                <td>Harga per jam:</td>
                                <td>Rp <span id="pricePerHour">0</span></td>
                            </tr>
                            <tr>
                                <td>Subtotal:</td>
                                <td>Rp <span id="subtotal">0</span></td>
                            </tr>
                            <tr id="discountRow" style="display: none;">
                                <td>Diskon (<span id="discountPercent">0</span>%):</td>
                                <td class="text-success">- Rp <span id="discountAmount">0</span></td>
                            </tr>
                            <tr class="table-active">
                                <td><strong>Total:</strong></td>
                                <td><strong class="text-primary">Rp <span id="totalPrice">0</span></strong></td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle"></i>
                        <small>Harga akan dihitung otomatis berdasarkan durasi dan tipe customer</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt"></i> Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="setTimeSlot('08:00', '10:00')">
                        <i class="fas fa-clock"></i> 08:00 - 10:00 (2 jam)
                    </button>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="setTimeSlot('10:00', '12:00')">
                        <i class="fas fa-clock"></i> 10:00 - 12:00 (2 jam)
                    </button>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="setTimeSlot('14:00', '16:00')">
                        <i class="fas fa-clock"></i> 14:00 - 16:00 (2 jam)
                    </button>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="setTimeSlot('16:00', '18:00')">
                        <i class="fas fa-clock"></i> 16:00 - 18:00 (2 jam)
                    </button>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="setTimeSlot('19:00', '21:00')">
                        <i class="fas fa-clock"></i> 19:00 - 21:00 (2 jam)
                    </button>
                </div>
                
                <hr>
                
                <button type="button" class="btn btn-outline-info btn-sm w-100" onclick="setTodayDate()">
                    <i class="fas fa-calendar-day"></i> Set Hari Ini
                </button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Customer type change handler
    $('input[name="customer_type"]').change(function() {
        const customerType = $(this).val();
        
        if (customerType === 'member') {
            $('#memberSection').show();
            $('#nonMemberSection').hide();
            
            // Clear non-member fields
            $('#nama_pemesan').val('');
            $('#telepon_pemesan').val('');
        } else {
            $('#memberSection').hide();
            $('#nonMemberSection').show();
            
            // Clear member fields
            $('#member_code').val('');
            $('#member_name').val('');
            $('#member_phone').val('');
            $('#discount_percent').val('');
        }
        
        calculatePrice();
    });

    // Check member info
    $('#checkMemberBtn').click(function() {
        const memberCode = $('#member_code').val().trim();
        
        if (!memberCode) {
            showAlert('warning', 'Silakan masukkan member code');
            return;
        }
        
        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Checking...');
        
        $.ajax({
            url: '<?php echo site_url("booking/get_member_info"); ?>',
            method: 'POST',
            data: {
                member_code: memberCode,
                '<?php echo $this->security->get_csrf_token_name(); ?>': '<?php echo $this->security->get_csrf_hash(); ?>'
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    $('#member_name').val(response.data.full_name);
                    $('#member_phone').val(response.data.phone);
                    $('#discount_percent').val(response.data.discount_percent);
                    
                    showAlert('success', 'Member ditemukan: ' + response.data.full_name);
                    calculatePrice();
                } else {
                    showAlert('error', response.message || 'Member tidak ditemukan');
                    
                    // Clear member info
                    $('#member_name').val('');
                    $('#member_phone').val('');
                    $('#discount_percent').val('');
                }
            },
            error: function() {
                showAlert('error', 'Terjadi kesalahan saat mengecek member');
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-search"></i> Cek');
            }
        });
    });

    // Check availability
    $('#checkAvailabilityBtn').click(function() {
        const lapanganId = $('#lapangan_id').val();
        const tanggal = $('#tanggal_booking').val();
        const jamMulai = $('#jam_mulai').val();
        const jamSelesai = $('#jam_selesai').val();
        
        if (!lapanganId || !tanggal || !jamMulai || !jamSelesai) {
            showAlert('warning', 'Lengkapi data lapangan, tanggal, dan waktu terlebih dahulu');
            return;
        }
        
        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Checking...');
        
        $.ajax({
            url: '<?php echo site_url("booking/check_availability"); ?>',
            method: 'POST',
            data: {
                lapangan_id: lapanganId,
                tanggal_booking: tanggal,
                jam_mulai: jamMulai,
                jam_selesai: jamSelesai,
                '<?php echo $this->security->get_csrf_token_name(); ?>': '<?php echo $this->security->get_csrf_hash(); ?>'
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    if (response.available) {
                        $('#availabilityResult').html(
                            '<div class="alert alert-success alert-sm">' +
                            '<i class="fas fa-check"></i> ' + response.message +
                            '</div>'
                        );
                    } else {
                        $('#availabilityResult').html(
                            '<div class="alert alert-danger alert-sm">' +
                            '<i class="fas fa-times"></i> ' + response.message +
                            '</div>'
                        );
                    }
                } else {
                    showAlert('error', response.message || 'Gagal mengecek ketersediaan');
                }
            },
            error: function(xhr, status, error) {
                console.error('Availability check error:', xhr.responseText);
                let errorMessage = 'Terjadi kesalahan saat mengecek ketersediaan';

                if (status === 'timeout') {
                    errorMessage = 'Request timeout. Silakan coba lagi.';
                } else if (xhr.status === 403) {
                    errorMessage = 'Akses ditolak. Silakan refresh halaman.';
                } else if (xhr.status === 500) {
                    errorMessage = 'Server error. Silakan hubungi administrator.';
                }

                showAlert('error', errorMessage);
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-calendar-check"></i> Cek Ketersediaan Jadwal');
            }
        });
    });

    // Calculate price when input changes
    $('#lapangan_id, #jam_mulai, #jam_selesai').change(calculatePrice);

    // Form validation
    $('#bookingForm').submit(function(e) {
        e.preventDefault();
        
        // Basic validation
        const customerType = $('input[name="customer_type"]:checked').val();
        
        if (customerType === 'member') {
            if (!$('#member_code').val().trim()) {
                showAlert('error', 'Member code harus diisi');
                return false;
            }
            
            if (!$('#member_name').val().trim()) {
                showAlert('error', 'Silakan cek member code terlebih dahulu');
                return false;
            }
        } else {
            if (!$('#nama_pemesan').val().trim()) {
                showAlert('error', 'Nama pemesan harus diisi');
                return false;
            }
            
            if (!$('#telepon_pemesan').val().trim()) {
                showAlert('error', 'Telepon pemesan harus diisi');
                return false;
            }
        }
        
        if (!$('#lapangan_id').val()) {
            showAlert('error', 'Pilih lapangan terlebih dahulu');
            return false;
        }
        
        if (!$('#tanggal_booking').val()) {
            showAlert('error', 'Tanggal booking harus diisi');
            return false;
        }
        
        if (!$('#jam_mulai').val() || !$('#jam_selesai').val()) {
            showAlert('error', 'Jam mulai dan selesai harus diisi');
            return false;
        }
        
        if ($('#jam_mulai').val() >= $('#jam_selesai').val()) {
            showAlert('error', 'Jam selesai harus lebih besar dari jam mulai');
            return false;
        }
        
        // Submit form
        $('#submitBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');
        this.submit();
    });
});

function calculatePrice() {
    const lapanganId = $('#lapangan_id').val();
    const jamMulai = $('#jam_mulai').val();
    const jamSelesai = $('#jam_selesai').val();
    const discountPercent = $('#discount_percent').val() || 0;
    
    if (!lapanganId || !jamMulai || !jamSelesai) {
        $('#priceCalculator').show();
        $('#priceDetails').hide();
        return;
    }
    
    $.ajax({
        url: '<?php echo site_url("booking/calculate_price"); ?>',
        method: 'POST',
        data: {
            lapangan_id: lapanganId,
            jam_mulai: jamMulai,
            jam_selesai: jamSelesai,
            discount_percent: discountPercent,
            '<?php echo $this->security->get_csrf_token_name(); ?>': '<?php echo $this->security->get_csrf_hash(); ?>'
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                const data = response.data;
                const lapanganText = $('#lapangan_id option:selected').text().split(' (')[0];
                
                $('#selectedLapangan').text(lapanganText);
                $('#duration').text(data.durasi_jam);
                $('#pricePerHour').text(formatNumber(data.harga_per_jam));
                $('#subtotal').text(formatNumber(data.price_before_discount));
                
                if (data.discount_percent > 0) {
                    $('#discountPercent').text(data.discount_percent);
                    $('#discountAmount').text(formatNumber(data.discount_amount));
                    $('#discountRow').show();
                } else {
                    $('#discountRow').hide();
                }
                
                $('#totalPrice').text(formatNumber(data.harga_total));
                
                $('#priceCalculator').hide();
                $('#priceDetails').show();
            } else {
                showAlert('error', response.message || 'Gagal menghitung harga');
            }
        },
        error: function() {
            console.error('Error calculating price');
        }
    });
}

function setTimeSlot(start, end) {
    $('#jam_mulai').val(start);
    $('#jam_selesai').val(end);
    calculatePrice();
}

function setTodayDate() {
    const today = new Date().toISOString().split('T')[0];
    $('#tanggal_booking').val(today);
}

function formatNumber(num) {
    return parseInt(num).toLocaleString('id-ID');
}

function showAlert(type, message) {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    };
    
    const alert = $(`
        <div class="alert ${alertClass[type]} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'error' ? 'times' : type === 'success' ? 'check' : 'info'}-circle"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);
    
    $('.page-header').after(alert);
    
    setTimeout(() => {
        alert.fadeOut();
    }, 5000);
}
</script>