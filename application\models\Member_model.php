<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Member_model extends CI_Model {

    private $table = 'member_codes';

    public function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    /**
     * Get all members
     */
    public function get_all_members($search = '', $status = '')
    {
        $this->db->select('*');
        $this->db->from($this->table);
        
        // Search
        if (!empty($search)) {
            $this->db->group_start();
            $this->db->like('member_code', $search);
            $this->db->or_like('full_name', $search);
            $this->db->or_like('phone', $search);
            $this->db->or_like('email', $search);
            $this->db->group_end();
        }
        
        // Status filter
        if (!empty($status)) {
            $this->db->where('status', $status);
        }
        
        $this->db->order_by('created_at', 'DESC');
        
        return $this->db->get()->result();
    }

    /**
     * Get member by ID
     */
    public function get_member_by_id($id)
    {
        $this->db->where('id', $id);
        return $this->db->get($this->table)->row();
    }

    /**
     * Get member by member code
     */
    public function get_member_by_code($member_code)
    {
        $this->db->where('member_code', $member_code);
        return $this->db->get($this->table)->row();
    }

    /**
     * Get member by phone
     */
    public function get_member_by_phone($phone)
    {
        $this->db->where('phone', $phone);
        return $this->db->get($this->table)->row();
    }

    /**
     * Create new member
     */
    public function create_member($data)
    {
        // Generate member code if not provided
        if (!isset($data['member_code']) || empty($data['member_code'])) {
            $data['member_code'] = $this->generate_member_code();
        }
        
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->db->insert($this->table, $data);
    }

    /**
     * Update member
     */
    public function update_member($id, $data)
    {
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $this->db->where('id', $id);
        return $this->db->update($this->table, $data);
    }

    /**
     * Delete member
     */
    public function delete_member($id)
    {
        $this->db->where('id', $id);
        return $this->db->delete($this->table);
    }

    /**
     * Generate unique member code
     */
    public function generate_member_code()
    {
        do {
            $code = 'MBR' . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
            $this->db->where('member_code', $code);
            $exists = $this->db->get($this->table)->num_rows() > 0;
        } while ($exists);
        
        return $code;
    }

    /**
     * Check if member code exists
     */
    public function member_code_exists($member_code, $exclude_id = null)
    {
        $this->db->where('member_code', $member_code);
        
        if ($exclude_id) {
            $this->db->where('id !=', $exclude_id);
        }
        
        return $this->db->count_all_results($this->table) > 0;
    }

    /**
     * Check if phone exists
     */
    public function phone_exists($phone, $exclude_id = null)
    {
        $this->db->where('phone', $phone);
        
        if ($exclude_id) {
            $this->db->where('id !=', $exclude_id);
        }
        
        return $this->db->count_all_results($this->table) > 0;
    }

    /**
     * Get member statistics
     */
    public function get_member_stats()
    {
        $stats = array();
        
        // Total members
        $stats['total'] = $this->db->count_all($this->table);
        
        // Active members
        $this->db->where('status', 'aktif');
        $stats['active'] = $this->db->count_all_results($this->table);
        
        // Inactive members
        $stats['inactive'] = $stats['total'] - $stats['active'];
        
        // Top spenders this month
        $this->db->select('member_code, full_name, total_spent');
        $this->db->where('status', 'aktif');
        $this->db->order_by('total_spent', 'DESC');
        $this->db->limit(5);
        $stats['top_spenders'] = $this->db->get($this->table)->result();
        
        // Most active members (by booking count)
        $this->db->select('member_code, full_name, total_bookings');
        $this->db->where('status', 'aktif');
        $this->db->order_by('total_bookings', 'DESC');
        $this->db->limit(5);
        $stats['most_active'] = $this->db->get($this->table)->result();
        
        return $stats;
    }

    /**
     * Get member with booking summary
     */
    public function get_member_with_bookings($member_code)
    {
        $this->db->select('mc.*, COUNT(b.id) as booking_count, SUM(b.harga_total) as total_spent_calculated');
        $this->db->from($this->table . ' mc');
        $this->db->join('booking b', 'b.member_code = mc.member_code AND b.status_booking != "dibatalkan"', 'left');
        $this->db->where('mc.member_code', $member_code);
        $this->db->group_by('mc.id');
        
        return $this->db->get()->row();
    }

    /**
     * Get member bookings
     */
    public function get_member_bookings($member_code, $limit = 10, $offset = 0)
    {
        $this->db->select('b.*, l.nama_lapangan');
        $this->db->from('booking b');
        $this->db->join('lapangan l', 'l.id = b.lapangan_id', 'left');
        $this->db->where('b.member_code', $member_code);
        $this->db->order_by('b.tanggal_booking', 'DESC');
        $this->db->order_by('b.jam_mulai', 'DESC');
        $this->db->limit($limit, $offset);
        
        return $this->db->get()->result();
    }

    /**
     * Count member bookings
     */
    public function count_member_bookings($member_code)
    {
        $this->db->where('member_code', $member_code);
        return $this->db->count_all_results('booking');
    }

    /**
     * Update member statistics manually (if needed)
     */
    public function update_member_statistics($member_code)
    {
        // Calculate actual statistics from booking table
        $this->db->select('COUNT(*) as total_bookings, COALESCE(SUM(harga_total), 0) as total_spent');
        $this->db->where('member_code', $member_code);
        $this->db->where('status_booking !=', 'dibatalkan');
        $result = $this->db->get('booking')->row();
        
        if ($result) {
            $update_data = array(
                'total_bookings' => $result->total_bookings,
                'total_spent' => $result->total_spent,
                'updated_at' => date('Y-m-d H:i:s')
            );
            
            $this->db->where('member_code', $member_code);
            return $this->db->update($this->table, $update_data);
        }
        
        return false;
    }

    /**
     * Search members for autocomplete
     */
    public function search_members($keyword, $limit = 10)
    {
        $this->db->select('member_code, full_name, phone, discount_percent');
        $this->db->from($this->table);
        $this->db->where('status', 'aktif');
        
        $this->db->group_start();
        $this->db->like('member_code', $keyword);
        $this->db->or_like('full_name', $keyword);
        $this->db->or_like('phone', $keyword);
        $this->db->group_end();
        
        $this->db->order_by('full_name', 'ASC');
        $this->db->limit($limit);
        
        return $this->db->get()->result();
    }

    /**
     * Get members with filters
     */
    public function get_members_filtered($filters = array())
    {
        $this->db->select('*');
        $this->db->from($this->table);
        
        // Apply filters
        if (!empty($filters['status'])) {
            $this->db->where('status', $filters['status']);
        }
        
        if (!empty($filters['discount_min'])) {
            $this->db->where('discount_percent >=', $filters['discount_min']);
        }
        
        if (!empty($filters['discount_max'])) {
            $this->db->where('discount_percent <=', $filters['discount_max']);
        }
        
        if (!empty($filters['member_since_from'])) {
            $this->db->where('created_at >=', $filters['member_since_from']);
        }
        
        if (!empty($filters['member_since_to'])) {
            $this->db->where('created_at <=', $filters['member_since_to'] . ' 23:59:59');
        }
        
        $this->db->order_by('created_at', 'DESC');
        
        return $this->db->get()->result();
    }

    /**
     * Toggle member status
     */
    public function toggle_status($id)
    {
        $member = $this->get_member_by_id($id);
        
        if ($member) {
            $new_status = ($member->status == 'aktif') ? 'tidak_aktif' : 'aktif';
            
            $data = array(
                'status' => $new_status,
                'updated_at' => date('Y-m-d H:i:s')
            );
            
            $this->db->where('id', $id);
            return $this->db->update($this->table, $data);
        }
        
        return false;
    }

    /**
     * Get discount tiers summary
     */
    public function get_discount_tiers()
    {
        $this->db->select('discount_percent, COUNT(*) as member_count');
        $this->db->where('status', 'aktif');
        $this->db->group_by('discount_percent');
        $this->db->order_by('discount_percent', 'ASC');
        
        return $this->db->get($this->table)->result();
    }

    /**
     * Validate member for booking
     */
    public function validate_member_for_booking($member_code, $phone = null)
    {
        $this->db->where('member_code', $member_code);
        $this->db->where('status', 'aktif');
        
        if ($phone) {
            $this->db->where('phone', $phone);
        }
        
        $member = $this->db->get($this->table)->row();
        
        return $member ? $member : false;
    }

    /**
     * Get member booking summary for specific period
     */
    public function get_member_booking_summary($member_code, $start_date = null, $end_date = null)
    {
        $this->db->select('
            COUNT(*) as total_bookings,
            SUM(CASE WHEN status_booking = "selesai" THEN 1 ELSE 0 END) as completed_bookings,
            SUM(CASE WHEN status_booking = "pending" THEN 1 ELSE 0 END) as pending_bookings,
            SUM(CASE WHEN status_booking = "dikonfirmasi" THEN 1 ELSE 0 END) as confirmed_bookings,
            SUM(CASE WHEN status_booking = "dibatalkan" THEN 1 ELSE 0 END) as cancelled_bookings,
            SUM(CASE WHEN status_booking = "selesai" THEN harga_total ELSE 0 END) as total_spent,
            AVG(CASE WHEN status_booking = "selesai" THEN harga_total ELSE NULL END) as avg_booking_value,
            SUM(CASE WHEN status_booking = "selesai" THEN durasi_jam ELSE 0 END) as total_hours_played
        ');
        
        $this->db->where('member_code', $member_code);
        
        if ($start_date) {
            $this->db->where('tanggal_booking >=', $start_date);
        }
        
        if ($end_date) {
            $this->db->where('tanggal_booking <=', $end_date);
        }
        
        return $this->db->get('booking')->row();
    }
}