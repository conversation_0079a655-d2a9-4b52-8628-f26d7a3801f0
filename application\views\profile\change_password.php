<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h1><i class="fas fa-key"></i> Ubah Password</h1>
            <p class="text-muted">Perbarui password akun Anda untuk keamanan yang lebih baik</p>
        </div>
        <div class="col-auto">
            <a href="<?php echo site_url('profile'); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shield-alt"></i> Ubah Password
                </h5>
            </div>
            <div class="card-body">
                <?php echo form_open('profile/change_password', array('id' => 'changePasswordForm')); ?>
                
                <!-- Current Password -->
                <div class="mb-3">
                    <label for="current_password" class="form-label">Password Lama <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <input type="password" class="form-control <?php echo form_error('current_password') ? 'is-invalid' : ''; ?>" 
                               id="current_password" name="current_password" 
                               placeholder="Masukkan password lama">
                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('current_password')">
                            <i class="fas fa-eye" id="current_password_icon"></i>
                        </button>
                    </div>
                    <?php echo form_error('current_password', '<div class="invalid-feedback d-block">', '</div>'); ?>
                </div>

                <!-- New Password -->
                <div class="mb-3">
                    <label for="new_password" class="form-label">Password Baru <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <input type="password" class="form-control <?php echo form_error('new_password') ? 'is-invalid' : ''; ?>" 
                               id="new_password" name="new_password" 
                               placeholder="Masukkan password baru">
                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('new_password')">
                            <i class="fas fa-eye" id="new_password_icon"></i>
                        </button>
                    </div>
                    <?php echo form_error('new_password', '<div class="invalid-feedback d-block">', '</div>'); ?>
                    
                    <!-- Password Strength Indicator -->
                    <div class="password-strength mt-2">
                        <div class="progress" style="height: 5px;">
                            <div class="progress-bar" id="strength-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <small id="strength-text" class="text-muted">Kekuatan password</small>
                    </div>
                </div>

                <!-- Confirm Password -->
                <div class="mb-4">
                    <label for="confirm_password" class="form-label">Konfirmasi Password Baru <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <input type="password" class="form-control <?php echo form_error('confirm_password') ? 'is-invalid' : ''; ?>" 
                               id="confirm_password" name="confirm_password" 
                               placeholder="Ulangi password baru">
                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                            <i class="fas fa-eye" id="confirm_password_icon"></i>
                        </button>
                    </div>
                    <?php echo form_error('confirm_password', '<div class="invalid-feedback d-block">', '</div>'); ?>
                    <div id="password-match" class="mt-1"></div>
                </div>

                <!-- Password Requirements -->
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> Persyaratan Password:</h6>
                    <ul class="mb-0 requirements-list">
                        <li id="req-length" class="text-muted">
                            <i class="fas fa-times"></i> Minimal 6 karakter
                        </li>
                        <li id="req-uppercase" class="text-muted">
                            <i class="fas fa-times"></i> Minimal 1 huruf besar
                        </li>
                        <li id="req-lowercase" class="text-muted">
                            <i class="fas fa-times"></i> Minimal 1 huruf kecil
                        </li>
                        <li id="req-number" class="text-muted">
                            <i class="fas fa-times"></i> Minimal 1 angka
                        </li>
                        <li id="req-special" class="text-muted">
                            <i class="fas fa-times"></i> Minimal 1 karakter khusus (!@#$%^&*)
                        </li>
                    </ul>
                </div>

                <!-- Submit Buttons -->
                <div class="d-flex justify-content-end gap-2">
                    <a href="<?php echo site_url('profile'); ?>" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Batal
                    </a>
                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <i class="fas fa-save"></i> Ubah Password
                    </button>
                </div>

                <?php echo form_close(); ?>
            </div>
        </div>

        <!-- Security Tips -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-lightbulb"></i> Tips Keamanan
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-12">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                Gunakan kombinasi huruf besar, kecil, angka, dan simbol
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                Hindari menggunakan informasi personal yang mudah ditebak
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                Jangan gunakan password yang sama dengan akun lain
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                Ubah password secara berkala untuk keamanan maksimal
                            </li>
                            <li class="mb-0">
                                <i class="fas fa-check text-success me-2"></i>
                                Jangan bagikan password kepada orang lain
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.requirements-list li {
    transition: all 0.3s ease;
}

.requirements-list li.valid {
    color: #28a745 !important;
}

.requirements-list li.valid i {
    color: #28a745;
}

.requirements-list li.valid i:before {
    content: "\f00c";
}

.password-strength {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.password-strength.show {
    opacity: 1;
}

.progress-bar {
    transition: width 0.3s ease, background-color 0.3s ease;
}
</style>

<script>
$(document).ready(function() {
    // Toggle password visibility
    window.togglePassword = function(fieldId) {
        const field = document.getElementById(fieldId);
        const icon = document.getElementById(fieldId + '_icon');
        
        if (field.type === 'password') {
            field.type = 'text';
            icon.classList.replace('fa-eye', 'fa-eye-slash');
        } else {
            field.type = 'password';
            icon.classList.replace('fa-eye-slash', 'fa-eye');
        }
    };
    
    // Password strength checker
    $('#new_password').on('input', function() {
        const password = $(this).val();
        checkPasswordStrength(password);
        checkPasswordRequirements(password);
    });
    
    // Password match checker
    $('#confirm_password').on('input', function() {
        const newPassword = $('#new_password').val();
        const confirmPassword = $(this).val();
        const matchDiv = $('#password-match');
        
        if (confirmPassword.length > 0) {
            if (newPassword === confirmPassword) {
                matchDiv.html('<small class="text-success"><i class="fas fa-check"></i> Password cocok</small>');
                $(this).removeClass('is-invalid').addClass('is-valid');
            } else {
                matchDiv.html('<small class="text-danger"><i class="fas fa-times"></i> Password tidak cocok</small>');
                $(this).removeClass('is-valid').addClass('is-invalid');
            }
        } else {
            matchDiv.html('');
            $(this).removeClass('is-invalid is-valid');
        }
    });
    
    function checkPasswordStrength(password) {
        const strengthBar = $('#strength-bar');
        const strengthText = $('#strength-text');
        const strengthContainer = $('.password-strength');
        
        if (password.length === 0) {
            strengthContainer.removeClass('show');
            return;
        }
        
        strengthContainer.addClass('show');
        
        let score = 0;
        let feedback = '';
        
        // Length check
        if (password.length >= 6) score += 1;
        if (password.length >= 8) score += 1;
        
        // Character variety
        if (/[a-z]/.test(password)) score += 1;
        if (/[A-Z]/.test(password)) score += 1;
        if (/[0-9]/.test(password)) score += 1;
        if (/[^A-Za-z0-9]/.test(password)) score += 1;
        
        // Set strength level
        if (score < 2) {
            strengthBar.css('width', '25%').removeClass().addClass('progress-bar bg-danger');
            feedback = 'Sangat Lemah';
        } else if (score < 3) {
            strengthBar.css('width', '50%').removeClass().addClass('progress-bar bg-warning');
            feedback = 'Lemah';
        } else if (score < 5) {
            strengthBar.css('width', '75%').removeClass().addClass('progress-bar bg-info');
            feedback = 'Sedang';
        } else {
            strengthBar.css('width', '100%').removeClass().addClass('progress-bar bg-success');
            feedback = 'Kuat';
        }
        
        strengthText.text(feedback);
    }
    
    function checkPasswordRequirements(password) {
        // Length check
        if (password.length >= 6) {
            $('#req-length').addClass('valid text-success').removeClass('text-muted');
        } else {
            $('#req-length').removeClass('valid text-success').addClass('text-muted');
        }
        
        // Uppercase check
        if (/[A-Z]/.test(password)) {
            $('#req-uppercase').addClass('valid text-success').removeClass('text-muted');
        } else {
            $('#req-uppercase').removeClass('valid text-success').addClass('text-muted');
        }
        
        // Lowercase check
        if (/[a-z]/.test(password)) {
            $('#req-lowercase').addClass('valid text-success').removeClass('text-muted');
        } else {
            $('#req-lowercase').removeClass('valid text-success').addClass('text-muted');
        }
        
        // Number check
        if (/[0-9]/.test(password)) {
            $('#req-number').addClass('valid text-success').removeClass('text-muted');
        } else {
            $('#req-number').removeClass('valid text-success').addClass('text-muted');
        }
        
        // Special character check
        if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
            $('#req-special').addClass('valid text-success').removeClass('text-muted');
        } else {
            $('#req-special').removeClass('valid text-success').addClass('text-muted');
        }
    }
    
    // Form validation
    $('#changePasswordForm').on('submit', function(e) {
        let isValid = true;
        
        // Reset validation
        $('.form-control').removeClass('is-invalid');
        
        const currentPassword = $('#current_password').val();
        const newPassword = $('#new_password').val();
        const confirmPassword = $('#confirm_password').val();
        
        // Validate current password
        if (currentPassword.trim() === '') {
            $('#current_password').addClass('is-invalid');
            isValid = false;
        }
        
        // Validate new password
        if (newPassword.length < 6) {
            $('#new_password').addClass('is-invalid');
            isValid = false;
        }
        
        // Validate password match
        if (newPassword !== confirmPassword) {
            $('#confirm_password').addClass('is-invalid');
            isValid = false;
        }
        
        if (!isValid) {
            e.preventDefault();
            return false;
        }
        
        // Show loading state
        $('#submitBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Mengubah...');
    });
});
</script>