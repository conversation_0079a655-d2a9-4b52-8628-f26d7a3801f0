 <?php if (isset($page) && $page == 'login'): ?>
        <!-- Login page tidak perlu footer -->
    <?php else: ?>
        </main> <!-- Close main-content -->
    <?php endif; ?>
    
    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="<?php echo base_url('assets/js/script.js'); ?>"></script>
    
    <?php if (isset($extra_js)): ?>
        <?php foreach ($extra_js as $js): ?>
            <script src="<?php echo base_url($js); ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
    
    <?php if (isset($inline_js)): ?>
        <script>
            <?php echo $inline_js; ?>
        </script>
    <?php endif; ?>
    
    <!-- Flash Messages -->
    <?php if ($this->session->flashdata('success')): ?>
        <script>
            showAlert('success', '<?php echo $this->session->flashdata('success'); ?>');
        </script>
    <?php endif; ?>
    
    <?php if ($this->session->flashdata('error')): ?>
        <script>
            showAlert('error', '<?php echo $this->session->flashdata('error'); ?>');
        </script>
    <?php endif; ?>
    
    <?php if ($this->session->flashdata('warning')): ?>
        <script>
            showAlert('warning', '<?php echo $this->session->flashdata('warning'); ?>');
        </script>
    <?php endif; ?>
    
    <?php if ($this->session->flashdata('info')): ?>
        <script>
            showAlert('info', '<?php echo $this->session->flashdata('info'); ?>');
        </script>
    <?php endif; ?>
    
    <script>
        // Sidebar toggle function
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('main-content');
            
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');
        }
        
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                alert.style.display = 'none';
            });
        }, 5000);
    </script>
    
</body>
</html>