<?php
// Load header
$this->load->view('template/header');
?>

<div class="login-container">
    <div class="login-box">
        <div class="login-header">
            <div class="logo">
                <i class="fas fa-table-tennis"></i>
            </div>
            <h2>Padel Booking System</h2>
            <p>Silakan login untuk melanjutkan</p>
        </div>
        
        <div class="login-body">
            <!-- Flash Messages -->
            <?php if ($this->session->flashdata('error')): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo $this->session->flashdata('error'); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($this->session->flashdata('success')): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo $this->session->flashdata('success'); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($this->session->flashdata('info')): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <?php echo $this->session->flashdata('info'); ?>
                </div>
            <?php endif; ?>
            
            <!-- Login Form -->
            <?php echo form_open('auth/process_login', array('id' => 'login-form')); ?>
                
                <div class="form-group">
                    <label for="username" class="form-label">
                        <i class="fas fa-user"></i>
                        Username
                    </label>
                    <input 
                        type="text" 
                        id="username" 
                        name="username" 
                        class="form-control <?php echo form_error('username') ? 'error' : ''; ?>"
                        value="<?php echo set_value('username'); ?>"
                        placeholder="Masukkan username"
                        required
                    >
                    <i class="form-icon fas fa-user"></i>
                    <?php if (form_error('username')): ?>
                        <div class="form-text error">
                            <?php echo form_error('username'); ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock"></i>
                        Password
                    </label>
                    <div class="password-input">
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            class="form-control <?php echo form_error('password') ? 'error' : ''; ?>"
                            placeholder="Masukkan password"
                            required
                        >
                        <i class="form-icon fas fa-lock"></i>
                        <button type="button" class="password-toggle" onclick="togglePassword()">
                            <i class="fas fa-eye" id="password-icon"></i>
                        </button>
                    </div>
                    <?php if (form_error('password')): ?>
                        <div class="form-text error">
                            <?php echo form_error('password'); ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="form-group">
                    <div class="form-check">
                        <input type="checkbox" id="remember" name="remember" class="form-check-input">
                        <label for="remember" class="form-check-label">
                            Ingat saya
                        </label>
                    </div>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary btn-block" id="login-btn">
                        <i class="fas fa-sign-in-alt"></i>
                        Masuk
                    </button>
                </div>
                
            <?php echo form_close(); ?>
            
            <!-- Demo Accounts Info -->
            <div class="demo-accounts">
                <h4>Akun Demo:</h4>
                <div class="demo-list">
                    <div class="demo-item">
                        <strong>Admin:</strong> admin / 123456
                    </div>
                    <div class="demo-item">
                        <strong>Kasir:</strong> kasir / 123456
                    </div>
                    <div class="demo-item">
                        <strong>Pimpinan:</strong> pimpinan / 123456
                    </div>
                </div>
            </div>
        </div>
        
        <div class="login-footer">
            <p>&copy; 2024 Padel Booking System. All rights reserved.</p>
        </div>
    </div>
</div>

<style>
/* Login Page Specific Styles */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
}

.login-box {
    background: white;
    border-radius: 10px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 400px;
    overflow: hidden;
}

.login-header {
    text-align: center;
    padding: 40px 40px 20px;
}

.login-header .logo {
    font-size: 4rem;
    color: #3498db;
    margin-bottom: 20px;
}

.login-header h2 {
    margin: 0 0 10px;
    color: #333;
    font-size: 1.8rem;
}

.login-header p {
    color: #666;
    margin: 0;
}

.login-body {
    padding: 20px 40px 40px;
}

.form-group {
    margin-bottom: 20px;
    position: relative;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
}

.form-label i {
    margin-right: 8px;
    color: #3498db;
}

.form-control {
    width: 100%;
    padding: 12px 15px 12px 45px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
}

.form-control:focus {
    outline: none;
    border-color: #3498db;
    background-color: white;
    box-shadow: 0 0 0 3px rgba(52,152,219,0.1);
}

.form-control.error {
    border-color: #e74c3c;
    background-color: #fdf2f2;
}

.form-icon {
    position: absolute;
    left: 15px;
    top: 38px;
    color: #999;
    font-size: 14px;
}

.password-input {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 15px;
    top: 12px;
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    font-size: 14px;
    padding: 0;
}

.password-toggle:hover {
    color: #3498db;
}

.form-check {
    display: flex;
    align-items: center;
}

.form-check-input {
    margin-right: 8px;
}

.form-check-label {
    color: #666;
    font-size: 14px;
    cursor: pointer;
}

.btn-block {
    width: 100%;
    padding: 12px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52,152,219,0.3);
}

.btn-primary:active {
    transform: translateY(0);
}

.alert {
    padding: 12px 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: none;
}

.alert i {
    margin-right: 8px;
}

.alert-danger {
    background-color: #fdf2f2;
    color: #e74c3c;
    border: 1px solid #fadbd8;
}

.alert-success {
    background-color: #f0fff4;
    color: #27ae60;
    border: 1px solid #d5f4e6;
}

.alert-info {
    background-color: #f0f8ff;
    color: #3498db;
    border: 1px solid #d6eaf8;
}

.form-text.error {
    color: #e74c3c;
    font-size: 12px;
    margin-top: 5px;
    display: block;
}

.demo-accounts {
    margin-top: 30px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.demo-accounts h4 {
    margin: 0 0 15px;
    color: #555;
    font-size: 14px;
    text-align: center;
}

.demo-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.demo-item {
    font-size: 12px;
    color: #666;
    text-align: center;
    padding: 5px;
    background: white;
    border-radius: 4px;
}

.demo-item strong {
    color: #333;
}

.login-footer {
    background-color: #f8f9fa;
    padding: 20px;
    text-align: center;
    border-top: 1px solid #e9ecef;
}

.login-footer p {
    margin: 0;
    color: #666;
    font-size: 12px;
}

/* Loading state */
.btn[disabled] {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Responsive */
@media (max-width: 480px) {
    .login-container {
        padding: 10px;
    }
    
    .login-box {
        max-width: 100%;
    }
    
    .login-header,
    .login-body {
        padding: 20px;
    }
    
    .demo-list {
        gap: 5px;
    }
    
    .demo-item {
        font-size: 11px;
    }
}
</style>

<script>
// Password toggle function
function togglePassword() {
    const passwordField = document.getElementById('password');
    const passwordIcon = document.getElementById('password-icon');
    
    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        passwordIcon.classList.remove('fa-eye');
        passwordIcon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        passwordIcon.classList.remove('fa-eye-slash');
        passwordIcon.classList.add('fa-eye');
    }
}

// Form submission with loading state
document.getElementById('login-form').addEventListener('submit', function(e) {
    const submitBtn = document.getElementById('login-btn');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Memproses...';
});

// Auto-focus on username field
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('username').focus();
});

// Demo account quick fill
document.addEventListener('DOMContentLoaded', function() {
    const demoItems = document.querySelectorAll('.demo-item');
    
    demoItems.forEach(item => {
        item.style.cursor = 'pointer';
        item.addEventListener('click', function() {
            const text = this.textContent;
            const parts = text.split(' / ');
            if (parts.length === 2) {
                const username = parts[0].split(': ')[1];
                const password = parts[1];
                
                document.getElementById('username').value = username;
                document.getElementById('password').value = password;
            }
        });
    });
});
</script>

<?php
// Load footer
$this->load->view('template/footer');
?>