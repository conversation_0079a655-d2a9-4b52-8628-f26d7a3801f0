<!-- <PERSON> Header -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">
                <i class="fas fa-tachometer-alt"></i>
                Dashboard Admin
            </h1>
            <p class="page-subtitle">Selamat datang, <?php echo $this->session->userdata('full_name'); ?>!</p>
        </div>
        <div class="page-actions">
            <button class="btn btn-primary" onclick="location.reload()">
                <i class="fas fa-sync-alt"></i>
                Refresh Data
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-calendar-check text-primary"></i>
        </div>
        <div class="stat-number">
            <?php echo isset($stats['total_bookings']) ? $stats['total_bookings'] : 0; ?>
        </div>
        <div class="stat-label">Total Booking</div>
    </div>

    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-clock text-warning"></i>
        </div>
        <div class="stat-number">
            <?php echo isset($stats['today_bookings']) ? $stats['today_bookings'] : 0; ?>
        </div>
        <div class="stat-label">Booking Hari Ini</div>
        <div class="stat-change">
            <i class="fas fa-calendar-day"></i>
            <?php echo date('d M Y'); ?>
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-hourglass-half text-danger"></i>
        </div>
        <div class="stat-number">
            <?php echo isset($stats['pending_bookings']) ? $stats['pending_bookings'] : 0; ?>
        </div>
        <div class="stat-label">Pending</div>
    </div>

    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-money-bill-wave text-success"></i>
        </div>
        <div class="stat-number">
            Rp <?php echo number_format(isset($stats['monthly_revenue']) ? $stats['monthly_revenue'] : 0, 0, ',', '.'); ?>
        </div>
        <div class="stat-label">Pendapatan Bulan Ini</div>
    </div>

    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-users text-info"></i>
        </div>
        <div class="stat-number">
            <?php echo isset($stats['total_users']) ? $stats['total_users'] : 0; ?>
        </div>
        <div class="stat-label">Total Pengguna</div>
    </div>

    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-map-marked-alt text-primary"></i>
        </div>
        <div class="stat-number">
            <?php echo isset($stats['active_courts']) ? $stats['active_courts'] : 0; ?>
        </div>
        <div class="stat-label">Lapangan Aktif</div>
    </div>
</div>

<!-- Main Content -->
<div class="row">
    <!-- Quick Actions -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3>
                    <i class="fas fa-bolt"></i>
                    Aksi Cepat
                </h3>
            </div>
            <div class="card-body">
                <div class="quick-actions-grid">
                    <a href="<?php echo site_url('booking/create'); ?>" class="quick-action-item">
                        <div class="quick-action-icon">
                            <i class="fas fa-plus-circle"></i>
                        </div>
                        <div class="quick-action-label">Buat Booking</div>
                    </a>

                    <a href="<?php echo site_url('users/create'); ?>" class="quick-action-item">
                        <div class="quick-action-icon">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="quick-action-label">Tambah User</div>
                    </a>

                    <a href="<?php echo site_url('lapangan/create'); ?>" class="quick-action-item">
                        <div class="quick-action-icon">
                            <i class="fas fa-map-marked-alt"></i>
                        </div>
                        <div class="quick-action-label">Tambah Lapangan</div>
                    </a>

                    <a href="<?php echo site_url('booking'); ?>" class="quick-action-item">
                        <div class="quick-action-icon">
                            <i class="fas fa-list"></i>
                        </div>
                        <div class="quick-action-label">Daftar Booking</div>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- System Info -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3>
                    <i class="fas fa-info-circle"></i>
                    Informasi Sistem
                </h3>
            </div>
            <div class="card-body">
                <div class="system-info">
                    <div class="info-item">
                        <div class="info-label">Status Sistem:</div>
                        <div class="info-value">
                            <span class="badge badge-success">
                                <i class="fas fa-check"></i>
                                Online
                            </span>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">Database:</div>
                        <div class="info-value">
                            <span class="badge badge-success">
                                <i class="fas fa-database"></i>
                                Terhubung
                            </span>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">Versi PHP:</div>
                        <div class="info-value"><?php echo phpversion(); ?></div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">CodeIgniter:</div>
                        <div class="info-value">v<?php echo CI_VERSION; ?></div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">Server Time:</div>
                        <div class="info-value"><?php echo date('d M Y H:i:s'); ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Navigation Cards -->
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card nav-card">
            <div class="card-body text-center">
                <i class="fas fa-users fa-3x text-primary mb-3"></i>
                <h5>Manajemen Pengguna</h5>
                <p class="text-muted">Kelola pengguna sistem</p>
                <a href="<?php echo site_url('users'); ?>" class="btn btn-primary">
                    Kelola Pengguna
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card nav-card">
            <div class="card-body text-center">
                <i class="fas fa-map-marked-alt fa-3x text-success mb-3"></i>
                <h5>Manajemen Lapangan</h5>
                <p class="text-muted">Kelola lapangan padel</p>
                <a href="<?php echo site_url('lapangan'); ?>" class="btn btn-success">
                    Kelola Lapangan
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card nav-card">
            <div class="card-body text-center">
                <i class="fas fa-calendar-alt fa-3x text-warning mb-3"></i>
                <h5>Manajemen Booking</h5>
                <p class="text-muted">Kelola booking pelanggan</p>
                <a href="<?php echo site_url('booking'); ?>" class="btn btn-warning">
                    Kelola Booking
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.page-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
}

.page-title {
    margin: 0;
    color: #2c3e50;
    font-size: 2rem;
    font-weight: 600;
}

.page-subtitle {
    margin: 5px 0 0 0;
    color: #6c757d;
    font-size: 1rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 10px;
}

.stat-label {
    color: #6c757d;
    font-size: 14px;
    font-weight: 500;
}

.stat-change {
    margin-top: 10px;
    font-size: 12px;
    color: #6c757d;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.quick-action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    text-decoration: none;
    color: #495057;
    transition: all 0.3s ease;
}

.quick-action-item:hover {
    background: #3498db;
    color: white;
    text-decoration: none;
    transform: translateY(-3px);
}

.quick-action-icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.quick-action-label {
    font-size: 14px;
    font-weight: 600;
    text-align: center;
}

.system-info .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f1f1f1;
}

.system-info .info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: #495057;
}

.info-value {
    color: #6c757d;
}

.nav-card {
    transition: all 0.3s ease;
}

.nav-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .quick-actions-grid {
        grid-template-columns: 1fr;
    }
}
</style>