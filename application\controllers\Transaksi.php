<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Transaksi extends CI_Controller {

    public function __construct() {
        parent::__construct();

        // Check if user is logged in
        if (!$this->session->userdata('user_id')) {
            redirect('auth/login');
        }

        // Load required models and libraries
        $this->load->model('Transaksi_model');
        $this->load->model('Booking_model');
        $this->load->model('User_model');
        $this->load->library('form_validation');
        $this->load->helper('url');

        // Check role access - admin dan kasir bisa akses transaksi
        $allowed_roles = array('admin', 'kasir');
        if (!in_array($this->session->userdata('role'), $allowed_roles)) {
            show_error('Access denied. You do not have permission to access this page.', 403, 'Access Denied');
        }
    }

    public function index() {
        $data['title'] = 'Pembayaran Booking';
        
        // Get booking ID from URL parameter if exists
        $booking_id = $this->input->get('booking_id');
        
        // Get confirmed bookings that haven't been paid
        $data['pending_payments'] = $this->Booking_model->get_confirmed_unpaid_bookings();
        
        // If specific booking ID is provided, get that booking detail
        if ($booking_id) {
            $booking = $this->Booking_model->get_booking_detail($booking_id);
            if ($booking && $booking->status_booking == 'dikonfirmasi' && !$booking->transaksi_id) {
                $data['selected_booking'] = $booking;
            }
        }
        
        $this->load->view('template/header', $data);
        $this->load->view('transaksi/index', $data);
        $this->load->view('template/footer');
    }

    public function process() {
        try {
            // Validate form input
            $this->form_validation->set_rules('booking_id', 'Booking ID', 'required|numeric');
            $this->form_validation->set_rules('jumlah_bayar', 'Jumlah Bayar', 'required|numeric|greater_than[0]');
            $this->form_validation->set_rules('metode_pembayaran', 'Metode Pembayaran', 'required|in_list[tunai,transfer,kartu,qris,ovo,gopay,dana]');

            if ($this->form_validation->run() == FALSE) {
                $this->session->set_flashdata('error', strip_tags(validation_errors()));
                redirect('transaksi');
                return;
            }

            $booking_id = (int)$this->input->post('booking_id');
            $jumlah_bayar = (float)$this->input->post('jumlah_bayar');
            $metode_pembayaran = $this->input->post('metode_pembayaran');
            $catatan = $this->input->post('catatan');

            // Get booking detail using the view
            $this->db->where('id', $booking_id);
            $booking = $this->db->get('v_booking_detail')->row();

            if (!$booking) {
                $this->session->set_flashdata('error', 'Booking tidak ditemukan');
                redirect('transaksi');
                return;
            }

            // Check if booking is confirmed and not paid yet
            if ($booking->status_booking != 'dikonfirmasi') {
                $this->session->set_flashdata('error', 'Booking harus dikonfirmasi terlebih dahulu');
                redirect('transaksi');
                return;
            }

            // Check if already paid
            if ($booking->kode_transaksi) {
                $this->session->set_flashdata('error', 'Booking sudah dibayar');
                redirect('transaksi');
                return;
            }

            // Validate payment amount
            if ($jumlah_bayar < $booking->harga_total) {
                $this->session->set_flashdata('error', 'Jumlah bayar tidak boleh kurang dari total harga: Rp ' . number_format($booking->harga_total, 0, ',', '.'));
                redirect('transaksi');
                return;
            }

            // Calculate change
            $kembalian = $jumlah_bayar - $booking->harga_total;

            // Generate transaction code
            $kode_transaksi = $this->_generate_transaction_code();

            // Prepare transaction data
            $transaksi_data = array(
                'booking_id' => $booking_id,
                'kode_transaksi' => $kode_transaksi,
                'total_harga' => $booking->harga_total,
                'jumlah_bayar' => $jumlah_bayar,
                'kembalian' => $kembalian,
                'metode_pembayaran' => $metode_pembayaran,
                'kasir_id' => $this->session->userdata('user_id'),
                'tanggal_transaksi' => date('Y-m-d H:i:s'),
                'catatan' => $catatan,
                'status_transaksi' => 'berhasil'
            );

            // Start database transaction
            $this->db->trans_begin();

            // Insert transaction
            $this->db->insert('transaksi', $transaksi_data);
            $transaksi_id = $this->db->insert_id();

            if ($transaksi_id) {
                // Update booking status to 'selesai'
                $this->db->where('id', $booking_id);
                $this->db->update('booking', array(
                    'status_booking' => 'selesai',
                    'updated_at' => date('Y-m-d H:i:s')
                ));

                // Update member statistics if member booking
                if ($booking->member_code) {
                    $this->db->where('member_code', $booking->member_code);
                    $this->db->set('total_bookings', 'total_bookings + 1', FALSE);
                    $this->db->set('total_spent', 'total_spent + ' . $booking->harga_total, FALSE);
                    $this->db->set('updated_at', date('Y-m-d H:i:s'));
                    $this->db->update('member_codes');
                }
            }

            if ($this->db->trans_status() === FALSE) {
                $this->db->trans_rollback();
                $this->session->set_flashdata('error', 'Gagal memproses pembayaran. Silakan coba lagi.');
                redirect('transaksi');
            } else {
                $this->db->trans_commit();
                $this->session->set_flashdata('success', 'Pembayaran berhasil diproses!');
                redirect('transaksi/receipt/' . $transaksi_id);
            }

        } catch (Exception $e) {
            $this->db->trans_rollback();
            log_message('error', 'Transaction process error: ' . $e->getMessage());
            $this->session->set_flashdata('error', 'Terjadi kesalahan sistem. Silakan coba lagi.');
            redirect('transaksi');
        }
    }

    public function receipt($transaksi_id) {
        $data['title'] = 'Struk Pembayaran';
        
        // Get transaction detail
        $data['transaksi'] = $this->Transaksi_model->get_transaksi_detail($transaksi_id);
        
        if (!$data['transaksi']) {
            show_404();
        }
        
        $this->load->view('template/header', $data);
        $this->load->view('transaksi/receipt', $data);
        $this->load->view('template/footer');
    }

    public function riwayat() {
        $data['title'] = 'Riwayat Transaksi';
        
        // Get filter parameters
        $filter_tanggal = $this->input->get('tanggal');
        $filter_metode = $this->input->get('metode');
        $filter_kasir = $this->input->get('kasir');
        
        // Get transaction history with filters
        $data['transaksi_list'] = $this->Transaksi_model->get_transaksi_with_filters($filter_tanggal, $filter_metode, $filter_kasir);
        
        // Get kasir list for filter
        $this->load->model('User_model');
        $data['kasir_list'] = $this->User_model->get_users_by_role('kasir');
        
        // Calculate summary
        $data['summary'] = $this->Transaksi_model->get_transaksi_summary($filter_tanggal, $filter_metode, $filter_kasir);
        
        $this->load->view('template/header', $data);
        $this->load->view('transaksi/riwayat', $data);
        $this->load->view('template/footer');
    }

    public function print_receipt($transaksi_id) {
        $data['transaksi'] = $this->Transaksi_model->get_transaksi_detail($transaksi_id);
        
        if (!$data['transaksi']) {
            show_404();
        }
        
        $this->load->view('transaksi/print_receipt', $data);
    }

    // AJAX Methods
    public function get_booking_detail() {
        $booking_id = $this->input->post('booking_id');
        
        if (!$booking_id) {
            echo json_encode(array('success' => false, 'message' => 'Booking ID is required'));
            return;
        }
        
        $booking = $this->Booking_model->get_booking_detail($booking_id);
        
        if (!$booking) {
            echo json_encode(array('success' => false, 'message' => 'Booking not found'));
            return;
        }
        
        if ($booking->status_booking != 'dikonfirmasi') {
            echo json_encode(array('success' => false, 'message' => 'Booking must be confirmed first'));
            return;
        }
        
        if ($booking->transaksi_id) {
            echo json_encode(array('success' => false, 'message' => 'Booking already paid'));
            return;
        }
        
        echo json_encode(array(
            'success' => true,
            'data' => array(
                'kode_booking' => $booking->kode_booking,
                'customer_name' => $booking->customer_name,
                'customer_phone' => $booking->customer_phone,
                'nama_lapangan' => $booking->nama_lapangan,
                'tanggal_booking' => date('d F Y', strtotime($booking->tanggal_booking)),
                'jam_mulai' => $booking->jam_mulai,
                'jam_selesai' => $booking->jam_selesai,
                'durasi' => $booking->durasi,
                'harga_per_jam' => number_format($booking->harga_per_jam, 0, ',', '.'),
                'subtotal' => number_format($booking->subtotal, 0, ',', '.'),
                'diskon' => number_format($booking->diskon, 0, ',', '.'),
                'harga_total' => number_format($booking->harga_total, 0, ',', '.'),
                'harga_total_raw' => $booking->harga_total,
                'member_name' => $booking->member_name
            )
        ));
    }

    private function _generate_transaction_code() {
        $date = date('ymd');
        $last_transaction = $this->Transaksi_model->get_last_transaction_code($date);
        
        if ($last_transaction) {
            $last_number = intval(substr($last_transaction->kode_transaksi, -3));
            $new_number = str_pad($last_number + 1, 3, '0', STR_PAD_LEFT);
        } else {
            $new_number = '001';
        }
        
        return 'TRX' . $date . $new_number;
    }
}
