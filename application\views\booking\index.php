<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h1><i class="fas fa-calendar-alt"></i> Daftar Booking</h1>
            <p class="text-muted">Ke<PERSON>la semua booking lapangan padel</p>
        </div>
        <div class="col-auto">
            <?php if (in_array($this->session->userdata('role'), ['admin', 'kasir'])): ?>
                <a href="<?php echo site_url('booking/create'); ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Buat Booking Baru
                </a>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Filter Panel -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-filter"></i> Filter Booking
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" action="<?php echo site_url('booking'); ?>" class="row g-3" id="filterForm">
            <div class="col-md-3">
                <label for="tanggal" class="form-label">Tanggal</label>
                <input type="date" class="form-control" id="tanggal" name="tanggal" 
                       value="<?php echo $filter_tanggal; ?>">
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">Status</label>
                <select class="form-control" id="status" name="status">
                    <option value="">Semua Status</option>
                    <option value="pending" <?php echo ($filter_status == 'pending') ? 'selected' : ''; ?>>Pending</option>
                    <option value="dikonfirmasi" <?php echo ($filter_status == 'dikonfirmasi') ? 'selected' : ''; ?>>Dikonfirmasi</option>
                    <option value="selesai" <?php echo ($filter_status == 'selesai') ? 'selected' : ''; ?>>Selesai</option>
                    <option value="dibatalkan" <?php echo ($filter_status == 'dibatalkan') ? 'selected' : ''; ?>>Dibatalkan</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="lapangan" class="form-label">Lapangan</label>
                <select class="form-control" id="lapangan" name="lapangan">
                    <option value="">Semua Lapangan</option>
                    <?php foreach ($lapangan_list as $lapangan): ?>
                        <option value="<?php echo $lapangan->id; ?>" 
                                <?php echo ($filter_lapangan == $lapangan->id) ? 'selected' : ''; ?>>
                            <?php echo $lapangan->nama_lapangan; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-2">
                <label for="customer_type" class="form-label">Tipe Customer</label>
                <select class="form-control" id="customer_type" name="customer_type">
                    <option value="">Semua Tipe</option>
                    <option value="member" <?php echo ($filter_customer_type == 'member') ? 'selected' : ''; ?>>Member</option>
                    <option value="non_member" <?php echo ($filter_customer_type == 'non_member') ? 'selected' : ''; ?>>Non-Member</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="search" class="form-label">Pencarian</label>
                <div class="input-group">
                    <input type="text" class="form-control" id="search" name="search" 
                           placeholder="Cari nama, telepon, kode booking..."
                           value="<?php echo $this->input->get('search'); ?>">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
            <div class="col-12">
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter"></i> Terapkan Filter
                    </button>
                    <a href="<?php echo site_url('booking'); ?>" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Reset
                    </a>
                    <?php if (in_array($this->session->userdata('role'), ['admin', 'pimpinan'])): ?>
                        <button type="button" class="btn btn-success" onclick="exportData()">
                            <i class="fas fa-file-excel"></i> Export Excel
                        </button>
                        <button type="button" class="btn btn-info" onclick="printData()">
                            <i class="fas fa-print"></i> Print
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3 class="mb-0"><?php echo count(array_filter($bookings, function($b) { return $b->status_booking == 'pending'; })); ?></h3>
                        <small>Pending</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3 class="mb-0"><?php echo count(array_filter($bookings, function($b) { return $b->status_booking == 'dikonfirmasi'; })); ?></h3>
                        <small>Dikonfirmasi</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3 class="mb-0"><?php echo count(array_filter($bookings, function($b) { return $b->status_booking == 'selesai'; })); ?></h3>
                        <small>Selesai</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-double fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-secondary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3 class="mb-0"><?php echo count($bookings); ?></h3>
                        <small>Total Booking</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Booking List -->
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list"></i> 
                    Daftar Booking 
                    <span class="badge bg-primary"><?php echo count($bookings); ?></span>
                </h5>
            </div>
            <div class="col-auto">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-cog"></i> Tampilan
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="javascript:void(0)" onclick="changeView('card')">
                                <i class="fas fa-th-large"></i> Card View
                            </a></li>
                            <li><a class="dropdown-item" href="javascript:void(0)" onclick="changeView('table')">
                                <i class="fas fa-table"></i> Table View
                            </a></li>
                            <li><a class="dropdown-item" href="javascript:void(0)" onclick="changeView('timeline')">
                                <i class="fas fa-clock"></i> Timeline View
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (empty($bookings)): ?>
            <div class="text-center py-5">
                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Tidak ada booking ditemukan</h5>
                <p class="text-muted">Belum ada booking yang sesuai dengan filter yang dipilih.</p>
                <?php if (in_array($this->session->userdata('role'), ['admin', 'kasir'])): ?>
                    <a href="<?php echo site_url('booking/create'); ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Buat Booking Pertama
                    </a>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <!-- Table View (Default) -->
            <div id="table-view">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="bookingTable">
                        <thead class="table-light">
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAll" onchange="toggleAllCheckbox()">
                                </th>
                                <th>Kode Booking</th>
                                <th>Tanggal & Waktu</th>
                                <th>Lapangan</th>
                                <th>Pemesan</th>
                                <th>Tipe</th>
                                <th>Harga</th>
                                <th>Status</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($bookings as $booking): ?>
                                <tr data-booking-id="<?php echo $booking->id; ?>">
                                    <td>
                                        <input type="checkbox" class="booking-checkbox" value="<?php echo $booking->id; ?>">
                                    </td>
                                    <td>
                                        <strong><?php echo $booking->kode_booking; ?></strong>
                                        <br>
                                        <small class="text-muted">
                                            <?php echo date('d M Y H:i', strtotime($booking->created_at)); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <strong><?php echo date('d M Y', strtotime($booking->tanggal_booking)); ?></strong>
                                        <br>
                                        <span class="text-muted">
                                            <?php echo date('H:i', strtotime($booking->jam_mulai)); ?> - 
                                            <?php echo date('H:i', strtotime($booking->jam_selesai)); ?>
                                        </span>
                                        <br>
                                        <small class="text-info"><?php echo $booking->durasi_jam; ?> jam</small>
                                    </td>
                                    <td>
                                        <strong><?php echo $booking->nama_lapangan; ?></strong>
                                        <br>
                                        <small class="text-muted">
                                            Rp <?php echo number_format($booking->harga_per_jam, 0, ',', '.'); ?>/jam
                                        </small>
                                    </td>
                                    <td>
                                        <strong><?php echo $booking->nama_pemesan; ?></strong>
                                        <br>
                                        <span class="text-muted"><?php echo $booking->telepon_pemesan; ?></span>
                                        <?php if ($booking->member_code): ?>
                                            <br>
                                            <span class="badge bg-info"><?php echo $booking->member_code; ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($booking->customer_type == 'member'): ?>
                                            <span class="badge bg-success">
                                                <i class="fas fa-star"></i> Member
                                            </span>
                                            <?php if ($booking->discount_percent > 0): ?>
                                                <br>
                                                <small class="text-success">
                                                    Diskon <?php echo $booking->discount_percent; ?>%
                                                </small>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">
                                                <i class="fas fa-user"></i> Non-Member
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($booking->price_before_discount && $booking->discount_percent > 0): ?>
                                            <small class="text-muted text-decoration-line-through">
                                                Rp <?php echo number_format($booking->price_before_discount, 0, ',', '.'); ?>
                                            </small>
                                            <br>
                                        <?php endif; ?>
                                        <strong class="text-success">
                                            Rp <?php echo number_format($booking->harga_total, 0, ',', '.'); ?>
                                        </strong>
                                    </td>
                                    <td>
                                        <?php
                                        $status_class = '';
                                        $status_icon = '';
                                        switch ($booking->status_booking) {
                                            case 'pending':
                                                $status_class = 'bg-warning text-dark';
                                                $status_icon = 'fas fa-clock';
                                                break;
                                            case 'dikonfirmasi':
                                                $status_class = 'bg-info';
                                                $status_icon = 'fas fa-check';
                                                break;
                                            case 'selesai':
                                                $status_class = 'bg-success';
                                                $status_icon = 'fas fa-check-double';
                                                break;
                                            case 'dibatalkan':
                                                $status_class = 'bg-danger';
                                                $status_icon = 'fas fa-times';
                                                break;
                                        }
                                        ?>
                                        <span class="badge <?php echo $status_class; ?>">
                                            <i class="<?php echo $status_icon; ?>"></i>
                                            <?php echo ucfirst($booking->status_booking); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <!-- View Button -->
                                            <a href="<?php echo site_url('booking/view/' . $booking->id); ?>" 
                                               class="btn btn-outline-primary" title="Lihat Detail">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            
                                            <?php if (in_array($this->session->userdata('role'), ['admin', 'kasir'])): ?>
                                                <?php if ($booking->status_booking == 'pending'): ?>
                                                    <!-- Edit Button -->
                                                    <a href="<?php echo site_url('booking/edit/' . $booking->id); ?>" 
                                                       class="btn btn-outline-warning" title="Edit Booking">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    
                                                    <!-- Delete Button -->
                                                    <button type="button" class="btn btn-outline-danger" 
                                                            onclick="deleteBooking(<?php echo $booking->id; ?>)" 
                                                            title="Hapus Booking">
                                                        <i class="fas fa-trash-alt"></i>
                                                    </button>
                                                <?php endif; ?>
                                                
                                                <!-- Quick Status Actions -->
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-outline-secondary dropdown-toggle" 
                                                            data-bs-toggle="dropdown" title="Ubah Status">
                                                        <i class="fas fa-tasks"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <?php if ($booking->status_booking != 'dikonfirmasi'): ?>
                                                            <li>
                                                                <a class="dropdown-item" href="javascript:void(0)" 
                                                                   onclick="updateStatus(<?php echo $booking->id; ?>, 'dikonfirmasi')">
                                                                    <i class="fas fa-check-circle text-info me-2"></i> 
                                                                    <span>Konfirmasi</span>
                                                                </a>
                                                            </li>
                                                        <?php endif; ?>
                                                        <?php if ($booking->status_booking != 'selesai'): ?>
                                                            <li>
                                                                <a class="dropdown-item" href="javascript:void(0)" 
                                                                   onclick="updateStatus(<?php echo $booking->id; ?>, 'selesai')">
                                                                    <i class="fas fa-check-double text-success me-2"></i> 
                                                                    <span>Selesai</span>
                                                                </a>
                                                            </li>
                                                        <?php endif; ?>
                                                        <?php if ($booking->status_booking != 'dibatalkan'): ?>
                                                            <li><hr class="dropdown-divider"></li>
                                                            <li>
                                                                <a class="dropdown-item text-danger" href="javascript:void(0)" 
                                                                   onclick="updateStatus(<?php echo $booking->id; ?>, 'dibatalkan')">
                                                                    <i class="fas fa-ban text-danger me-2"></i> 
                                                                    <span>Batalkan</span>
                                                                </a>
                                                            </li>
                                                        <?php endif; ?>
                                                        
                                                        <?php if ($booking->status_booking == 'dikonfirmasi'): ?>
                                                            <li><hr class="dropdown-divider"></li>
                                                            <li>
                                                                <a class="dropdown-item" href="<?php echo site_url('transaksi?booking_id=' . $booking->id); ?>">
                                                                    <i class="fas fa-cash-register text-primary me-2"></i> 
                                                                    <span>Proses Pembayaran</span>
                                                                </a>
                                                            </li>
                                                        <?php endif; ?>
                                                    </ul>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- Card View (Hidden by default) -->
            <div id="card-view" style="display: none;">
                <div class="row">
                    <?php foreach ($bookings as $booking): ?>
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card booking-card h-100">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0"><?php echo $booking->kode_booking; ?></h6>
                                    <?php
                                    $status_class = '';
                                    switch ($booking->status_booking) {
                                        case 'pending': $status_class = 'bg-warning text-dark'; break;
                                        case 'dikonfirmasi': $status_class = 'bg-info'; break;
                                        case 'selesai': $status_class = 'bg-success'; break;
                                        case 'dibatalkan': $status_class = 'bg-danger'; break;
                                    }
                                    ?>
                                    <span class="badge <?php echo $status_class; ?>">
                                        <?php echo ucfirst($booking->status_booking); ?>
                                    </span>
                                </div>
                                <div class="card-body">
                                    <h6 class="card-title"><?php echo $booking->nama_lapangan; ?></h6>
                                    <p class="card-text">
                                        <strong><?php echo $booking->nama_pemesan; ?></strong><br>
                                        <small class="text-muted"><?php echo $booking->telepon_pemesan; ?></small>
                                    </p>
                                    <p class="card-text">
                                        <i class="fas fa-calendar"></i> <?php echo date('d M Y', strtotime($booking->tanggal_booking)); ?><br>
                                        <i class="fas fa-clock"></i> <?php echo date('H:i', strtotime($booking->jam_mulai)); ?> - <?php echo date('H:i', strtotime($booking->jam_selesai)); ?>
                                    </p>
                                    <p class="card-text">
                                        <strong class="text-success">Rp <?php echo number_format($booking->harga_total, 0, ',', '.'); ?></strong>
                                    </p>
                                </div>
                                <div class="card-footer">
                                    <div class="btn-group w-100" role="group">
                                        <a href="<?php echo site_url('booking/view/' . $booking->id); ?>" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php if (in_array($this->session->userdata('role'), ['admin', 'kasir']) && $booking->status_booking == 'pending'): ?>
                                            <a href="<?php echo site_url('booking/edit/' . $booking->id); ?>" class="btn btn-sm btn-outline-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Bulk Actions (if items selected) -->
<div id="bulk-actions" class="card mt-3" style="display: none;">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col">
                <span id="selected-count">0</span> item dipilih
            </div>
            <div class="col-auto">
                <?php if (in_array($this->session->userdata('role'), ['admin', 'kasir'])): ?>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-info btn-sm" onclick="bulkUpdateStatus('dikonfirmasi')">
                            <i class="fas fa-check"></i> Konfirmasi Semua
                        </button>
                        <button type="button" class="btn btn-success btn-sm" onclick="bulkUpdateStatus('selesai')">
                            <i class="fas fa-check-double"></i> Selesai Semua
                        </button>
                        <button type="button" class="btn btn-danger btn-sm" onclick="bulkUpdateStatus('dibatalkan')">
                            <i class="fas fa-ban"></i> Batalkan Semua
                        </button>
                    </div>
                <?php endif; ?>
                <button type="button" class="btn btn-secondary btn-sm" onclick="clearSelection()">
                    <i class="fas fa-times"></i> Batal
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Status Update Modal -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <div class="modal-header bg-light border-0">
                <h5 class="modal-title d-flex align-items-center">
                    <div class="modal-icon me-3">
                        <i class="fas fa-exchange-alt text-primary"></i>
                    </div>
                    Konfirmasi Perubahan Status
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center py-4">
                <div class="status-icon mb-3">
                    <i id="statusIcon" class="fas fa-question-circle fa-3x text-warning"></i>
                </div>
                <p id="statusMessage" class="mb-0 fs-5">Apakah Anda yakin ingin mengubah status booking ini?</p>
            </div>
            <div class="modal-footer border-0 justify-content-center">
                <button type="button" class="btn btn-light px-4" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Batal
                </button>
                <button type="button" class="btn btn-primary px-4" id="confirmStatusUpdate">
                    <i class="fas fa-check me-2"></i>Ya, Ubah Status
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <div class="modal-header bg-light border-0">
                <h5 class="modal-title d-flex align-items-center">
                    <div class="modal-icon me-3">
                        <i class="fas fa-trash-alt text-danger"></i>
                    </div>
                    Konfirmasi Hapus Booking
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center py-4">
                <div class="delete-icon mb-3">
                    <i class="fas fa-exclamation-triangle fa-3x text-danger"></i>
                </div>
                <p class="mb-2 fs-5">Apakah Anda yakin ingin menghapus booking ini?</p>
                <div class="alert alert-warning mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Perhatian:</strong> Aksi ini tidak dapat dibatalkan!
                </div>
            </div>
            <div class="modal-footer border-0 justify-content-center">
                <button type="button" class="btn btn-light px-4" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Batal
                </button>
                <button type="button" class="btn btn-danger px-4" id="confirmDelete">
                    <i class="fas fa-trash-alt me-2"></i>Ya, Hapus
                </button>
            </div>
        </div>
    </div>
</div>

<style>
/* =============================================================================
   BOOKING PAGE SPECIFIC STYLES
   ============================================================================= */

/* Page Header */
.page-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e9ecef;
}

.page-header h1 {
    color: #2c3e50;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 4px;
}

.page-header h1 i {
    color: #3498db;
    margin-right: 8px;
}

.page-header p {
    color: #6c757d;
    font-size: 14px;
    margin: 0;
}

/* Filter Panel */
.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-bottom: 1px solid #e9ecef;
    padding: 16px 20px;
}

.card-header h5 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #495057;
}

.card-header h5 i {
    color: #3498db;
    margin-right: 6px;
}

/* Filter Form */
.form-row {
    margin-bottom: 0;
}

.form-group {
    margin-bottom: 16px;
}

.form-label {
    font-size: 12px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 4px;
}

.form-control {
    font-size: 13px;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.form-control:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52,152,219,0.2);
}

.input-group .btn {
    border-radius: 0 4px 4px 0;
    border-left: none;
}

/* Quick Stats Cards */
.card.bg-primary {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%) !important;
    border: none;
    box-shadow: 0 2px 8px rgba(52,152,219,0.3);
}

.card.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
    border: none;
    box-shadow: 0 2px 8px rgba(23,162,184,0.3);
}

.card.bg-success {
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%) !important;
    border: none;
    box-shadow: 0 2px 8px rgba(39,174,96,0.3);
}

.card.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
    border: none;
    box-shadow: 0 2px 8px rgba(108,117,125,0.3);
}

.card.bg-primary .card-body,
.card.bg-info .card-body,
.card.bg-success .card-body,
.card.bg-secondary .card-body {
    padding: 20px;
}

.card.bg-primary h3,
.card.bg-info h3,
.card.bg-success h3,
.card.bg-secondary h3 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 4px;
    line-height: 1;
}

.card.bg-primary small,
.card.bg-info small,
.card.bg-success small,
.card.bg-secondary small {
    font-size: 12px;
    opacity: 0.9;
    font-weight: 500;
}

.card.bg-primary i,
.card.bg-info i,
.card.bg-success i,
.card.bg-secondary i {
    opacity: 0.7;
}

/* Table Improvements */
.table {
    font-size: 12px;
}

.table th {
    font-weight: 600;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #dee2e6;
    font-size: 11px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #495057;
    padding: 12px 8px;
}

.table td {
    vertical-align: middle;
    padding: 10px 8px;
    border-bottom: 1px solid #e9ecef;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Action Column Styling */
.table td:last-child {
    width: 160px;
    min-width: 160px;
    text-align: center;
    padding: 8px 4px;
}

.table .btn-group {
    display: inline-flex;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.table .btn-group .btn {
    padding: 4px 6px;
    font-size: 10px;
    min-height: 26px;
    border: none;
    border-radius: 0;
    margin: 0;
    position: relative;
}

.table .btn-group .btn:first-child {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
}

.table .btn-group .btn:last-child {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

.table .btn-group .btn i {
    font-size: 9px;
    margin: 0;
}

.table .btn-group .btn:hover {
    transform: none;
    z-index: 2;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Dropdown in Action Column */
.table .btn-group .dropdown-toggle {
    padding: 4px 6px;
    font-size: 10px;
    min-height: 26px;
}

.table .dropdown-menu {
    font-size: 11px;
    min-width: 140px;
    padding: 2px 0;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border: 1px solid #e9ecef;
}

.table .dropdown-item {
    padding: 4px 8px;
    font-size: 11px;
    line-height: 1.4;
}

.table .dropdown-item i {
    width: 12px;
    margin-right: 4px;
    font-size: 9px;
}

/* Status Badges */
.badge {
    font-size: 9px;
    padding: 3px 6px;
    border-radius: 10px;
    font-weight: 600;
    letter-spacing: 0.3px;
    text-transform: uppercase;
}

.badge i {
    margin-right: 2px;
    font-size: 8px;
}

/* Card View Styling */
.booking-card {
    transition: all 0.2s ease;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    overflow: hidden;
}

.booking-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-color: #3498db;
}

.booking-card .card-header {
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.booking-card .card-body {
    padding: 16px;
    font-size: 12px;
}

.booking-card .card-footer {
    padding: 12px 16px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

/* Bulk Actions */
#bulk-actions {
    border-left: 4px solid #3498db;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

#bulk-actions .card-body {
    padding: 16px 20px;
}

/* Modal Enhancements */
.modal-content {
    border-radius: 8px;
    border: none;
    box-shadow: 0 8px 32px rgba(0,0,0,0.2);
}

.modal-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e9ecef;
}

.modal-body {
    padding: 20px 24px;
}

.modal-footer {
    padding: 16px 24px 20px;
    border-top: 1px solid #e9ecef;
}

.modal-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: rgba(52,152,219,0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.status-updating {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header h1 {
        font-size: 20px;
    }

    .table {
        font-size: 11px;
    }

    .table th,
    .table td {
        padding: 8px 4px;
    }

    .table td:last-child {
        width: 120px;
        min-width: 120px;
    }

    .table .btn-group .btn {
        padding: 3px 4px;
        font-size: 9px;
        min-height: 24px;
    }

    .table .btn-group .btn i {
        font-size: 8px;
    }

    .card.bg-primary h3,
    .card.bg-info h3,
    .card.bg-success h3,
    .card.bg-secondary h3 {
        font-size: 24px;
    }

    .booking-card .card-body {
        padding: 12px;
    }
}

/* Enhanced UI Effects */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(2px);
}

.spinner-container {
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #495057;
    font-size: 14px;
    font-weight: 500;
}

/* Enhanced Tooltips */
.enhanced-tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    max-width: 200px;
    word-wrap: break-word;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 9999;
}

.tooltip-content {
    position: relative;
    z-index: 1;
}

.tooltip-arrow {
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid rgba(0, 0, 0, 0.9);
}

/* Button Ripple Effect */
.btn {
    position: relative;
    overflow: hidden;
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Table Row Enhancements */
.table-row-hover {
    background-color: #f8f9fa !important;
    transform: scale(1.001);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.selected-row {
    background-color: #e3f2fd !important;
    border-left: 3px solid #3498db;
}

/* Dropdown Enhancements */
.dropdown-menu {
    animation: dropdown-fade-in 0.2s ease;
}

@keyframes dropdown-fade-in {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Search Results */
.search-results {
    animation: slide-down 0.3s ease;
    border-left: 4px solid #17a2b8;
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    border-radius: 6px;
    padding: 12px 16px;
    font-size: 12px;
}

@keyframes slide-down {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Card Animations */
.fade-in {
    animation: fade-in-up 0.5s ease;
}

@keyframes fade-in-up {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Smooth Transitions */
.card, .btn, .form-control, .table tbody tr {
    transition: all 0.2s ease;
}

/* Focus States */
.btn:focus,
.form-control:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.3);
}

/* Hover States */
.card:hover {
    transform: translateY(-2px);
}

.table tbody tr:hover {
    transform: translateX(2px);
}

/* Loading Button States */
.btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.btn.loading i {
    animation: spin 1s linear infinite;
}

/* Enhanced Form Controls */
.form-control:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
    transform: scale(1.01);
}

/* Status Badge Animations */
.badge {
    transition: all 0.2s ease;
}

.badge:hover {
    transform: scale(1.05);
}

/* Print Styles */
@media print {
    .btn, .btn-group, .page-header .col-auto, .card-header .col-auto,
    #bulk-actions, .modal, .loading-overlay, .enhanced-tooltip,
    .search-results {
        display: none !important;
    }

    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
        break-inside: avoid;
        transform: none !important;
    }

    .table th {
        background: #f0f0f0 !important;
        color: #000 !important;
    }

    .table td {
        border: 1px solid #000 !important;
    }

    .table tbody tr {
        transform: none !important;
    }
}
</style>

<script>
let currentBookingId = null;
let currentStatus = null;
let selectedBookings = [];

$(document).ready(function() {
    // Initialize enhanced UI components
    initializeEnhancedUI();

    // Initialize tooltips with better styling
    initializeTooltips();

    // Auto-submit filter form on change with loading state
    $('#filterForm select').change(function() {
        if ($(this).val() !== '') {
            showFormLoading();
            $('#filterForm').submit();
        }
    });

    // Enhanced search input handling
    $('#search').on('keypress', function(e) {
        if (e.which == 13) {
            showFormLoading();
            $('#filterForm').submit();
        }
    });

    // Debounced search for better UX
    let searchTimeout;
    $('#search').on('input', function() {
        clearTimeout(searchTimeout);
        const searchTerm = $(this).val();

        if (searchTerm.length >= 3) {
            searchTimeout = setTimeout(function() {
                performLiveSearch(searchTerm);
            }, 500);
        } else if (searchTerm.length === 0) {
            clearLiveSearch();
        }
    });

    // Enhanced checkbox handling
    $('.booking-checkbox').change(function() {
        updateSelectedBookings();
        animateCheckboxChange($(this));
    });

    // Initialize smooth scrolling for anchors
    $('a[href^="#"]').on('click', function(e) {
        e.preventDefault();
        const target = $($(this).attr('href'));
        if (target.length) {
            $('html, body').animate({
                scrollTop: target.offset().top - 100
            }, 500);
        }
    });

    // Initialize button loading states
    initializeButtonStates();

    // Initialize table enhancements
    initializeTableEnhancements();
});

// Enhanced UI initialization
function initializeEnhancedUI() {
    // Add loading overlay
    if (!$('#loading-overlay').length) {
        $('body').append(`
            <div id="loading-overlay" class="loading-overlay" style="display: none;">
                <div class="spinner-container">
                    <div class="spinner"></div>
                    <div class="loading-text">Memuat data...</div>
                </div>
            </div>
        `);
    }

    // Add smooth transitions to cards
    $('.card').addClass('fade-in');

    // Initialize button ripple effects
    $('.btn').on('click', function(e) {
        createRippleEffect(e, $(this));
    });
}

// Enhanced tooltips
function initializeTooltips() {
    $('[title]').each(function() {
        const $element = $(this);
        const title = $element.attr('title');

        if (title) {
            $element.removeAttr('title');
            $element.on('mouseenter', function(e) {
                showEnhancedTooltip(e, title);
            }).on('mouseleave', function() {
                hideEnhancedTooltip();
            });
        }
    });
}

// Show enhanced tooltip
function showEnhancedTooltip(e, text) {
    const tooltip = $(`
        <div class="enhanced-tooltip">
            <div class="tooltip-content">${text}</div>
            <div class="tooltip-arrow"></div>
        </div>
    `);

    $('body').append(tooltip);

    const tooltipWidth = tooltip.outerWidth();
    const tooltipHeight = tooltip.outerHeight();

    tooltip.css({
        position: 'absolute',
        top: e.pageY - tooltipHeight - 10,
        left: e.pageX - (tooltipWidth / 2),
        zIndex: 9999
    });

    tooltip.fadeIn(200);
}

// Hide enhanced tooltip
function hideEnhancedTooltip() {
    $('.enhanced-tooltip').fadeOut(200, function() {
        $(this).remove();
    });
}

// Show form loading state
function showFormLoading() {
    const submitBtn = $('#filterForm button[type="submit"]');
    const originalText = submitBtn.html();

    submitBtn.data('original-text', originalText);
    submitBtn.html('<i class="fas fa-spinner fa-spin"></i> Memuat...');
    submitBtn.prop('disabled', true);

    // Show loading overlay
    $('#loading-overlay').fadeIn(300);
}

// Animate checkbox changes
function animateCheckboxChange($checkbox) {
    const row = $checkbox.closest('tr');

    if ($checkbox.is(':checked')) {
        row.addClass('selected-row');
        row.find('td').animate({
            backgroundColor: '#e3f2fd'
        }, 200);
    } else {
        row.removeClass('selected-row');
        row.find('td').animate({
            backgroundColor: 'transparent'
        }, 200);
    }
}

// Create ripple effect for buttons
function createRippleEffect(e, $button) {
    const ripple = $('<span class="ripple"></span>');
    const rect = $button[0].getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = e.clientX - rect.left - size / 2;
    const y = e.clientY - rect.top - size / 2;

    ripple.css({
        width: size,
        height: size,
        left: x,
        top: y
    });

    $button.append(ripple);

    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// Initialize button states
function initializeButtonStates() {
    $('.btn').on('click', function() {
        const $btn = $(this);

        if (!$btn.hasClass('no-loading')) {
            const originalText = $btn.html();
            $btn.data('original-text', originalText);

            setTimeout(() => {
                if ($btn.data('original-text')) {
                    $btn.html($btn.data('original-text'));
                    $btn.prop('disabled', false);
                }
            }, 2000);
        }
    });
}

// Initialize table enhancements
function initializeTableEnhancements() {
    // Add hover effects to table rows
    $('.table tbody tr').on('mouseenter', function() {
        $(this).addClass('table-row-hover');
    }).on('mouseleave', function() {
        $(this).removeClass('table-row-hover');
    });

    // Initialize dropdown menus
    $('.dropdown-toggle').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const $dropdown = $(this).next('.dropdown-menu');
        const $allDropdowns = $('.dropdown-menu');

        // Close all other dropdowns
        $allDropdowns.not($dropdown).removeClass('show').fadeOut(200);

        // Toggle current dropdown
        if ($dropdown.hasClass('show')) {
            $dropdown.removeClass('show').fadeOut(200);
        } else {
            $dropdown.addClass('show').fadeIn(200);
        }
    });

    // Close dropdowns when clicking outside
    $(document).on('click', function() {
        $('.dropdown-menu.show').removeClass('show').fadeOut(200);
    });

    // Prevent dropdown from closing when clicking inside
    $('.dropdown-menu').on('click', function(e) {
        e.stopPropagation();
    });
}

// Live search functionality
function performLiveSearch(searchTerm) {
    const $rows = $('.table tbody tr');
    let visibleCount = 0;

    $rows.each(function() {
        const $row = $(this);
        const text = $row.text().toLowerCase();

        if (text.includes(searchTerm.toLowerCase())) {
            $row.fadeIn(200);
            visibleCount++;
        } else {
            $row.fadeOut(200);
        }
    });

    // Update result count
    updateSearchResults(visibleCount, $rows.length);
}

// Clear live search
function clearLiveSearch() {
    $('.table tbody tr').fadeIn(200);
    $('.search-results').remove();
}

// Update search results display
function updateSearchResults(visible, total) {
    $('.search-results').remove();

    const resultsHtml = `
        <div class="search-results alert alert-info mt-2">
            <i class="fas fa-info-circle"></i>
            Menampilkan ${visible} dari ${total} booking
        </div>
    `;

    $('#search').closest('.col-md-3').append(resultsHtml);
}

function updateStatus(bookingId, status) {
    currentBookingId = bookingId;
    currentStatus = status;
    
    const statusConfig = {
        'pending': {
            text: 'Pending',
            icon: 'fas fa-clock',
            color: 'text-warning'
        },
        'dikonfirmasi': {
            text: 'Dikonfirmasi',
            icon: 'fas fa-check-circle',
            color: 'text-info'
        },
        'selesai': {
            text: 'Selesai',
            icon: 'fas fa-check-double',
            color: 'text-success'
        },
        'dibatalkan': {
            text: 'Dibatalkan',
            icon: 'fas fa-ban',
            color: 'text-danger'
        }
    };
    
    const config = statusConfig[status];
    
    // Update icon and message
    const statusIcon = document.getElementById('statusIcon');
    statusIcon.className = `${config.icon} fa-3x ${config.color}`;
    
    document.getElementById('statusMessage').innerHTML = 
        `Apakah Anda yakin ingin mengubah status booking ini menjadi <strong class="${config.color}">${config.text}</strong>?`;
    
    new bootstrap.Modal(document.getElementById('statusModal')).show();
}

function deleteBooking(bookingId) {
    currentBookingId = bookingId;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

document.getElementById('confirmStatusUpdate').addEventListener('click', function() {
    if (currentBookingId && currentStatus) {
        // Show loading state
        const row = document.querySelector(`tr[data-booking-id="${currentBookingId}"]`);
        if (row) {
            row.classList.add('status-updating');
        }
        
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `<?php echo site_url('booking/update_status/'); ?>${currentBookingId}`;
        
        const statusInput = document.createElement('input');
        statusInput.type = 'hidden';
        statusInput.name = 'status';
        statusInput.value = currentStatus;
        
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '<?php echo $this->security->get_csrf_token_name(); ?>';
        csrfInput.value = '<?php echo $this->security->get_csrf_hash(); ?>';
        
        form.appendChild(statusInput);
        form.appendChild(csrfInput);
        document.body.appendChild(form);
        form.submit();
    }
});

document.getElementById('confirmDelete').addEventListener('click', function() {
    if (currentBookingId) {
        window.location.href = `<?php echo site_url('booking/delete/'); ?>${currentBookingId}`;
    }
});

// Toggle all checkboxes
function toggleAllCheckbox() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.booking-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateSelectedBookings();
}

// Update selected bookings array
function updateSelectedBookings() {
    selectedBookings = [];
    const checkboxes = document.querySelectorAll('.booking-checkbox:checked');
    
    checkboxes.forEach(checkbox => {
        selectedBookings.push(checkbox.value);
    });
    
    // Update bulk actions visibility
    const bulkActions = document.getElementById('bulk-actions');
    const selectedCount = document.getElementById('selected-count');
    
    if (selectedBookings.length > 0) {
        bulkActions.style.display = 'block';
        selectedCount.textContent = selectedBookings.length;
    } else {
        bulkActions.style.display = 'none';
    }
    
    // Update select all checkbox state
    const selectAll = document.getElementById('selectAll');
    const allCheckboxes = document.querySelectorAll('.booking-checkbox');
    
    if (selectedBookings.length === 0) {
        selectAll.indeterminate = false;
        selectAll.checked = false;
    } else if (selectedBookings.length === allCheckboxes.length) {
        selectAll.indeterminate = false;
        selectAll.checked = true;
    } else {
        selectAll.indeterminate = true;
    }
}

// Bulk status update
function bulkUpdateStatus(status) {
    if (selectedBookings.length === 0) {
        alert('Pilih booking yang ingin diupdate terlebih dahulu');
        return;
    }
    
    const statusText = {
        'dikonfirmasi': 'Dikonfirmasi',
        'selesai': 'Selesai',
        'dibatalkan': 'Dibatalkan'
    };
    
    if (confirm(`Apakah Anda yakin ingin mengubah status ${selectedBookings.length} booking menjadi ${statusText[status]}?`)) {
        // Create form for bulk update
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?php echo site_url('booking/bulk_update_status'); ?>';
        
        // Add booking IDs
        selectedBookings.forEach(id => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'booking_ids[]';
            input.value = id;
            form.appendChild(input);
        });
        
        // Add status
        const statusInput = document.createElement('input');
        statusInput.type = 'hidden';
        statusInput.name = 'status';
        statusInput.value = status;
        form.appendChild(statusInput);
        
        // Add CSRF
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '<?php echo $this->security->get_csrf_token_name(); ?>';
        csrfInput.value = '<?php echo $this->security->get_csrf_hash(); ?>';
        form.appendChild(csrfInput);
        
        document.body.appendChild(form);
        form.submit();
    }
}

// Clear selection
function clearSelection() {
    const checkboxes = document.querySelectorAll('.booking-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    
    document.getElementById('selectAll').checked = false;
    updateSelectedBookings();
}

// Change view
function changeView(viewType) {
    const tableView = document.getElementById('table-view');
    const cardView = document.getElementById('card-view');
    
    if (viewType === 'card') {
        tableView.style.display = 'none';
        cardView.style.display = 'block';
    } else if (viewType === 'table') {
        tableView.style.display = 'block';
        cardView.style.display = 'none';
    } else if (viewType === 'timeline') {
        // Timeline view implementation can be added here
        alert('Timeline view akan segera tersedia');
    }
    
    // Save preference to localStorage
    localStorage.setItem('bookingViewPreference', viewType);
}

// Refresh data
function refreshData() {
    const refreshBtn = document.querySelector('[onclick="refreshData()"]');
    const originalContent = refreshBtn.innerHTML;
    
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
    refreshBtn.disabled = true;
    
    // Reload page after short delay
    setTimeout(() => {
        window.location.reload();
    }, 1000);
}

// Export data
function exportData() {
    const currentUrl = new URL(window.location);
    currentUrl.searchParams.set('export', 'excel');
    window.open(currentUrl.toString(), '_blank');
}

// Print data
function printData() {
    window.print();
}

// Load saved view preference
document.addEventListener('DOMContentLoaded', function() {
    const savedView = localStorage.getItem('bookingViewPreference');
    if (savedView && savedView !== 'table') {
        changeView(savedView);
    }
});

// Auto-refresh every 2 minutes (only if no modal is open)
setInterval(function() {
    if (!document.querySelector('.modal.show')) {
        // Silent refresh - update counters only
        fetch(window.location.href)
            .then(response => response.text())
            .then(html => {
                const parser = new DOMParser();
                const newDoc = parser.parseFromString(html, 'text/html');
                
                // Update quick stats
                const statsCards = document.querySelectorAll('.card.bg-primary h3, .card.bg-info h3, .card.bg-success h3, .card.bg-secondary h3');
                const newStatsCards = newDoc.querySelectorAll('.card.bg-primary h3, .card.bg-info h3, .card.bg-success h3, .card.bg-secondary h3');
                
                statsCards.forEach((card, index) => {
                    if (newStatsCards[index]) {
                        card.textContent = newStatsCards[index].textContent;
                    }
                });
            })
            .catch(error => {
                console.log('Auto-refresh failed:', error);
            });
    }
}, 120000); // 2 minutes

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl + N = New booking
    if (e.ctrlKey && e.key === 'n') {
        e.preventDefault();
        window.location.href = '<?php echo site_url('booking/create'); ?>';
    }
    
    // Ctrl + R = Refresh
    if (e.ctrlKey && e.key === 'r') {
        e.preventDefault();
        refreshData();
    }
    
    // Escape = Clear selection
    if (e.key === 'Escape') {
        clearSelection();
    }
});
</script>