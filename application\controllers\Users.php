<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Users extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        
        // Check if user is logged in
        if (!$this->session->userdata('logged_in')) {
            redirect('auth/login');
        }
        
        // Check if user is admin
        if ($this->session->userdata('role') !== 'admin') {
            $this->session->set_flashdata('error', 'Anda tidak memiliki akses ke halaman ini.');
            redirect('dashboard');
        }
        
        // Load models and libraries
        $this->load->model('User_model');
        $this->load->library('form_validation');
        $this->load->library('pagination');
    }

    /**
     * Display users list
     */
    public function index()
    {
        $data['title'] = 'Manajemen Pengguna - Padel Booking System';
        $data['page'] = 'users_index';
        
        // Get search and filter parameters
        $search = $this->input->get('search');
        $role_filter = $this->input->get('role');
        $status_filter = $this->input->get('status');
        
        // Pagination configuration
        $config['base_url'] = site_url('users');
        $config['total_rows'] = $this->User_model->count_all_users($search, $role_filter, $status_filter);
        $config['per_page'] = 10;
        $config['uri_segment'] = 2;
        $config['reuse_query_string'] = TRUE;
        
        // Pagination styling
        $config['full_tag_open'] = '<nav><ul class="pagination justify-content-center">';
        $config['full_tag_close'] = '</ul></nav>';
        $config['first_link'] = 'First';
        $config['first_tag_open'] = '<li class="page-item">';
        $config['first_tag_close'] = '</li>';
        $config['last_link'] = 'Last';
        $config['last_tag_open'] = '<li class="page-item">';
        $config['last_tag_close'] = '</li>';
        $config['next_link'] = 'Next';
        $config['next_tag_open'] = '<li class="page-item">';
        $config['next_tag_close'] = '</li>';
        $config['prev_link'] = 'Previous';
        $config['prev_tag_open'] = '<li class="page-item">';
        $config['prev_tag_close'] = '</li>';
        $config['cur_tag_open'] = '<li class="page-item active"><a class="page-link">';
        $config['cur_tag_close'] = '</a></li>';
        $config['num_tag_open'] = '<li class="page-item">';
        $config['num_tag_close'] = '</li>';
        $config['attributes'] = array('class' => 'page-link');
        
        $this->pagination->initialize($config);
        
        // Get users data
        $offset = $this->uri->segment(2) ? $this->uri->segment(2) : 0;
        $data['users'] = $this->User_model->get_all_users($config['per_page'], $offset, $search, $role_filter, $status_filter);
        $data['pagination'] = $this->pagination->create_links();
        $data['total_records'] = $config['total_rows'];
        
        // Filter data for form
        $data['search'] = $search;
        $data['role_filter'] = $role_filter;
        $data['status_filter'] = $status_filter;
        
        // Load views
        $this->load->view('template/header', $data);
        $this->load->view('users/index', $data);
        $this->load->view('template/footer', $data);
    }

    /**
     * Show create user form
     */
    public function create()
    {
        $data['title'] = 'Tambah Pengguna - Padel Booking System';
        $data['page'] = 'users_create';
        $data['action'] = 'create';
        $data['user'] = null;
        
        // Load views
        $this->load->view('template/header', $data);
        $this->load->view('users/form', $data);
        $this->load->view('template/footer', $data);
    }

    /**
     * Store new user
     */
    public function store()
    {
        // Set validation rules
        $this->_set_validation_rules();
        
        if ($this->form_validation->run() == FALSE) {
            // Validation failed
            $this->create();
        } else {
            // Validation passed
            $data = array(
                'username' => $this->input->post('username'),
                'password' => $this->input->post('password'),
                'full_name' => $this->input->post('full_name'),
                'email' => $this->input->post('email'),
                'phone' => $this->input->post('phone'),
                'role' => $this->input->post('role'),
                'status' => $this->input->post('status')
            );
            
            if ($this->User_model->create_user($data)) {
                $this->session->set_flashdata('success', 'Pengguna berhasil ditambahkan.');
                redirect('users');
            } else {
                $this->session->set_flashdata('error', 'Gagal menambahkan pengguna.');
                $this->create();
            }
        }
    }

    /**
     * Show edit user form
     */
    public function edit($id)
    {
        $user = $this->User_model->get_user_by_id($id);
        
        if (!$user) {
            $this->session->set_flashdata('error', 'Pengguna tidak ditemukan.');
            redirect('users');
        }
        
        $data['title'] = 'Edit Pengguna - Padel Booking System';
        $data['page'] = 'users_edit';
        $data['action'] = 'edit';
        $data['user'] = $user;
        
        // Load views
        $this->load->view('template/header', $data);
        $this->load->view('users/form', $data);
        $this->load->view('template/footer', $data);
    }

    /**
     * Update user
     */
    public function update($id)
    {
        $user = $this->User_model->get_user_by_id($id);
        
        if (!$user) {
            $this->session->set_flashdata('error', 'Pengguna tidak ditemukan.');
            redirect('users');
        }
        
        // Set validation rules for update
        $this->_set_validation_rules($id);
        
        if ($this->form_validation->run() == FALSE) {
            // Validation failed
            $this->edit($id);
        } else {
            // Validation passed
            $data = array(
                'username' => $this->input->post('username'),
                'full_name' => $this->input->post('full_name'),
                'email' => $this->input->post('email'),
                'phone' => $this->input->post('phone'),
                'role' => $this->input->post('role'),
                'status' => $this->input->post('status')
            );
            
            // Add password if provided
            $password = $this->input->post('password');
            if (!empty($password)) {
                $data['password'] = $password;
            }
            
            if ($this->User_model->update_user($id, $data)) {
                $this->session->set_flashdata('success', 'Pengguna berhasil diperbarui.');
                redirect('users');
            } else {
                $this->session->set_flashdata('error', 'Gagal memperbarui pengguna.');
                $this->edit($id);
            }
        }
    }

    /**
     * Delete user
     */
    public function delete($id)
    {
        $user = $this->User_model->get_user_by_id($id);
        
        if (!$user) {
            $this->session->set_flashdata('error', 'Pengguna tidak ditemukan.');
            redirect('users');
        }
        
        // Check if user is trying to delete themselves
        if ($id == $this->session->userdata('user_id')) {
            $this->session->set_flashdata('error', 'Anda tidak dapat menghapus akun sendiri.');
            redirect('users');
        }
        
        // Check if user has related bookings
        if ($this->User_model->has_bookings($id)) {
            $this->session->set_flashdata('error', 'Tidak dapat menghapus pengguna yang memiliki riwayat booking.');
            redirect('users');
        }
        
        if ($this->User_model->delete_user($id)) {
            $this->session->set_flashdata('success', 'Pengguna berhasil dihapus.');
        } else {
            $this->session->set_flashdata('error', 'Gagal menghapus pengguna.');
        }
        
        redirect('users');
    }

    /**
     * Toggle user status
     */
    public function toggle_status($id)
    {
        $user = $this->User_model->get_user_by_id($id);
        
        if (!$user) {
            echo json_encode(array('success' => false, 'message' => 'Pengguna tidak ditemukan.'));
            return;
        }
        
        // Check if user is trying to deactivate themselves
        if ($id == $this->session->userdata('user_id')) {
            echo json_encode(array('success' => false, 'message' => 'Anda tidak dapat menonaktifkan akun sendiri.'));
            return;
        }
        
        if ($this->User_model->toggle_status($id)) {
            $new_status = ($user->status == 'aktif') ? 'tidak_aktif' : 'aktif';
            echo json_encode(array(
                'success' => true, 
                'message' => 'Status pengguna berhasil diubah.',
                'new_status' => $new_status
            ));
        } else {
            echo json_encode(array('success' => false, 'message' => 'Gagal mengubah status pengguna.'));
        }
    }

    /**
     * View user profile
     */
    public function profile()
    {
        $user_id = $this->session->userdata('user_id');
        $user = $this->User_model->get_user_by_id($user_id);
        
        if (!$user) {
            $this->session->set_flashdata('error', 'Data pengguna tidak ditemukan.');
            redirect('dashboard');
        }
        
        $data['title'] = 'Profil Saya - Padel Booking System';
        $data['page'] = 'user_profile';
        $data['user'] = $user;
        
        // Load views
        $this->load->view('template/header', $data);
        $this->load->view('users/profile', $data);
        $this->load->view('template/footer', $data);
    }

    /**
     * Update user profile
     */
    public function update_profile()
    {
        $user_id = $this->session->userdata('user_id');
        
        // Set validation rules for profile update
        $this->form_validation->set_rules('full_name', 'Nama Lengkap', 'required|trim');
        $this->form_validation->set_rules('email', 'Email', 'valid_email');
        $this->form_validation->set_rules('phone', 'Telepon', 'trim');
        
        if ($this->form_validation->run() == FALSE) {
            $this->profile();
        } else {
            $data = array(
                'full_name' => $this->input->post('full_name'),
                'email' => $this->input->post('email'),
                'phone' => $this->input->post('phone')
            );
            
            if ($this->User_model->update_user($user_id, $data)) {
                // Update session data
                $this->session->set_userdata('full_name', $data['full_name']);
                
                $this->session->set_flashdata('success', 'Profil berhasil diperbarui.');
            } else {
                $this->session->set_flashdata('error', 'Gagal memperbarui profil.');
            }
            
            redirect('users/profile');
        }
    }

    /**
     * Change password
     */
    public function change_password()
    {
        $user_id = $this->session->userdata('user_id');
        
        // Set validation rules
        $this->form_validation->set_rules('current_password', 'Password Saat Ini', 'required');
        $this->form_validation->set_rules('new_password', 'Password Baru', 'required|min_length[6]');
        $this->form_validation->set_rules('confirm_password', 'Konfirmasi Password', 'required|matches[new_password]');
        
        if ($this->form_validation->run() == FALSE) {
            $this->profile();
        } else {
            $current_password = $this->input->post('current_password');
            $new_password = $this->input->post('new_password');
            
            // Verify current password
            $user = $this->User_model->get_user_by_id($user_id);
            if (!password_verify($current_password, $user->password)) {
                $this->session->set_flashdata('error', 'Password saat ini salah.');
                redirect('users/profile');
            }
            
            // Update password
            $data = array('password' => $new_password);
            
            if ($this->User_model->update_user($user_id, $data)) {
                $this->session->set_flashdata('success', 'Password berhasil diubah.');
            } else {
                $this->session->set_flashdata('error', 'Gagal mengubah password.');
            }
            
            redirect('users/profile');
        }
    }

    /**
     * AJAX: Check if username exists
     */
    public function check_username()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $username = $this->input->post('username');
        $exclude_id = $this->input->post('exclude_id');
        
        $exists = $this->User_model->username_exists($username, $exclude_id);
        
        echo json_encode(array('exists' => $exists));
    }

    /**
     * AJAX: Check if email exists
     */
    public function check_email()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $email = $this->input->post('email');
        $exclude_id = $this->input->post('exclude_id');
        
        $exists = $this->User_model->email_exists($email, $exclude_id);
        
        echo json_encode(array('exists' => $exists));
    }

    /**
     * Export users to CSV
     */
    public function export_csv()
    {
        $users = $this->User_model->get_all_users();
        
        $filename = 'users_export_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // CSV headers
        fputcsv($output, array('ID', 'Username', 'Nama Lengkap', 'Email', 'Telepon', 'Role', 'Status', 'Dibuat', 'Diperbarui'));
        
        // CSV data
        foreach ($users as $user) {
            fputcsv($output, array(
                $user->id,
                $user->username,
                $user->full_name,
                $user->email,
                $user->phone,
                ucfirst($user->role),
                ucfirst($user->status),
                date('d/m/Y H:i', strtotime($user->created_at)),
                date('d/m/Y H:i', strtotime($user->updated_at))
            ));
        }
        
        fclose($output);
    }

    /**
     * Print users list
     */
    public function print_list()
    {
        $users = $this->User_model->get_all_users();
        
        $data['title'] = 'Daftar Pengguna';
        $data['users'] = $users;
        $data['print_date'] = date('d F Y H:i:s');
        
        $this->load->view('users/print', $data);
    }

    /**
     * Set validation rules
     */
    private function _set_validation_rules($user_id = null)
    {
        // Username validation
        if ($user_id) {
            $this->form_validation->set_rules('username', 'Username', 'required|trim|callback__check_username_unique[' . $user_id . ']');
        } else {
            $this->form_validation->set_rules('username', 'Username', 'required|trim|is_unique[users.username]');
        }
        
        // Password validation (required only for new users)
        if (!$user_id) {
            $this->form_validation->set_rules('password', 'Password', 'required|min_length[6]');
            $this->form_validation->set_rules('password_confirm', 'Konfirmasi Password', 'required|matches[password]');
        } else {
            $this->form_validation->set_rules('password', 'Password', 'min_length[6]');
            $this->form_validation->set_rules('password_confirm', 'Konfirmasi Password', 'matches[password]');
        }
        
        // Other fields
        $this->form_validation->set_rules('full_name', 'Nama Lengkap', 'required|trim');
        
        // Email validation
        if ($user_id) {
            $this->form_validation->set_rules('email', 'Email', 'valid_email|callback__check_email_unique[' . $user_id . ']');
        } else {
            $this->form_validation->set_rules('email', 'Email', 'valid_email|is_unique[users.email]');
        }
        
        $this->form_validation->set_rules('phone', 'Telepon', 'trim');
        $this->form_validation->set_rules('role', 'Role', 'required|in_list[admin,kasir,pimpinan]');
        $this->form_validation->set_rules('status', 'Status', 'required|in_list[aktif,tidak_aktif]');
        
        // Custom validation messages
        $this->form_validation->set_message('required', '{field} wajib diisi.');
        $this->form_validation->set_message('min_length', '{field} minimal {param} karakter.');
        $this->form_validation->set_message('matches', '{field} tidak sama.');
        $this->form_validation->set_message('is_unique', '{field} sudah digunakan.');
        $this->form_validation->set_message('valid_email', '{field} tidak valid.');
        $this->form_validation->set_message('in_list', '{field} tidak valid.');
    }

    /**
     * Custom validation callback for unique username
     */
    public function _check_username_unique($username, $user_id)
    {
        if ($this->User_model->username_exists($username, $user_id)) {
            $this->form_validation->set_message('_check_username_unique', 'Username sudah digunakan.');
            return FALSE;
        }
        return TRUE;
    }

    /**
     * Custom validation callback for unique email
     */
    public function _check_email_unique($email, $user_id)
    {
        if (!empty($email) && $this->User_model->email_exists($email, $user_id)) {
            $this->form_validation->set_message('_check_email_unique', 'Email sudah digunakan.');
            return FALSE;
        }
        return TRUE;
    }

    /**
     * Bulk actions
     */
    public function bulk_action()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $action = $this->input->post('action');
        $user_ids = $this->input->post('user_ids');
        
        if (empty($user_ids) || !is_array($user_ids)) {
            echo json_encode(array('success' => false, 'message' => 'Pilih pengguna terlebih dahulu.'));
            return;
        }
        
        $success_count = 0;
        $current_user_id = $this->session->userdata('user_id');
        
        foreach ($user_ids as $user_id) {
            // Skip current user
            if ($user_id == $current_user_id) {
                continue;
            }
            
            switch ($action) {
                case 'activate':
                    if ($this->User_model->update_user($user_id, array('status' => 'aktif'))) {
                        $success_count++;
                    }
                    break;
                    
                case 'deactivate':
                    if ($this->User_model->update_user($user_id, array('status' => 'tidak_aktif'))) {
                        $success_count++;
                    }
                    break;
                    
                case 'delete':
                    if (!$this->User_model->has_bookings($user_id)) {
                        if ($this->User_model->delete_user($user_id)) {
                            $success_count++;
                        }
                    }
                    break;
            }
        }
        
        $total_selected = count($user_ids);
        if ($current_user_id && in_array($current_user_id, $user_ids)) {
            $total_selected--; // Exclude current user from count
        }
        
        if ($success_count > 0) {
            echo json_encode(array(
                'success' => true, 
                'message' => "Berhasil memproses {$success_count} dari {$total_selected} pengguna."
            ));
        } else {
            echo json_encode(array(
                'success' => false, 
                'message' => 'Tidak ada pengguna yang berhasil diproses.'
            ));
        }
    }

    /**
     * Get user statistics
     */
    public function statistics()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $stats = $this->User_model->get_user_stats();
        
        echo json_encode(array(
            'success' => true,
            'data' => $stats
        ));
    }

    /**
     * Search users (AJAX)
     */
    public function search()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $keyword = $this->input->post('keyword');
        $users = $this->User_model->search_users($keyword);
        
        echo json_encode(array(
            'success' => true,
            'data' => $users
        ));
    }
}