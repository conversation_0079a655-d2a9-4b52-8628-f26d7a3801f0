<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Transaksi_model extends CI_Model {

    private $table = 'transaksi';

    public function __construct() {
        parent::__construct();
        $this->load->database();
    }

    /**
     * Insert new transaction
     */
    public function insert_transaksi($data) {
        $this->db->insert($this->table, $data);
        return $this->db->insert_id();
    }

    /**
     * Get transaction detail with booking info
     */
    public function get_transaksi_detail($id) {
        $this->db->select('
            t.*,
            b.kode_booking,
            b.customer_name,
            b.customer_phone,
            b.customer_email,
            b.tanggal_booking,
            b.jam_mulai,
            b.jam_selesai,
            b.durasi,
            b.subtotal,
            b.diskon,
            b.member_code,
            l.nama_lapangan,
            l.harga_per_jam,
            mc.full_name as member_name,
            u.full_name as kasir_name
        ');
        $this->db->from($this->table . ' t');
        $this->db->join('booking b', 't.booking_id = b.id', 'left');
        $this->db->join('lapangan l', 'b.lapangan_id = l.id', 'left');
        $this->db->join('member_codes mc', 'b.member_code = mc.member_code', 'left');
        $this->db->join('users u', 't.kasir_id = u.id', 'left');
        $this->db->where('t.id', $id);
        
        return $this->db->get()->row();
    }

    /**
     * Get transactions with filters
     */
    public function get_transaksi_with_filters($filter_tanggal = null, $filter_metode = null, $filter_kasir = null) {
        $this->db->select('
            t.*,
            b.kode_booking,
            b.customer_name,
            b.tanggal_booking,
            l.nama_lapangan,
            u.full_name as kasir_name
        ');
        $this->db->from($this->table . ' t');
        $this->db->join('booking b', 't.booking_id = b.id', 'left');
        $this->db->join('lapangan l', 'b.lapangan_id = l.id', 'left');
        $this->db->join('users u', 't.kasir_id = u.id', 'left');
        
        // Apply filters
        if ($filter_tanggal) {
            $this->db->where('DATE(t.tanggal_transaksi)', $filter_tanggal);
        }
        
        if ($filter_metode) {
            $this->db->where('t.metode_pembayaran', $filter_metode);
        }
        
        if ($filter_kasir) {
            $this->db->where('t.kasir_id', $filter_kasir);
        }
        
        $this->db->order_by('t.tanggal_transaksi', 'DESC');
        
        return $this->db->get()->result();
    }

    /**
     * Get transaction summary
     */
    public function get_transaksi_summary($filter_tanggal = null, $filter_metode = null, $filter_kasir = null) {
        $this->db->select('
            COUNT(*) as total_transaksi,
            SUM(total_harga) as total_pendapatan,
            SUM(jumlah_bayar) as total_diterima,
            SUM(kembalian) as total_kembalian,
            AVG(total_harga) as rata_rata_transaksi
        ');
        $this->db->from($this->table);
        
        // Apply same filters
        if ($filter_tanggal) {
            $this->db->where('DATE(tanggal_transaksi)', $filter_tanggal);
        }
        
        if ($filter_metode) {
            $this->db->where('metode_pembayaran', $filter_metode);
        }
        
        if ($filter_kasir) {
            $this->db->where('kasir_id', $filter_kasir);
        }
        
        return $this->db->get()->row();
    }

    /**
     * Get transactions by payment method
     */
    public function get_transaksi_by_metode($filter_tanggal = null) {
        $this->db->select('
            metode_pembayaran,
            COUNT(*) as jumlah_transaksi,
            SUM(total_harga) as total_pendapatan
        ');
        $this->db->from($this->table);
        
        if ($filter_tanggal) {
            $this->db->where('DATE(tanggal_transaksi)', $filter_tanggal);
        }
        
        $this->db->group_by('metode_pembayaran');
        $this->db->order_by('total_pendapatan', 'DESC');
        
        return $this->db->get()->result();
    }

    /**
     * Get daily transaction summary
     */
    public function get_daily_summary($start_date = null, $end_date = null) {
        if (!$start_date) {
            $start_date = date('Y-m-d', strtotime('-30 days'));
        }
        if (!$end_date) {
            $end_date = date('Y-m-d');
        }
        
        $this->db->select('
            DATE(tanggal_transaksi) as tanggal,
            COUNT(*) as jumlah_transaksi,
            SUM(total_harga) as total_pendapatan
        ');
        $this->db->from($this->table);
        $this->db->where('DATE(tanggal_transaksi) >=', $start_date);
        $this->db->where('DATE(tanggal_transaksi) <=', $end_date);
        $this->db->group_by('DATE(tanggal_transaksi)');
        $this->db->order_by('tanggal', 'ASC');
        
        return $this->db->get()->result();
    }

    /**
     * Get monthly transaction summary
     */
    public function get_monthly_summary($year = null) {
        if (!$year) {
            $year = date('Y');
        }
        
        $this->db->select('
            MONTH(tanggal_transaksi) as bulan,
            YEAR(tanggal_transaksi) as tahun,
            COUNT(*) as jumlah_transaksi,
            SUM(total_harga) as total_pendapatan
        ');
        $this->db->from($this->table);
        $this->db->where('YEAR(tanggal_transaksi)', $year);
        $this->db->group_by('YEAR(tanggal_transaksi), MONTH(tanggal_transaksi)');
        $this->db->order_by('bulan', 'ASC');
        
        return $this->db->get()->result();
    }

    /**
     * Get top customers by transaction value
     */
    public function get_top_customers($limit = 10, $filter_tanggal = null) {
        $this->db->select('
            b.customer_name,
            b.customer_phone,
            COUNT(*) as jumlah_transaksi,
            SUM(t.total_harga) as total_belanja,
            AVG(t.total_harga) as rata_rata_belanja
        ');
        $this->db->from($this->table . ' t');
        $this->db->join('booking b', 't.booking_id = b.id', 'left');
        
        if ($filter_tanggal) {
            $this->db->where('DATE(t.tanggal_transaksi)', $filter_tanggal);
        }
        
        $this->db->group_by('b.customer_name, b.customer_phone');
        $this->db->order_by('total_belanja', 'DESC');
        $this->db->limit($limit);
        
        return $this->db->get()->result();
    }

    /**
     * Get cashier performance
     */
    public function get_kasir_performance($filter_tanggal = null) {
        $this->db->select('
            u.full_name as kasir_name,
            COUNT(*) as jumlah_transaksi,
            SUM(t.total_harga) as total_pendapatan,
            AVG(t.total_harga) as rata_rata_transaksi
        ');
        $this->db->from($this->table . ' t');
        $this->db->join('users u', 't.kasir_id = u.id', 'left');
        
        if ($filter_tanggal) {
            $this->db->where('DATE(t.tanggal_transaksi)', $filter_tanggal);
        }
        
        $this->db->group_by('t.kasir_id, u.full_name');
        $this->db->order_by('total_pendapatan', 'DESC');
        
        return $this->db->get()->result();
    }

    /**
     * Get last transaction code for generating new code
     */
    public function get_last_transaction_code($date) {
        $this->db->select('kode_transaksi');
        $this->db->from($this->table);
        $this->db->like('kode_transaksi', 'TRX' . $date, 'after');
        $this->db->order_by('id', 'DESC');
        $this->db->limit(1);
        
        return $this->db->get()->row();
    }

    /**
     * Get transaction by booking ID
     */
    public function get_transaksi_by_booking($booking_id) {
        $this->db->where('booking_id', $booking_id);
        return $this->db->get($this->table)->row();
    }

    /**
     * Update transaction
     */
    public function update_transaksi($id, $data) {
        $data['updated_at'] = date('Y-m-d H:i:s');
        $this->db->where('id', $id);
        return $this->db->update($this->table, $data);
    }

    /**
     * Delete transaction
     */
    public function delete_transaksi($id) {
        $this->db->where('id', $id);
        return $this->db->delete($this->table);
    }

    /**
     * Get today's transactions
     */
    public function get_today_transactions() {
        $this->db->select('
            t.*,
            b.kode_booking,
            b.customer_name,
            l.nama_lapangan,
            u.full_name as kasir_name
        ');
        $this->db->from($this->table . ' t');
        $this->db->join('booking b', 't.booking_id = b.id', 'left');
        $this->db->join('lapangan l', 'b.lapangan_id = l.id', 'left');
        $this->db->join('users u', 't.kasir_id = u.id', 'left');
        $this->db->where('DATE(t.tanggal_transaksi)', date('Y-m-d'));
        $this->db->order_by('t.tanggal_transaksi', 'DESC');
        
        return $this->db->get()->result();
    }

    /**
     * Get transaction statistics for dashboard
     */
    public function get_dashboard_stats() {
        // Today's stats
        $today_stats = $this->db->select('
            COUNT(*) as total_transaksi,
            COALESCE(SUM(total_harga), 0) as total_pendapatan
        ')
        ->where('DATE(tanggal_transaksi)', date('Y-m-d'))
        ->get($this->table)->row();
        
        // This month's stats
        $month_stats = $this->db->select('
            COUNT(*) as total_transaksi,
            COALESCE(SUM(total_harga), 0) as total_pendapatan
        ')
        ->where('YEAR(tanggal_transaksi)', date('Y'))
        ->where('MONTH(tanggal_transaksi)', date('m'))
        ->get($this->table)->row();
        
        return array(
            'today' => $today_stats,
            'month' => $month_stats
        );
    }
}
