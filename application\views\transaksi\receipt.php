<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h1><i class="fas fa-receipt"></i> Struk Pembayaran</h1>
            <p class="text-muted">Transaksi berhasil diproses</p>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="<?php echo site_url('transaksi/print_receipt/' . $transaksi->id); ?>" 
                   class="btn btn-outline-primary" target="_blank">
                    <i class="fas fa-print"></i> Cetak
                </a>
                <a href="<?php echo site_url('transaksi'); ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Transaksi Baru
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-success text-white">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-check-circle"></i> Pembayaran Berhasil
                        </h5>
                    </div>
                    <div class="col-auto">
                        <span class="badge bg-light text-success">
                            <?php echo $transaksi->kode_transaksi; ?>
                        </span>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Receipt Content -->
                <div id="receipt-content">
                    <!-- Header -->
                    <div class="text-center mb-4">
                        <h3 class="mb-1">PADEL COURT</h3>
                        <p class="text-muted mb-0">Jl. Contoh No. 123, Kota</p>
                        <p class="text-muted mb-0">Telp: (*************</p>
                        <hr class="my-3">
                    </div>

                    <!-- Transaction Info -->
                    <div class="row mb-4">
                        <div class="col-6">
                            <table class="table table-borderless table-sm">
                                <tr>
                                    <td class="fw-bold">No. Transaksi:</td>
                                    <td><?php echo $transaksi->kode_transaksi; ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Tanggal:</td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($transaksi->tanggal_transaksi)); ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Kasir:</td>
                                    <td><?php echo $transaksi->kasir_name; ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-6">
                            <table class="table table-borderless table-sm">
                                <tr>
                                    <td class="fw-bold">Booking:</td>
                                    <td><?php echo $transaksi->kode_booking; ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Customer:</td>
                                    <td><?php echo $transaksi->customer_name; ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Telepon:</td>
                                    <td><?php echo $transaksi->customer_phone; ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Booking Details -->
                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">Detail Booking:</h6>
                        <div class="bg-light p-3 rounded">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless table-sm mb-0">
                                        <tr>
                                            <td class="fw-bold" width="40%">Lapangan:</td>
                                            <td><?php echo $transaksi->nama_lapangan; ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Tanggal:</td>
                                            <td><?php echo date('d F Y', strtotime($transaksi->tanggal_booking)); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Waktu:</td>
                                            <td><?php echo $transaksi->jam_mulai; ?> - <?php echo $transaksi->jam_selesai; ?></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless table-sm mb-0">
                                        <tr>
                                            <td class="fw-bold" width="40%">Durasi:</td>
                                            <td><?php echo $transaksi->durasi; ?> jam</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Harga/Jam:</td>
                                            <td>Rp <?php echo number_format($transaksi->harga_per_jam, 0, ',', '.'); ?></td>
                                        </tr>
                                        <?php if ($transaksi->member_name): ?>
                                        <tr>
                                            <td class="fw-bold">Member:</td>
                                            <td class="text-primary">
                                                <i class="fas fa-star"></i> <?php echo $transaksi->member_name; ?>
                                            </td>
                                        </tr>
                                        <?php endif; ?>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Summary -->
                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">Rincian Pembayaran:</h6>
                        <table class="table table-borderless">
                            <tr>
                                <td>Subtotal (<?php echo $transaksi->durasi; ?> jam × Rp <?php echo number_format($transaksi->harga_per_jam, 0, ',', '.'); ?>)</td>
                                <td class="text-end">Rp <?php echo number_format($transaksi->subtotal, 0, ',', '.'); ?></td>
                            </tr>
                            <?php if ($transaksi->diskon > 0): ?>
                            <tr>
                                <td>Diskon <?php echo $transaksi->member_name ? '(Member)' : ''; ?></td>
                                <td class="text-end text-success">-Rp <?php echo number_format($transaksi->diskon, 0, ',', '.'); ?></td>
                            </tr>
                            <?php endif; ?>
                            <tr class="border-top">
                                <td class="fw-bold">Total Harga</td>
                                <td class="text-end fw-bold">Rp <?php echo number_format($transaksi->total_harga, 0, ',', '.'); ?></td>
                            </tr>
                            <tr>
                                <td>Jumlah Bayar (<?php echo ucfirst($transaksi->metode_pembayaran); ?>)</td>
                                <td class="text-end">Rp <?php echo number_format($transaksi->jumlah_bayar, 0, ',', '.'); ?></td>
                            </tr>
                            <tr class="border-top">
                                <td class="fw-bold">Kembalian</td>
                                <td class="text-end fw-bold text-success">Rp <?php echo number_format($transaksi->kembalian, 0, ',', '.'); ?></td>
                            </tr>
                        </table>
                    </div>

                    <!-- Footer -->
                    <div class="text-center mt-4 pt-3 border-top">
                        <p class="mb-1">Terima kasih atas kunjungan Anda!</p>
                        <p class="text-muted small mb-0">Simpan struk ini sebagai bukti pembayaran</p>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i> 
                            Status booking otomatis berubah menjadi "Selesai"
                        </small>
                    </div>
                    <div class="col-md-6 text-end">
                        <small class="text-muted">
                            Dicetak pada: <?php echo date('d/m/Y H:i:s'); ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    border: none;
    border-radius: 12px;
}

.card-header.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

.bg-light {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
}

#receipt-content {
    font-family: 'Courier New', monospace;
    line-height: 1.4;
}

.table-borderless td {
    padding: 0.25rem 0.5rem;
    border: none;
}

.border-top {
    border-top: 2px solid #dee2e6 !important;
}

.text-success {
    color: #28a745 !important;
}

.badge.bg-light {
    color: #28a745 !important;
    font-weight: 600;
}

@media print {
    .page-header,
    .card-footer,
    .btn-group {
        display: none !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }
    
    .card-header {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
}
</style>

<script>
$(document).ready(function() {
    // Auto-focus on print button for quick printing
    setTimeout(function() {
        $('.btn-outline-primary').focus();
    }, 500);
    
    // Keyboard shortcut for printing
    $(document).keydown(function(e) {
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            window.open('<?php echo site_url('transaksi/print_receipt/' . $transaksi->id); ?>', '_blank');
        }
    });
});
</script>
