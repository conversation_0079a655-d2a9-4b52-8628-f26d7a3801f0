<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h1><i class="fas fa-history"></i> Riwayat Transaksi</h1>
            <p class="text-muted">History pembayaran booking yang telah diproses</p>
        </div>
        <div class="col-auto">
            <a href="<?php echo site_url('transaksi'); ?>" class="btn btn-primary">
                <i class="fas fa-cash-register"></i> Pembayaran Baru
            </a>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h3 class="mb-1"><?php echo $summary->total_transaksi; ?></h3>
                        <small>Total Transaksi</small>
                    </div>
                    <div class="ms-3">
                        <i class="fas fa-receipt fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h3 class="mb-1">Rp <?php echo number_format($summary->total_pendapatan, 0, ',', '.'); ?></h3>
                        <small>Total Pendapatan</small>
                    </div>
                    <div class="ms-3">
                        <i class="fas fa-money-bill-wave fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h3 class="mb-1">Rp <?php echo number_format($summary->rata_rata_transaksi, 0, ',', '.'); ?></h3>
                        <small>Rata-rata Transaksi</small>
                    </div>
                    <div class="ms-3">
                        <i class="fas fa-chart-line fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h3 class="mb-1">Rp <?php echo number_format($summary->total_kembalian, 0, ',', '.'); ?></h3>
                        <small>Total Kembalian</small>
                    </div>
                    <div class="ms-3">
                        <i class="fas fa-hand-holding-usd fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filter Panel -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-filter"></i> Filter Transaksi
        </h5>
    </div>
    <div class="card-body">
        <form method="get" action="<?php echo site_url('transaksi/riwayat'); ?>" id="filterForm">
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label class="form-label">Tanggal</label>
                        <input type="date" name="tanggal" class="form-control" 
                               value="<?php echo $this->input->get('tanggal'); ?>">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label class="form-label">Metode Pembayaran</label>
                        <select name="metode" class="form-control">
                            <option value="">Semua Metode</option>
                            <option value="tunai" <?php echo ($this->input->get('metode') == 'tunai') ? 'selected' : ''; ?>>Tunai</option>
                            <option value="transfer" <?php echo ($this->input->get('metode') == 'transfer') ? 'selected' : ''; ?>>Transfer</option>
                            <option value="kartu" <?php echo ($this->input->get('metode') == 'kartu') ? 'selected' : ''; ?>>Kartu</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label class="form-label">Kasir</label>
                        <select name="kasir" class="form-control">
                            <option value="">Semua Kasir</option>
                            <?php foreach ($kasir_list as $kasir): ?>
                                <option value="<?php echo $kasir->id; ?>" 
                                        <?php echo ($this->input->get('kasir') == $kasir->id) ? 'selected' : ''; ?>>
                                    <?php echo $kasir->full_name; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> Filter
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Transaction List -->
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list"></i> Daftar Transaksi
                </h5>
            </div>
            <div class="col-auto">
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-secondary" onclick="exportData('excel')">
                        <i class="fas fa-file-excel"></i> Excel
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="exportData('pdf')">
                        <i class="fas fa-file-pdf"></i> PDF
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (empty($transaksi_list)): ?>
            <div class="text-center py-5">
                <i class="fas fa-inbox fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">Tidak Ada Transaksi</h5>
                <p class="text-muted">Belum ada transaksi yang sesuai dengan filter</p>
                <a href="<?php echo site_url('transaksi'); ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Buat Transaksi Baru
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>Kode Transaksi</th>
                            <th>Booking</th>
                            <th>Customer</th>
                            <th>Lapangan</th>
                            <th>Tanggal Main</th>
                            <th>Total</th>
                            <th>Metode</th>
                            <th>Kasir</th>
                            <th>Tanggal Bayar</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($transaksi_list as $index => $transaksi): ?>
                        <tr>
                            <td><?php echo $index + 1; ?></td>
                            <td>
                                <strong><?php echo $transaksi->kode_transaksi; ?></strong>
                            </td>
                            <td>
                                <a href="<?php echo site_url('booking/view/' . $transaksi->booking_id); ?>" 
                                   class="text-decoration-none">
                                    <?php echo $transaksi->kode_booking; ?>
                                </a>
                            </td>
                            <td>
                                <div>
                                    <strong><?php echo $transaksi->customer_name; ?></strong>
                                </div>
                            </td>
                            <td><?php echo $transaksi->nama_lapangan; ?></td>
                            <td>
                                <?php echo date('d M Y', strtotime($transaksi->tanggal_booking)); ?>
                            </td>
                            <td>
                                <strong class="text-success">
                                    Rp <?php echo number_format($transaksi->total_harga, 0, ',', '.'); ?>
                                </strong>
                            </td>
                            <td>
                                <span class="badge bg-<?php 
                                    echo ($transaksi->metode_pembayaran == 'tunai') ? 'success' : 
                                         (($transaksi->metode_pembayaran == 'transfer') ? 'info' : 'warning'); 
                                ?>">
                                    <i class="fas fa-<?php 
                                        echo ($transaksi->metode_pembayaran == 'tunai') ? 'money-bill' : 
                                             (($transaksi->metode_pembayaran == 'transfer') ? 'university' : 'credit-card'); 
                                    ?>"></i>
                                    <?php echo ucfirst($transaksi->metode_pembayaran); ?>
                                </span>
                            </td>
                            <td><?php echo $transaksi->kasir_name; ?></td>
                            <td>
                                <small>
                                    <?php echo date('d/m/Y H:i', strtotime($transaksi->tanggal_transaksi)); ?>
                                </small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="<?php echo site_url('transaksi/receipt/' . $transaksi->id); ?>" 
                                       class="btn btn-outline-primary" title="Lihat Struk">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?php echo site_url('transaksi/print_receipt/' . $transaksi->id); ?>" 
                                       class="btn btn-outline-secondary" target="_blank" title="Cetak Struk">
                                        <i class="fas fa-print"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
.card {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: none;
    border-radius: 8px;
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
}

.bg-primary {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%) !important;
}

.bg-success {
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%) !important;
}

.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
}

.bg-warning {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%) !important;
}

.table th {
    font-weight: 600;
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table td {
    vertical-align: middle;
    font-size: 13px;
}

.badge {
    font-size: 10px;
    padding: 4px 8px;
}

.btn-group-sm .btn {
    padding: 4px 8px;
    font-size: 11px;
}

.text-success {
    font-weight: 600;
}

.opacity-75 {
    opacity: 0.75;
}
</style>

<script>
$(document).ready(function() {
    // Auto-submit filter form on change
    $('#filterForm select, #filterForm input[type="date"]').change(function() {
        $('#filterForm').submit();
    });
});

function exportData(format) {
    alert('Fitur export akan segera tersedia');
    // const params = new URLSearchParams(window.location.search);
    // params.set('export', format);
    // window.open('<?php echo site_url('transaksi/export'); ?>?' + params.toString(), '_blank');
}
</script>
