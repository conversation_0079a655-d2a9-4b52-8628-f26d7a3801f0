-- ============================================================================
-- DATABASE LENGKAP SISTEM PADEL BOOKING - VERSI FINAL
-- ============================================================================
-- Database siap pakai untuk sistem booking lapangan padel
-- Sudah termasuk semua tabel, data sample, dan konfigurasi
-- ============================================================================

-- Nonaktifkan foreign key checks sementara
SET FOREIGN_KEY_CHECKS = 0;

-- ============================================================================
-- TABEL USERS
-- ============================================================================
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `role` enum('admin','kasir','member') NOT NULL DEFAULT 'member',
  `status` enum('aktif','nonaktif') NOT NULL DEFAULT 'aktif',
  `last_login` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- TABEL LAPANGAN
-- ============================================================================
CREATE TABLE `lapangan` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nama_lapangan` varchar(100) NOT NULL,
  `deskripsi` text DEFAULT NULL,
  `harga_per_jam` decimal(10,2) NOT NULL,
  `status` enum('aktif','nonaktif','maintenance') NOT NULL DEFAULT 'aktif',
  `fasilitas` text DEFAULT NULL,
  `gambar` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `nama_lapangan` (`nama_lapangan`),
  KEY `idx_status` (`status`),
  KEY `idx_harga` (`harga_per_jam`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- TABEL MEMBER CODES
-- ============================================================================
CREATE TABLE `member_codes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_code` varchar(20) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `discount_percentage` decimal(5,2) NOT NULL DEFAULT 10.00,
  `total_bookings` int(11) NOT NULL DEFAULT 0,
  `total_spent` decimal(12,2) NOT NULL DEFAULT 0.00,
  `status` enum('aktif','nonaktif','suspended') NOT NULL DEFAULT 'aktif',
  `expired_date` date DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `member_code` (`member_code`),
  UNIQUE KEY `phone` (`phone`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- TABEL BOOKING
-- ============================================================================
CREATE TABLE `booking` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kode_booking` varchar(20) NOT NULL,
  `lapangan_id` int(11) NOT NULL,
  `customer_name` varchar(100) NOT NULL,
  `customer_phone` varchar(20) NOT NULL,
  `customer_email` varchar(100) DEFAULT NULL,
  `tanggal_booking` date NOT NULL,
  `jam_mulai` time NOT NULL,
  `jam_selesai` time NOT NULL,
  `durasi` decimal(3,1) NOT NULL,
  `harga_per_jam` decimal(10,2) NOT NULL,
  `subtotal` decimal(10,2) NOT NULL,
  `member_code` varchar(20) DEFAULT NULL,
  `diskon` decimal(10,2) NOT NULL DEFAULT 0.00,
  `harga_total` decimal(10,2) NOT NULL,
  `status_booking` enum('pending','dikonfirmasi','selesai','dibatalkan') NOT NULL DEFAULT 'pending',
  `catatan` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `kode_booking` (`kode_booking`),
  KEY `idx_lapangan_id` (`lapangan_id`),
  KEY `idx_tanggal_booking` (`tanggal_booking`),
  KEY `idx_status_booking` (`status_booking`),
  KEY `idx_member_code` (`member_code`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_lapangan_tanggal` (`lapangan_id`, `tanggal_booking`),
  
  CONSTRAINT `fk_booking_lapangan` FOREIGN KEY (`lapangan_id`) REFERENCES `lapangan` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `fk_booking_member` FOREIGN KEY (`member_code`) REFERENCES `member_codes` (`member_code`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_booking_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- TABEL TRANSAKSI
-- ============================================================================
CREATE TABLE `transaksi` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `booking_id` int(11) NOT NULL,
  `kode_transaksi` varchar(20) NOT NULL,
  `total_harga` decimal(12,2) NOT NULL,
  `jumlah_bayar` decimal(12,2) NOT NULL,
  `kembalian` decimal(12,2) NOT NULL DEFAULT 0.00,
  `metode_pembayaran` enum('tunai','transfer','kartu','qris','ovo','gopay','dana') NOT NULL DEFAULT 'tunai',
  `kasir_id` int(11) NOT NULL,
  `tanggal_transaksi` datetime NOT NULL,
  `nomor_referensi` varchar(50) DEFAULT NULL,
  `catatan` text DEFAULT NULL,
  `status_transaksi` enum('pending','berhasil','gagal','dibatalkan') NOT NULL DEFAULT 'berhasil',
  `bukti_pembayaran` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `kode_transaksi` (`kode_transaksi`),
  UNIQUE KEY `booking_id` (`booking_id`),
  KEY `idx_tanggal_transaksi` (`tanggal_transaksi`),
  KEY `idx_metode_pembayaran` (`metode_pembayaran`),
  KEY `idx_kasir_id` (`kasir_id`),
  KEY `idx_status_transaksi` (`status_transaksi`),
  
  CONSTRAINT `fk_transaksi_booking` FOREIGN KEY (`booking_id`) REFERENCES `booking` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `fk_transaksi_kasir` FOREIGN KEY (`kasir_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Aktifkan kembali foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- ============================================================================
-- INSERT DATA SAMPLE
-- ============================================================================

-- Insert Users
INSERT INTO `users` (`username`, `email`, `password`, `full_name`, `phone`, `role`, `status`) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', '081234567890', 'admin', 'aktif'),
('kasir1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Kasir Satu', '081234567891', 'kasir', 'aktif'),
('kasir2', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Kasir Dua', '081234567892', 'kasir', 'aktif');

-- Insert Lapangan
INSERT INTO `lapangan` (`nama_lapangan`, `deskripsi`, `harga_per_jam`, `status`, `fasilitas`) VALUES
('Lapangan A', 'Lapangan padel premium dengan fasilitas lengkap', 150000.00, 'aktif', 'AC, Sound System, Lighting LED'),
('Lapangan B', 'Lapangan padel standar dengan kualitas baik', 120000.00, 'aktif', 'Lighting LED, Shower'),
('Lapangan C', 'Lapangan padel outdoor dengan view bagus', 100000.00, 'aktif', 'Natural Lighting, Parking'),
('Lapangan D', 'Lapangan padel indoor ber-AC', 180000.00, 'aktif', 'AC, Premium Sound, LED Display');

-- Insert Member Codes
INSERT INTO `member_codes` (`member_code`, `full_name`, `phone`, `email`, `discount_percentage`, `status`, `expired_date`) VALUES
('MB0001', 'John Doe', '081111111111', '<EMAIL>', 15.00, 'aktif', '2025-12-31'),
('MB0002', 'Jane Smith', '081222222222', '<EMAIL>', 10.00, 'aktif', '2025-12-31'),
('MB0003', 'Bob Wilson', '081333333333', '<EMAIL>', 20.00, 'aktif', '2025-12-31'),
('MB0004', 'Alice Brown', '081444444444', '<EMAIL>', 12.00, 'aktif', '2025-12-31');

-- Insert Sample Bookings
INSERT INTO `booking` (`kode_booking`, `lapangan_id`, `customer_name`, `customer_phone`, `tanggal_booking`, `jam_mulai`, `jam_selesai`, `durasi`, `harga_per_jam`, `subtotal`, `member_code`, `diskon`, `harga_total`, `status_booking`, `created_by`) VALUES
('BK2501140001', 1, 'John Doe', '081111111111', '2025-01-15', '09:00:00', '11:00:00', 2.0, 150000.00, 300000.00, 'MB0001', 45000.00, 255000.00, 'dikonfirmasi', 1),
('BK2501140002', 2, 'Jane Smith', '081222222222', '2025-01-15', '14:00:00', '16:00:00', 2.0, 120000.00, 240000.00, 'MB0002', 24000.00, 216000.00, 'dikonfirmasi', 1),
('BK2501140003', 3, 'Regular Customer', '081555555555', '2025-01-15', '16:00:00', '18:00:00', 2.0, 100000.00, 200000.00, NULL, 0.00, 200000.00, 'dikonfirmasi', 1),
('BK2501140004', 1, 'Bob Wilson', '081333333333', '2025-01-16', '10:00:00', '12:00:00', 2.0, 150000.00, 300000.00, 'MB0003', 60000.00, 240000.00, 'pending', 1),
('BK2501140005', 4, 'Alice Brown', '081444444444', '2025-01-16', '19:00:00', '21:00:00', 2.0, 180000.00, 360000.00, 'MB0004', 43200.00, 316800.00, 'pending', 1);

-- ============================================================================
-- VIEWS UNTUK REPORTING
-- ============================================================================

-- View booking detail lengkap
CREATE VIEW `v_booking_detail` AS
SELECT 
    b.id,
    b.kode_booking,
    b.customer_name,
    b.customer_phone,
    b.customer_email,
    b.tanggal_booking,
    b.jam_mulai,
    b.jam_selesai,
    b.durasi,
    b.harga_per_jam,
    b.subtotal,
    b.diskon,
    b.harga_total,
    b.status_booking,
    b.catatan,
    l.nama_lapangan,
    l.fasilitas,
    mc.full_name as member_name,
    mc.member_code,
    mc.discount_percentage,
    u.full_name as created_by_name,
    t.kode_transaksi,
    t.metode_pembayaran,
    t.tanggal_transaksi,
    b.created_at,
    b.updated_at
FROM booking b
LEFT JOIN lapangan l ON b.lapangan_id = l.id
LEFT JOIN member_codes mc ON b.member_code = mc.member_code
LEFT JOIN users u ON b.created_by = u.id
LEFT JOIN transaksi t ON b.id = t.booking_id;

-- View transaksi detail lengkap
CREATE VIEW `v_transaksi_detail` AS
SELECT 
    t.id,
    t.kode_transaksi,
    t.tanggal_transaksi,
    t.total_harga,
    t.jumlah_bayar,
    t.kembalian,
    t.metode_pembayaran,
    t.status_transaksi,
    t.nomor_referensi,
    t.catatan as catatan_transaksi,
    b.kode_booking,
    b.customer_name,
    b.customer_phone,
    b.tanggal_booking,
    b.jam_mulai,
    b.jam_selesai,
    b.durasi,
    b.subtotal,
    b.diskon,
    l.nama_lapangan,
    l.harga_per_jam,
    u.full_name as nama_kasir,
    mc.full_name as nama_member,
    mc.member_code,
    t.created_at,
    t.updated_at
FROM transaksi t
LEFT JOIN booking b ON t.booking_id = b.id
LEFT JOIN lapangan l ON b.lapangan_id = l.id
LEFT JOIN users u ON t.kasir_id = u.id
LEFT JOIN member_codes mc ON b.member_code = mc.member_code;

-- ============================================================================
-- STORED PROCEDURES
-- ============================================================================

-- Procedure untuk generate kode booking
DELIMITER $$
CREATE PROCEDURE `sp_generate_booking_code`(
    IN p_tanggal_booking DATE,
    OUT p_kode_booking VARCHAR(20)
)
BEGIN
    DECLARE next_number INT DEFAULT 1;
    DECLARE date_part VARCHAR(6);
    DECLARE last_code VARCHAR(20);
    
    SET date_part = DATE_FORMAT(p_tanggal_booking, '%y%m%d');
    
    SELECT kode_booking INTO last_code
    FROM booking 
    WHERE kode_booking LIKE CONCAT('BK', date_part, '%')
    ORDER BY id DESC 
    LIMIT 1;
    
    IF last_code IS NOT NULL THEN
        SET next_number = CAST(SUBSTRING(last_code, -3) AS UNSIGNED) + 1;
    END IF;
    
    SET p_kode_booking = CONCAT('BK', date_part, LPAD(next_number, 3, '0'));
END$$
DELIMITER ;

-- Procedure untuk generate kode transaksi
DELIMITER $$
CREATE PROCEDURE `sp_generate_transaction_code`(
    IN p_tanggal_transaksi DATETIME,
    OUT p_kode_transaksi VARCHAR(20)
)
BEGIN
    DECLARE next_number INT DEFAULT 1;
    DECLARE date_part VARCHAR(6);
    DECLARE last_code VARCHAR(20);
    
    SET date_part = DATE_FORMAT(p_tanggal_transaksi, '%y%m%d');
    
    SELECT kode_transaksi INTO last_code
    FROM transaksi 
    WHERE kode_transaksi LIKE CONCAT('TRX', date_part, '%')
    ORDER BY id DESC 
    LIMIT 1;
    
    IF last_code IS NOT NULL THEN
        SET next_number = CAST(SUBSTRING(last_code, -3) AS UNSIGNED) + 1;
    END IF;
    
    SET p_kode_transaksi = CONCAT('TRX', date_part, LPAD(next_number, 3, '0'));
END$$
DELIMITER ;

-- ============================================================================
-- SELESAI - DATABASE SIAP DIGUNAKAN
-- ============================================================================

-- Tampilkan informasi database
SELECT 'DATABASE PADEL BOOKING BERHASIL DIBUAT!' as STATUS;
SELECT COUNT(*) as TOTAL_USERS FROM users;
SELECT COUNT(*) as TOTAL_LAPANGAN FROM lapangan;
SELECT COUNT(*) as TOTAL_MEMBERS FROM member_codes;
SELECT COUNT(*) as TOTAL_BOOKINGS FROM booking;

-- Login credentials:
-- Username: admin, Password: password
-- Username: kasir1, Password: password  
-- Username: kasir2, Password: password
