/* =============================================================================
   PADEL BOOKING SYSTEM - COMPLETE CSS
   Simple, Clean, and Responsive Design
   ============================================================================= */

/* Reset dan Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
    overflow-x: hidden;
}

/* =============================================================================
   TYPOGRAPHY & ICONS
   ============================================================================= */

/* Base Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 600;
    line-height: 1.3;
    color: #2c3e50;
    margin-bottom: 0.5em;
}

h1 { font-size: 2rem; }
h2 { font-size: 1.75rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.1rem; }
h6 { font-size: 1rem; }

p {
    line-height: 1.6;
    margin-bottom: 1rem;
    color: #495057;
}

small {
    font-size: 0.875em;
    color: #6c757d;
}

strong {
    font-weight: 600;
}

/* Icon Standardization */
i, .icon {
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
    vertical-align: middle;
}

/* Icon Sizes */
.icon-xs { font-size: 0.75rem; }
.icon-sm { font-size: 0.875rem; }
.icon-md { font-size: 1rem; }
.icon-lg { font-size: 1.25rem; }
.icon-xl { font-size: 1.5rem; }
.icon-2x { font-size: 2rem; }
.icon-3x { font-size: 3rem; }

/* Icon Spacing */
.icon-left {
    margin-right: 0.5rem;
}

.icon-right {
    margin-left: 0.5rem;
}

/* Button Icons */
.btn i {
    margin-right: 0.375rem;
    font-size: 0.875em;
    line-height: 1;
    vertical-align: middle;
}

.btn-sm i {
    margin-right: 0.25rem;
    font-size: 0.75em;
}

.btn-lg i {
    margin-right: 0.5rem;
    font-size: 1em;
}

/* Only icon buttons */
.btn i:only-child {
    margin: 0;
}

/* Table Icons */
.table i {
    font-size: 0.875rem;
    vertical-align: middle;
}

/* Card Header Icons */
.card-header i {
    margin-right: 0.5rem;
    font-size: 1em;
    vertical-align: middle;
}

/* Sidebar Icons */
.sidebar-nav i {
    width: 18px;
    text-align: center;
    margin-right: 0.75rem;
    font-size: 0.9rem;
    vertical-align: middle;
}

/* Status Icons */
.status-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    font-size: 0.75rem;
    margin-right: 0.5rem;
}

.status-icon.success {
    background: rgba(39,174,96,0.1);
    color: #27ae60;
}

.status-icon.warning {
    background: rgba(243,156,18,0.1);
    color: #f39c12;
}

.status-icon.danger {
    background: rgba(231,76,60,0.1);
    color: #e74c3c;
}

.status-icon.info {
    background: rgba(52,152,219,0.1);
    color: #3498db;
}

/* Text Colors */
.text-primary { color: #3498db !important; }
.text-success { color: #27ae60 !important; }
.text-warning { color: #f39c12 !important; }
.text-danger { color: #e74c3c !important; }
.text-info { color: #17a2b8 !important; }
.text-secondary { color: #6c757d !important; }
.text-muted { color: #6c757d !important; }
.text-white { color: #ffffff !important; }
.text-dark { color: #343a40 !important; }

/* Font Weights */
.font-weight-light { font-weight: 300; }
.font-weight-normal { font-weight: 400; }
.font-weight-medium { font-weight: 500; }
.font-weight-semibold { font-weight: 600; }
.font-weight-bold { font-weight: 700; }

/* Text Transforms */
.text-uppercase { text-transform: uppercase; }
.text-lowercase { text-transform: lowercase; }
.text-capitalize { text-transform: capitalize; }

/* Line Heights */
.lh-1 { line-height: 1; }
.lh-sm { line-height: 1.25; }
.lh-base { line-height: 1.5; }
.lh-lg { line-height: 2; }

/* =============================================================================
   UTILITIES
   ============================================================================= */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.container-fluid {
    width: 100%;
    padding: 0 20px;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-inline-block { display: inline-block; }
.d-inline-flex { display: inline-flex; }

.justify-content-start { justify-content: flex-start; }
.justify-content-center { justify-content: center; }
.justify-content-end { justify-content: flex-end; }
.justify-content-between { justify-content: space-between; }
.justify-content-around { justify-content: space-around; }

.align-items-start { align-items: flex-start; }
.align-items-center { align-items: center; }
.align-items-end { align-items: flex-end; }
.align-items-stretch { align-items: stretch; }

.flex-column { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: 0.25rem; }
.ml-2 { margin-left: 0.5rem; }
.ml-3 { margin-left: 1rem; }
.ml-4 { margin-left: 1.5rem; }
.ml-5 { margin-left: 3rem; }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: 0.25rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 1rem; }
.mr-4 { margin-right: 1.5rem; }
.mr-5 { margin-right: 3rem; }

.p-0 { padding: 0; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }
.p-5 { padding: 3rem; }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: 0.25rem; }
.pt-2 { padding-top: 0.5rem; }
.pt-3 { padding-top: 1rem; }
.pt-4 { padding-top: 1.5rem; }
.pt-5 { padding-top: 3rem; }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: 0.25rem; }
.pb-2 { padding-bottom: 0.5rem; }
.pb-3 { padding-bottom: 1rem; }
.pb-4 { padding-bottom: 1.5rem; }
.pb-5 { padding-bottom: 3rem; }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: 0.25rem; }
.pl-2 { padding-left: 0.5rem; }
.pl-3 { padding-left: 1rem; }
.pl-4 { padding-left: 1.5rem; }
.pl-5 { padding-left: 3rem; }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: 0.25rem; }
.pr-2 { padding-right: 0.5rem; }
.pr-3 { padding-right: 1rem; }
.pr-4 { padding-right: 1.5rem; }
.pr-5 { padding-right: 3rem; }

/* =============================================================================
   HEADER
   ============================================================================= */
.header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 0;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-bottom: 3px solid #3498db;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.header-left {
    display: flex;
    align-items: center;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    margin-right: 15px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.sidebar-toggle:hover {
    background-color: rgba(255,255,255,0.1);
    transform: scale(1.1);
}

.header h1 {
    margin: 0;
    font-size: 1.4rem;
    font-weight: 600;
}

.header h1 i {
    margin-right: 10px;
    color: #3498db;
}

.header-right {
    display: flex;
    align-items: center;
}

.user-info {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
}

.user-info span {
    margin-right: 15px;
    color: #ecf0f1;
}

.user-role {
    background-color: #3498db;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    margin-right: 15px !important;
}

.user-info a {
    color: #ecf0f1;
    text-decoration: none;
    padding: 8px 12px;
    border-radius: 4px;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.user-info a:hover {
    color: white;
    background-color: #e74c3c;
    border-color: #c0392b;
}

/* =============================================================================
   SIDEBAR
   ============================================================================= */
.sidebar {
    background: linear-gradient(180deg, #34495e 0%, #2c3e50 100%);
    width: 260px;
    height: 100vh;
    position: fixed;
    left: 0;
    top: 71px;
    overflow-y: auto;
    transition: all 0.3s ease;
    border-right: 3px solid #3498db;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: #2c3e50;
}

.sidebar::-webkit-scrollbar-thumb {
    background: #3498db;
    border-radius: 3px;
}

.sidebar-nav {
    padding: 20px 0;
}

.sidebar-nav ul {
    list-style: none;
}

.sidebar-nav ul li {
    margin-bottom: 2px;
}

.sidebar-nav ul li a {
    color: #bdc3c7;
    text-decoration: none;
    display: flex;
    align-items: center;
    padding: 15px 20px;
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
    position: relative;
}

.sidebar-nav ul li a:hover {
    background-color: rgba(52,152,219,0.1);
    color: #3498db;
    border-left-color: #3498db;
    transform: translateX(5px);
}

.sidebar-nav ul li a.active {
    background-color: rgba(52,152,219,0.2);
    color: #3498db;
    border-left-color: #3498db;
    font-weight: 600;
}

.sidebar-nav ul li a i {
    margin-right: 12px;
    width: 20px;
    text-align: center;
    font-size: 1.1rem;
}

.sidebar.collapsed .sidebar-nav ul li a span {
    display: none;
}

.sidebar.collapsed .sidebar-nav ul li a {
    padding: 15px;
    justify-content: center;
}

.nav-divider {
    margin: 20px 20px 10px;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.nav-divider span {
    color: #95a5a6;
    font-size: 0.75rem;
    font-weight: 600;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.sidebar.collapsed .nav-divider {
    margin: 20px 10px 10px;
}

.sidebar.collapsed .nav-divider span {
    display: none;
}

/* =============================================================================
   MAIN CONTENT
   ============================================================================= */
.main-content {
    margin-left: 260px;
    margin-top: 71px;
    padding: 30px;
    transition: all 0.3s ease;
    min-height: calc(100vh - 71px);
    background-color: #f8f9fa;
}

.main-content.expanded {
    margin-left: 70px;
}

/* =============================================================================
   CARDS
   ============================================================================= */
.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    margin-bottom: 20px;
    overflow: hidden;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

.card:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.12);
    transform: translateY(-1px);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #495057;
    padding: 16px 20px;
    font-weight: 600;
    font-size: 14px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header.bg-primary {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    border-bottom: none;
}

.card-header.bg-success {
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
    color: white;
    border-bottom: none;
}

.card-header.bg-warning {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    color: white;
    border-bottom: none;
}

.card-header.bg-danger {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
    border-bottom: none;
}

.card-header.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    border-bottom: none;
}

.card-header i {
    margin-right: 8px;
    font-size: 14px;
}

.card-body {
    padding: 20px;
    font-size: 13px;
    line-height: 1.5;
}

.card-footer {
    background-color: #f8f9fa;
    padding: 16px 20px;
    border-top: 1px solid #e9ecef;
    font-size: 12px;
}

.card-title {
    margin: 0 0 12px;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.3;
}

.card-text {
    color: #6c757d;
    margin-bottom: 12px;
    font-size: 13px;
    line-height: 1.4;
}

/* Stats Cards */
.stats-card {
    border: none;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
    background: white;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3498db, #2980b9);
}

.stats-card.bg-primary::before {
    background: linear-gradient(90deg, #3498db, #2980b9);
}

.stats-card.bg-success::before {
    background: linear-gradient(90deg, #27ae60, #229954);
}

.stats-card.bg-warning::before {
    background: linear-gradient(90deg, #f39c12, #e67e22);
}

.stats-card.bg-danger::before {
    background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.stats-card.bg-info::before {
    background: linear-gradient(90deg, #17a2b8, #138496);
}

.stats-card .card-body {
    padding: 24px 20px;
    text-align: center;
}

.stats-card .stat-icon {
    font-size: 2.5rem;
    margin-bottom: 16px;
    opacity: 0.8;
    color: #3498db;
}

.stats-card.bg-primary .stat-icon {
    color: #3498db;
}

.stats-card.bg-success .stat-icon {
    color: #27ae60;
}

.stats-card.bg-warning .stat-icon {
    color: #f39c12;
}

.stats-card.bg-danger .stat-icon {
    color: #e74c3c;
}

.stats-card.bg-info .stat-icon {
    color: #17a2b8;
}

.stats-card .stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 8px;
    line-height: 1;
}

.stats-card .stat-label {
    color: #6c757d;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0;
}

/* Navigation Cards */
.nav-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    border-radius: 8px;
}

.nav-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    border-color: #3498db;
}

.nav-card .card-body {
    padding: 30px 20px;
    text-align: center;
}

.nav-card i {
    margin-bottom: 16px;
    opacity: 0.8;
}

.nav-card h5 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 8px;
}

.nav-card p {
    color: #6c757d;
    font-size: 13px;
    margin-bottom: 20px;
}

/* =============================================================================
   BUTTONS
   ============================================================================= */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 16px;
    background-color: #3498db;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
    white-space: nowrap;
    min-height: 38px;
    line-height: 1.4;
    position: relative;
    overflow: hidden;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 12px rgba(0,0,0,0.15);
    text-decoration: none;
    color: white;
}

.btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(0,0,0,0.2);
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(52,152,219,0.3);
}

.btn i {
    margin-right: 6px;
    font-size: 12px;
    line-height: 1;
}

.btn-primary {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    border: 1px solid #2980b9;
    box-shadow: 0 2px 4px rgba(52,152,219,0.2);
}
.btn-primary:hover {
    background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);
    box-shadow: 0 4px 12px rgba(52,152,219,0.3);
}

.btn-success {
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
    border: 1px solid #229954;
    box-shadow: 0 2px 4px rgba(39,174,96,0.2);
}
.btn-success:hover {
    background: linear-gradient(135deg, #229954 0%, #1e7e34 100%);
    box-shadow: 0 4px 12px rgba(39,174,96,0.3);
}

.btn-warning {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    border: 1px solid #e67e22;
    box-shadow: 0 2px 4px rgba(243,156,18,0.2);
    color: white;
}
.btn-warning:hover {
    background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
    box-shadow: 0 4px 12px rgba(243,156,18,0.3);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    border: 1px solid #c0392b;
    box-shadow: 0 2px 4px rgba(231,76,60,0.2);
}
.btn-danger:hover {
    background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
    box-shadow: 0 4px 12px rgba(231,76,60,0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
    border: 1px solid #7f8c8d;
    box-shadow: 0 2px 4px rgba(149,165,166,0.2);
}
.btn-secondary:hover {
    background: linear-gradient(135deg, #7f8c8d 0%, #6c7b7d 100%);
    box-shadow: 0 4px 12px rgba(149,165,166,0.3);
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    border: 1px solid #138496;
    box-shadow: 0 2px 4px rgba(23,162,184,0.2);
}
.btn-info:hover {
    background: linear-gradient(135deg, #138496 0%, #0f6674 100%);
    box-shadow: 0 4px 12px rgba(23,162,184,0.3);
}

/* Outline Buttons */
.btn-outline-primary {
    background: transparent;
    color: #3498db;
    border: 1px solid #3498db;
    box-shadow: none;
}
.btn-outline-primary:hover {
    background: #3498db;
    color: white;
    box-shadow: 0 3px 12px rgba(52,152,219,0.3);
}

.btn-outline-success {
    background: transparent;
    color: #27ae60;
    border: 1px solid #27ae60;
    box-shadow: none;
}
.btn-outline-success:hover {
    background: #27ae60;
    color: white;
    box-shadow: 0 3px 12px rgba(39,174,96,0.3);
}

.btn-outline-warning {
    background: transparent;
    color: #f39c12;
    border: 1px solid #f39c12;
    box-shadow: none;
}
.btn-outline-warning:hover {
    background: #f39c12;
    color: white;
    box-shadow: 0 3px 12px rgba(243,156,18,0.3);
}

.btn-outline-danger {
    background: transparent;
    color: #e74c3c;
    border: 1px solid #e74c3c;
    box-shadow: none;
}
.btn-outline-danger:hover {
    background: #e74c3c;
    color: white;
    box-shadow: 0 3px 12px rgba(231,76,60,0.3);
}

.btn-outline-secondary {
    background: transparent;
    color: #6c757d;
    border: 1px solid #6c757d;
    box-shadow: none;
}
.btn-outline-secondary:hover {
    background: #6c757d;
    color: white;
    box-shadow: 0 3px 12px rgba(108,117,125,0.3);
}

/* Button Sizes */
.btn-sm {
    padding: 6px 12px;
    font-size: 11px;
    min-height: 32px;
    border-radius: 4px;
}

.btn-sm i {
    font-size: 10px;
    margin-right: 4px;
}

.btn-lg {
    padding: 14px 28px;
    font-size: 15px;
    min-height: 48px;
    border-radius: 8px;
}

.btn-lg i {
    font-size: 14px;
    margin-right: 8px;
}

.btn-block {
    width: 100%;
    display: flex;
}

.btn[disabled],
.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* Button Groups */
.btn-group {
    display: inline-flex;
    vertical-align: middle;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-group .btn {
    border-radius: 0;
    border-right: 1px solid rgba(255,255,255,0.2);
    margin: 0;
    position: relative;
    z-index: 1;
}

.btn-group .btn:first-child {
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
}

.btn-group .btn:last-child {
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    border-right: none;
}

.btn-group .btn:hover {
    z-index: 2;
    transform: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.btn-group .btn:focus {
    z-index: 3;
}

/* Small Button Groups */
.btn-group-sm .btn {
    padding: 4px 8px;
    font-size: 10px;
    min-height: 28px;
}

.btn-group-sm .btn i {
    font-size: 9px;
    margin-right: 3px;
}

/* =============================================================================
   FORMS
   ============================================================================= */
.form-group {
    margin-bottom: 25px;
    position: relative;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
}

.form-label.required::after {
    content: ' *';
    color: #e74c3c;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background-color: white;
    color: #495057;
}

.form-control:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52,152,219,0.1);
    background-color: #fafbfc;
}

.form-control.error {
    border-color: #e74c3c;
    background-color: #fdf2f2;
}

.form-control:disabled {
    background-color: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
}

.form-text {
    font-size: 12px;
    color: #6c757d;
    margin-top: 6px;
    display: block;
}

.form-text.error,
.field-error {
    color: #e74c3c;
    font-weight: 500;
}

.input-group {
    display: flex;
    align-items: stretch;
}

.input-group .form-control {
    border-radius: 8px 0 0 8px;
}

.input-group-append {
    display: flex;
}

.input-group-text {
    background-color: #e9ecef;
    border: 2px solid #e9ecef;
    border-left: none;
    padding: 12px 15px;
    border-radius: 0 8px 8px 0;
    color: #495057;
}

.form-check {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.form-check-input {
    margin-right: 10px;
    transform: scale(1.2);
}

.form-check-label {
    color: #495057;
    font-size: 14px;
    cursor: pointer;
}

.form-row {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.form-row .form-group {
    flex: 1;
    min-width: 200px;
}

/* =============================================================================
   TABLES
   ============================================================================= */
.table-responsive {
    overflow-x: auto;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    border-radius: 8px;
    background: white;
    border: 1px solid #e9ecef;
}

.table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    margin: 0;
    font-size: 13px;
}

.table th,
.table td {
    padding: 12px 10px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
    line-height: 1.4;
}

.table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    font-weight: 600;
    color: #495057;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 2px solid #dee2e6;
    position: sticky;
    top: 0;
    z-index: 10;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: #fafbfc;
}

.table-bordered {
    border: 1px solid #dee2e6;
}

.table-bordered th,
.table-bordered td {
    border: 1px solid #dee2e6;
}

.table-sm th,
.table-sm td {
    padding: 8px 6px;
    font-size: 12px;
}

/* Table Action Column */
.table td:last-child {
    width: 140px;
    min-width: 140px;
    text-align: center;
    padding: 8px 6px;
}

.table th:last-child {
    width: 140px;
    min-width: 140px;
    text-align: center;
}

/* Action Button Groups in Tables */
.table .btn-group {
    box-shadow: none;
    border-radius: 4px;
}

.table .btn-group .btn {
    padding: 4px 8px;
    font-size: 11px;
    min-height: 28px;
    border-radius: 0;
    border: 1px solid rgba(0,0,0,0.1);
    margin: 0;
}

.table .btn-group .btn:first-child {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
}

.table .btn-group .btn:last-child {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

.table .btn-group .btn i {
    font-size: 10px;
    margin: 0;
}

.table .btn-group .btn:hover {
    transform: none;
    z-index: 2;
    box-shadow: 0 2px 4px rgba(0,0,0,0.15);
}

/* Dropdown in Action Column */
.table .dropdown {
    position: relative;
    display: inline-block;
}

.table .dropdown-toggle {
    background: transparent;
    border: 1px solid #dee2e6;
    color: #6c757d;
    padding: 4px 8px;
    font-size: 11px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.table .dropdown-toggle:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
    color: #495057;
}

.table .dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 1000;
    min-width: 160px;
    padding: 4px 0;
    margin: 2px 0 0;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    display: none;
}

.table .dropdown-menu.show {
    display: block;
}

.table .dropdown-item {
    display: block;
    width: 100%;
    padding: 6px 12px;
    clear: both;
    font-size: 12px;
    color: #495057;
    text-decoration: none;
    white-space: nowrap;
    background: transparent;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.table .dropdown-item:hover {
    background: #f8f9fa;
    color: #16181b;
}

.table .dropdown-item i {
    width: 14px;
    margin-right: 6px;
    font-size: 11px;
}

.table .dropdown-divider {
    height: 0;
    margin: 4px 0;
    overflow: hidden;
    border-top: 1px solid #e9ecef;
}

/* Status Badges in Tables */
.table .badge {
    font-size: 10px;
    padding: 3px 6px;
    border-radius: 12px;
    font-weight: 500;
    letter-spacing: 0.3px;
}

/* Checkbox Column */
.table td:first-child,
.table th:first-child {
    width: 40px;
    text-align: center;
    padding: 12px 8px;
}

.table input[type="checkbox"] {
    transform: scale(1.1);
    cursor: pointer;
}

/* =============================================================================
   ALERTS
   ============================================================================= */
.alert {
    padding: 15px 20px;
    margin-bottom: 25px;
    border: none;
    border-radius: 8px;
    border-left: 4px solid;
    position: relative;
    display: flex;
    align-items: center;
    font-size: 14px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.alert i {
    margin-right: 12px;
    font-size: 16px;
}

.alert-success {
    background-color: #d4edda;
    border-left-color: #27ae60;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border-left-color: #e74c3c;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    border-left-color: #f39c12;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    border-left-color: #3498db;
    color: #0c5460;
}

.alert-close {
    position: absolute;
    top: 10px;
    right: 15px;
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: inherit;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.alert-close:hover {
    opacity: 1;
}

/* =============================================================================
   LOGIN PAGE
   ============================================================================= */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
    position: relative;
    overflow: hidden;
}

.login-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s infinite linear;
}

@keyframes float {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.login-box {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.2);
    width: 100%;
    max-width: 420px;
    overflow: hidden;
    position: relative;
    z-index: 1;
    backdrop-filter: blur(10px);
}

.login-header {
    text-align: center;
    padding: 40px 40px 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.login-header .logo {
    font-size: 4rem;
    color: #3498db;
    margin-bottom: 20px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.login-header h2 {
    margin: 0 0 10px;
    color: #2c3e50;
    font-size: 1.8rem;
    font-weight: 700;
}

.login-header p {
    color: #6c757d;
    margin: 0;
    font-size: 14px;
}

.login-body {
    padding: 30px 40px 40px;
}

.password-input {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    font-size: 14px;
    padding: 0;
    z-index: 2;
}

.password-toggle:hover {
    color: #3498db;
}

.demo-accounts {
    margin-top: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    border: 1px solid #dee2e6;
}

.demo-accounts h4 {
    margin: 0 0 15px;
    color: #495057;
    font-size: 14px;
    text-align: center;
    font-weight: 600;
}

.demo-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.demo-item {
    font-size: 12px;
    color: #6c757d;
    text-align: center;
    padding: 10px;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.demo-item:hover {
    background: #3498db;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(52,152,219,0.3);
}

.demo-item strong {
    color: inherit;
}

.login-footer {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px;
    text-align: center;
    border-top: 1px solid #e9ecef;
}

.login-footer p {
    margin: 0;
    color: #6c757d;
    font-size: 12px;
}

/* =============================================================================
   DASHBOARD STATS
   ============================================================================= */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.stat-card {
    background: white;
    padding: 30px;
    border-radius: 16px;
    box-shadow: 0 8px 30px rgba(0,0,0,0.1);
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3498db, #2980b9);
}

.stat-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.stat-card .stat-icon {
    font-size: 3rem;
    margin-bottom: 20px;
    color: #3498db;
    opacity: 0.8;
}

.stat-card .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 10px;
    line-height: 1;
}

.stat-card .stat-label {
    color: #6c757d;
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-card .stat-change {
    margin-top: 15px;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 12px;
    display: inline-block;
}

.stat-change.positive {
    background-color: #d4edda;
    color: #27ae60;
}

.stat-change.negative {
    background-color: #f8d7da;
    color: #e74c3c;
}

/* =============================================================================
   MODAL
   ============================================================================= */
.modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal-dialog {
    position: relative;
    margin: 50px auto;
    max-width: 600px;
    width: 90%;
}

.modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    overflow: hidden;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-header {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background-color: rgba(255,255,255,0.2);
    transform: rotate(90deg);
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    background-color: #f8f9fa;
    padding: 20px 25px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* =============================================================================
   BADGE
   ============================================================================= */
.badge {
    display: inline-block;
    padding: 6px 12px;
    font-size: 12px;
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-primary { background-color: #3498db; color: white; }
.badge-success { background-color: #27ae60; color: white; }
.badge-warning { background-color: #f39c12; color: white; }
.badge-danger { background-color: #e74c3c; color: white; }
.badge-secondary { background-color: #95a5a6; color: white; }
.badge-info { background-color: #17a2b8; color: white; }
.badge-light { background-color: #f8f9fa; color: #495057; }
.badge-dark { background-color: #343a40; color: white; }

/* =============================================================================
   PAGINATION
   ============================================================================= */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
    margin: 30px 0;
}

.pagination a,
.pagination span {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
    padding: 0 12px;
    text-decoration: none;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    color: #495057;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background-color: #3498db;
    color: white;
    border-color: #3498db;
    transform: translateY(-2px);
}

.pagination .current {
    background-color: #3498db;
    color: white;
    border-color: #3498db;
}

.pagination .disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* =============================================================================
   SEARCH & FILTERS
   ============================================================================= */
.search-box {
    position: relative;
    margin-bottom: 25px;
}

.search-input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    font-size: 14px;
    transition: all 0.3s ease;
    background-color: white;
}

.search-input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52,152,219,0.1);
}

.search-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 16px;
}

.filter-bar {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    flex-wrap: wrap;
    align-items: center;
}

.filter-item {
    display: flex;
    flex-direction: column;
    min-width: 150px;
}

.filter-item label {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 5px;
    font-weight: 500;
}

.filter-item select,
.filter-item input {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    font-size: 14px;
}

/* =============================================================================
   LOADING & SPINNER
   ============================================================================= */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255,255,255,0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(3px);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    margin-top: 15px;
    color: #495057;
    font-size: 14px;
}

/* =============================================================================
   CALENDAR & DATE PICKER
   ============================================================================= */
.calendar-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0,0,0,0.1);
    padding: 20px;
    border: 1px solid #e9ecef;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.calendar-nav-btn {
    background: none;
    border: none;
    font-size: 18px;
    color: #3498db;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.calendar-nav-btn:hover {
    background-color: #f8f9fa;
    color: #2980b9;
}

.calendar-title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;
}

.calendar-day {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.calendar-day-header {
    background-color: #f8f9fa;
    color: #6c757d;
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    cursor: default;
}

.calendar-day:not(.calendar-day-header):hover {
    background-color: #e3f2fd;
    color: #1976d2;
    transform: scale(1.1);
}

.calendar-day.selected {
    background-color: #3498db;
    color: white;
}

.calendar-day.today {
    border-color: #3498db;
    color: #3498db;
    font-weight: 700;
}

.calendar-day.disabled {
    color: #ced4da;
    cursor: not-allowed;
}

.calendar-day.disabled:hover {
    background-color: transparent;
    transform: none;
}

/* =============================================================================
   BREADCRUMB
   ============================================================================= */
.breadcrumb {
    background: transparent;
    padding: 0;
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    color: #6c757d;
    font-size: 14px;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: '/';
    margin: 0 10px;
    color: #dee2e6;
}

.breadcrumb-item a {
    color: #3498db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-item a:hover {
    color: #2980b9;
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: #495057;
    font-weight: 500;
}

/* =============================================================================
   TOOLTIP
   ============================================================================= */
.tooltip {
    position: absolute;
    background-color: rgba(0,0,0,0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    z-index: 9999;
    white-space: nowrap;
    animation: tooltipFadeIn 0.2s ease;
}

.tooltip::before {
    content: '';
    position: absolute;
    top: -5px;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-bottom-color: rgba(0,0,0,0.9);
}

@keyframes tooltipFadeIn {
    from { opacity: 0; transform: translateY(5px); }
    to { opacity: 1; transform: translateY(0); }
}

/* =============================================================================
   PROGRESS BAR
   ============================================================================= */
.progress {
    height: 10px;
    background-color: #e9ecef;
    border-radius: 5px;
    overflow: hidden;
    margin: 15px 0;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2980b9);
    border-radius: 5px;
    transition: width 0.6s ease;
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-image: linear-gradient(
        45deg,
        rgba(255,255,255,.2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255,255,255,.2) 50%,
        rgba(255,255,255,.2) 75%,
        transparent 75%,
        transparent
    );
    background-size: 1rem 1rem;
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    from { background-position-x: 1rem; }
}

/* =============================================================================
   RESPONSIVE DESIGN
   ============================================================================= */

/* Large Desktop (1200px and up) */
@media (min-width: 1200px) {
    .container {
        max-width: 1140px;
    }

    .table td:last-child {
        width: 180px;
        min-width: 180px;
    }
}

/* Desktop (992px to 1199px) */
@media (max-width: 1199px) {
    .container {
        padding: 0 15px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }

    .card-body {
        padding: 18px;
    }
}

/* Tablet (768px to 991px) */
@media (max-width: 991px) {
    .sidebar {
        width: 70px;
    }

    .sidebar .nav-divider span,
    .sidebar-nav ul li a span {
        display: none;
    }

    .sidebar-nav ul li a {
        padding: 15px;
        justify-content: center;
    }

    .main-content {
        margin-left: 70px;
    }

    .form-row {
        flex-direction: column;
        gap: 0;
    }

    .form-row .form-group {
        min-width: 100%;
        margin-bottom: 16px;
    }

    .btn-group {
        flex-wrap: wrap;
        gap: 4px;
    }

    .table td:last-child {
        width: 140px;
        min-width: 140px;
    }

    .table .btn-group .btn {
        padding: 3px 6px;
        font-size: 10px;
        min-height: 24px;
    }
}

/* Mobile Large (576px to 767px) */
@media (max-width: 767px) {
    .header h1 {
        font-size: 1.1rem;
    }

    .header-content {
        padding: 12px 0;
    }

    .sidebar {
        width: 0;
        overflow: hidden;
        transform: translateX(-100%);
        transition: all 0.3s ease;
    }

    .sidebar.show {
        width: 260px;
        transform: translateX(0);
        box-shadow: 2px 0 10px rgba(0,0,0,0.3);
    }

    .main-content {
        margin-left: 0;
        padding: 16px 12px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .card-body,
    .card-header,
    .card-footer {
        padding: 16px 12px;
    }

    .table-responsive {
        border-radius: 6px;
        margin: 0 -12px;
        border-left: none;
        border-right: none;
    }

    .table {
        font-size: 11px;
    }

    .table th,
    .table td {
        padding: 8px 6px;
        font-size: 11px;
    }

    .table td:last-child {
        width: 100px;
        min-width: 100px;
        padding: 6px 4px;
    }

    .table .btn-group {
        flex-direction: column;
        width: 100%;
        border-radius: 3px;
    }

    .table .btn-group .btn {
        padding: 2px 4px;
        font-size: 9px;
        min-height: 22px;
        border-radius: 0;
        margin-bottom: 1px;
    }

    .table .btn-group .btn:first-child {
        border-radius: 3px 3px 0 0;
    }

    .table .btn-group .btn:last-child {
        border-radius: 0 0 3px 3px;
        margin-bottom: 0;
    }

    .table .btn-group .btn i {
        font-size: 8px;
        margin-right: 2px;
    }

    .table .dropdown-menu {
        font-size: 10px;
        min-width: 120px;
        right: -10px;
    }

    .table .dropdown-item {
        padding: 3px 6px;
        font-size: 10px;
    }

    .badge {
        font-size: 8px;
        padding: 2px 4px;
    }

    .filter-bar {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .filter-item {
        min-width: 100%;
    }

    .modal-dialog {
        margin: 16px;
        width: calc(100% - 32px);
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 16px;
    }

    .pagination {
        flex-wrap: wrap;
        gap: 2px;
        justify-content: center;
    }

    .pagination a,
    .pagination span {
        min-width: 32px;
        height: 32px;
        font-size: 11px;
        padding: 0 8px;
    }

    /* Hide less important columns on mobile */
    .table th:nth-child(3),
    .table td:nth-child(3),
    .table th:nth-child(6),
    .table td:nth-child(6) {
        display: none;
    }
}

/* Mobile Small (up to 575px) */
@media (max-width: 575px) {
    .header-content {
        padding: 10px 0;
    }

    .header h1 {
        font-size: 1rem;
    }

    .user-info {
        font-size: 0.75rem;
    }

    .user-info span:first-child {
        display: none;
    }

    .main-content {
        padding: 12px 8px;
    }

    .page-header h1 {
        font-size: 18px;
    }

    .card {
        margin-bottom: 16px;
        border-radius: 6px;
    }

    .card-body,
    .card-header,
    .card-footer {
        padding: 12px 10px;
    }

    .btn {
        padding: 8px 12px;
        font-size: 12px;
        min-height: 34px;
    }

    .btn-sm {
        padding: 4px 8px;
        font-size: 10px;
        min-height: 28px;
    }

    .form-control {
        padding: 8px 10px;
        font-size: 12px;
    }

    .form-label {
        font-size: 11px;
    }

    .table {
        font-size: 10px;
    }

    .table th,
    .table td {
        padding: 6px 4px;
        font-size: 10px;
    }

    .table td:last-child {
        width: 80px;
        min-width: 80px;
        padding: 4px 2px;
    }

    .table .btn-group .btn {
        padding: 1px 3px;
        font-size: 8px;
        min-height: 20px;
    }

    .table .btn-group .btn i {
        font-size: 7px;
        margin: 0;
    }

    .stats-card .stat-number {
        font-size: 1.8rem;
    }

    .stats-card .stat-icon {
        font-size: 2rem;
    }

    .stats-card .card-body {
        padding: 16px 12px;
    }

    .modal-dialog {
        margin: 12px;
        width: calc(100% - 24px);
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 12px;
    }

    /* Hide more columns on very small screens */
    .table th:nth-child(4),
    .table td:nth-child(4),
    .table th:nth-child(7),
    .table td:nth-child(7) {
        display: none;
    }
}

/* Landscape orientation adjustments */
@media (max-height: 500px) and (orientation: landscape) {
    .sidebar {
        height: 100vh;
        overflow-y: auto;
    }

    .main-content {
        padding: 12px;
    }

    .modal-dialog {
        margin: 8px;
        max-height: calc(100vh - 16px);
        overflow-y: auto;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .table {
        font-size: 13px;
    }

    .btn {
        font-size: 13px;
    }

    .form-control {
        font-size: 13px;
    }
}

/* =============================================================================
   PRINT STYLES
   ============================================================================= */
@media print {
    .header,
    .sidebar,
    .btn,
    .no-print {
        display: none !important;
    }
    
    .main-content {
        margin: 0;
        padding: 0;
        background: white;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #000;
        break-inside: avoid;
    }
    
    .table {
        border-collapse: collapse;
    }
    
    .table th,
    .table td {
        border: 1px solid #000;
        padding: 8px;
    }
    
    .alert {
        border: 1px solid #000;
        background: white !important;
    }
}

/* =============================================================================
   CUSTOM SCROLLBAR
   ============================================================================= */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* =============================================================================
   ACCESSIBILITY
   ============================================================================= */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.focus-outline:focus {
    outline: 2px solid #3498db;
    outline-offset: 2px;
}

/* =============================================================================
   ANIMATIONS & INTERACTIONS
   ============================================================================= */

/* Base Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-30px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(30px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes scaleIn {
    from {
        transform: scale(0.9);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-2px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(2px);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(52,152,219,0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(52,152,219,0.8);
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease;
}

.fade-in-up {
    animation: fadeInUp 0.5s ease;
}

.fade-in-down {
    animation: fadeInDown 0.5s ease;
}

.slide-in-up {
    animation: slideInUp 0.5s ease;
}

.slide-in-left {
    animation: slideInLeft 0.5s ease;
}

.slide-in-right {
    animation: slideInRight 0.5s ease;
}

.scale-in {
    animation: scaleIn 0.3s ease;
}

.bounce {
    animation: bounce 1s ease;
}

.pulse {
    animation: pulse 2s infinite;
}

.shake {
    animation: shake 0.5s ease;
}

.glow {
    animation: glow 2s infinite;
}

/* Hover Effects */
.hover-lift {
    transition: all 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.hover-scale {
    transition: transform 0.2s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: transform 0.3s ease;
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

.hover-glow {
    transition: all 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 15px rgba(52,152,219,0.5);
}

/* Interactive Elements */
.interactive {
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.interactive::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
    pointer-events: none;
}

.interactive:hover::before {
    width: 300px;
    height: 300px;
}

.interactive:active {
    transform: scale(0.98);
}

/* Smooth Transitions */
* {
    transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;
}

.smooth-all {
    transition: all 0.3s ease;
}

.smooth-transform {
    transition: transform 0.3s ease;
}

.smooth-opacity {
    transition: opacity 0.3s ease;
}

/* Loading States */
.loading-state {
    position: relative;
    pointer-events: none;
}

.loading-state::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Micro Interactions */
.btn {
    position: relative;
    overflow: hidden;
    transform: translateZ(0);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

/* Card Interactions */
.card {
    transform: translateZ(0);
    backface-visibility: hidden;
}

.card:hover {
    transform: translateY(-2px) translateZ(0);
}

/* Table Row Interactions */
.table tbody tr {
    transition: all 0.2s ease;
    position: relative;
}

.table tbody tr::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 0;
    background: linear-gradient(90deg, #3498db, transparent);
    transition: width 0.3s ease;
}

.table tbody tr:hover::before {
    width: 4px;
}

/* Form Interactions */
.form-control {
    position: relative;
    transition: all 0.3s ease;
}

.form-control:focus {
    transform: translateY(-1px);
}

.form-group {
    position: relative;
}

.form-group::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: #3498db;
    transition: width 0.3s ease;
}

.form-group:focus-within::after {
    width: 100%;
}

/* Badge Interactions */
.badge {
    transition: all 0.2s ease;
    cursor: default;
}

.badge:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

/* Icon Interactions */
i, .icon {
    transition: all 0.2s ease;
}

.btn i {
    transition: transform 0.2s ease;
}

.btn:hover i {
    transform: scale(1.1);
}

/* Stagger Animation for Lists */
.stagger-animation > * {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.5s ease forwards;
}

.stagger-animation > *:nth-child(1) { animation-delay: 0.1s; }
.stagger-animation > *:nth-child(2) { animation-delay: 0.2s; }
.stagger-animation > *:nth-child(3) { animation-delay: 0.3s; }
.stagger-animation > *:nth-child(4) { animation-delay: 0.4s; }
.stagger-animation > *:nth-child(5) { animation-delay: 0.5s; }
.stagger-animation > *:nth-child(6) { animation-delay: 0.6s; }
.stagger-animation > *:nth-child(7) { animation-delay: 0.7s; }
.stagger-animation > *:nth-child(8) { animation-delay: 0.8s; }
.stagger-animation > *:nth-child(9) { animation-delay: 0.9s; }
.stagger-animation > *:nth-child(10) { animation-delay: 1s; }

/* =============================================================================
   UTILITY CLASSES
   ============================================================================= */
.shadow-sm { box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
.shadow { box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
.shadow-lg { box-shadow: 0 8px 16px rgba(0,0,0,0.15); }

.rounded { border-radius: 8px; }
.rounded-lg { border-radius: 12px; }
.rounded-xl { border-radius: 16px; }
.rounded-full { border-radius: 50%; }

.border { border: 1px solid #dee2e6; }
.border-top { border-top: 1px solid #dee2e6; }
.border-bottom { border-bottom: 1px solid #dee2e6; }
.border-left { border-left: 1px solid #dee2e6; }
.border-right { border-right: 1px solid #dee2e6; }

.text-primary { color: #3498db !important; }
.text-success { color: #27ae60 !important; }
.text-warning { color: #f39c12 !important; }
.text-danger { color: #e74c3c !important; }
.text-secondary { color: #6c757d !important; }
.text-muted { color: #6c757d !important; }
.text-white { color: #ffffff !important; }
.text-dark { color: #343a40 !important; }

.bg-primary { background-color: #3498db !important; }
.bg-success { background-color: #27ae60 !important; }
.bg-warning { background-color: #f39c12 !important; }
.bg-danger { background-color: #e74c3c !important; }
.bg-secondary { background-color: #6c757d !important; }
.bg-light { background-color: #f8f9fa !important; }
.bg-white { background-color: #ffffff !important; }
.bg-dark { background-color: #343a40 !important; }

.font-weight-bold { font-weight: 700; }
.font-weight-normal { font-weight: 400; }
.font-weight-light { font-weight: 300; }

.text-uppercase { text-transform: uppercase; }
.text-lowercase { text-transform: lowercase; }
.text-capitalize { text-transform: capitalize; }

.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }

.position-relative { position: relative; }
.position-absolute { position: absolute; }
.position-fixed { position: fixed; }
.position-sticky { position: sticky; }

.w-25 { width: 25%; }
.w-50 { width: 50%; }
.w-75 { width: 75%; }
.w-100 { width: 100%; }

.h-25 { height: 25%; }
.h-50 { height: 50%; }
.h-75 { height: 75%; }
.h-100 { height: 100%; }