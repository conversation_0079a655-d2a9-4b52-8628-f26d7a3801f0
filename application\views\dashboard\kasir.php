<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h1><i class="fas fa-cash-register"></i> Dashboard Kasir</h1>
            <p class="text-muted">Selamat datang, <strong><?php echo $this->session->userdata('full_name'); ?></strong> - <PERSON><PERSON>la booking dan transaksi dengan mudah</p>
        </div>
        <div class="col-auto">
            <div class="btn-group" role="group">
                <a href="<?php echo site_url('booking/create'); ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Booking Baru
                </a>
                <a href="<?php echo site_url('transaksi'); ?>" class="btn btn-success">
                    <i class="fas fa-cash-register"></i> Pembayaran
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Real-time Stats -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h2 class="mb-1" id="pending-count"><?php echo $stats['pending_bookings']; ?></h2>
                        <p class="mb-0">Booking Pending</p>
                        <small class="opacity-75">Perlu dikonfirmasi</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="<?php echo site_url('booking?status=pending'); ?>" class="text-white text-decoration-none">
                    <small><i class="fas fa-arrow-right"></i> Lihat Detail</small>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h2 class="mb-1" id="confirmed-count"><?php echo $stats['confirmed_bookings']; ?></h2>
                        <p class="mb-0">Siap Bayar</p>
                        <small class="opacity-75">Booking dikonfirmasi</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="<?php echo site_url('transaksi'); ?>" class="text-white text-decoration-none">
                    <small><i class="fas fa-arrow-right"></i> Proses Pembayaran</small>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h2 class="mb-1" id="today-transactions"><?php echo $stats['today_transactions']; ?></h2>
                        <p class="mb-0">Transaksi Hari Ini</p>
                        <small class="opacity-75">Total: Rp <?php echo number_format($stats['today_revenue'], 0, ',', '.'); ?></small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-money-bill-wave fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="<?php echo site_url('transaksi/hari-ini'); ?>" class="text-white text-decoration-none">
                    <small><i class="fas fa-arrow-right"></i> Lihat Transaksi</small>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-dark h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h2 class="mb-1" id="my-transactions"><?php echo $stats['my_transactions']; ?></h2>
                        <p class="mb-0">Transaksi Saya</p>
                        <small class="opacity-75">Total bulan ini</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-check fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="<?php echo site_url('transaksi/riwayat'); ?>" class="text-dark text-decoration-none">
                    <small><i class="fas fa-arrow-right"></i> Riwayat Saya</small>
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Quick Actions -->
    <div class="col-lg-3 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt"></i> Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?php echo site_url('booking/create'); ?>" class="btn btn-primary">
                        <i class="fas fa-plus-circle"></i> Buat Booking
                    </a>
                    <a href="<?php echo site_url('transaksi'); ?>" class="btn btn-success">
                        <i class="fas fa-cash-register"></i> Proses Pembayaran
                    </a>
                    <a href="<?php echo site_url('member/search'); ?>" class="btn btn-info">
                        <i class="fas fa-search"></i> Cari Member
                    </a>
                    <a href="<?php echo site_url('booking?status=pending'); ?>" class="btn btn-warning">
                        <i class="fas fa-clock"></i> Booking Pending
                    </a>
                    <a href="<?php echo site_url('booking?status=dikonfirmasi'); ?>" class="btn btn-outline-success">
                        <i class="fas fa-check-circle"></i> Siap Bayar
                    </a>
                    <a href="<?php echo site_url('lapangan'); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-map-marked-alt"></i> Info Lapangan
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Today's Schedule -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-calendar-day"></i> Jadwal Hari Ini
                </h6>
            </div>
            <div class="card-body p-0">
                <?php if (empty($today_bookings)): ?>
                    <div class="text-center py-3">
                        <i class="fas fa-calendar-times fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">Tidak ada booking hari ini</p>
                    </div>
                <?php else: ?>
                    <div class="schedule-list">
                        <?php foreach (array_slice($today_bookings, 0, 5) as $booking): ?>
                            <div class="schedule-item">
                                <div class="time">
                                    <?php echo date('H:i', strtotime($booking->jam_mulai)); ?>
                                </div>
                                <div class="details">
                                    <strong><?php echo $booking->nama_lapangan; ?></strong>
                                    <br>
                                    <small class="text-muted"><?php echo $booking->nama_pemesan; ?></small>
                                    <span class="badge badge-sm bg-<?php echo ($booking->status_booking == 'pending') ? 'warning' : (($booking->status_booking == 'dikonfirmasi') ? 'info' : 'success'); ?>">
                                        <?php echo ucfirst($booking->status_booking); ?>
                                    </span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        
                        <?php if (count($today_bookings) > 5): ?>
                            <div class="text-center pt-2 border-top">
                                <a href="<?php echo site_url('booking?tanggal=' . date('Y-m-d')); ?>" class="btn btn-sm btn-outline-primary">
                                    Lihat Semua (<?php echo count($today_bookings); ?>)
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Pending Bookings -->
    <div class="col-lg-5 mb-4">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-clock"></i> Booking Pending
                            <span class="badge bg-warning"><?php echo count($pending_bookings); ?></span>
                        </h5>
                    </div>
                    <div class="col-auto">
                        <a href="<?php echo site_url('booking?status=pending'); ?>" class="btn btn-sm btn-outline-primary">
                            Lihat Semua
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (empty($pending_bookings)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h6 class="text-muted">Semua booking sudah dikonfirmasi!</h6>
                        <p class="text-muted mb-0">Tidak ada booking yang menunggu konfirmasi</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Booking</th>
                                    <th>Pemesan</th>
                                    <th>Jadwal</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach (array_slice($pending_bookings, 0, 5) as $booking): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo $booking->kode_booking; ?></strong>
                                            <br>
                                            <small class="text-muted"><?php echo $booking->nama_lapangan; ?></small>
                                        </td>
                                        <td>
                                            <strong><?php echo $booking->nama_pemesan; ?></strong>
                                            <br>
                                            <small class="text-muted"><?php echo $booking->telepon_pemesan; ?></small>
                                            <?php if ($booking->customer_type == 'member'): ?>
                                                <br><span class="badge bg-success badge-sm">Member</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <strong><?php echo date('d M', strtotime($booking->tanggal_booking)); ?></strong>
                                            <br>
                                            <small class="text-muted">
                                                <?php echo date('H:i', strtotime($booking->jam_mulai)); ?> - 
                                                <?php echo date('H:i', strtotime($booking->jam_selesai)); ?>
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <button type="button" class="btn btn-success" 
                                                        onclick="quickConfirm(<?php echo $booking->id; ?>)"
                                                        title="Konfirmasi">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <a href="<?php echo site_url('booking/view/' . $booking->id); ?>" 
                                                   class="btn btn-outline-primary" title="Detail">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Ready for Payment -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-money-bill-wave"></i> Siap Bayar
                            <span class="badge bg-info"><?php echo count($payment_ready); ?></span>
                        </h5>
                    </div>
                    <div class="col-auto">
                        <a href="<?php echo site_url('transaksi'); ?>" class="btn btn-sm btn-success">
                            Pembayaran
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (empty($payment_ready)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-wallet fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">Tidak ada pembayaran pending</h6>
                        <p class="text-muted mb-0">Semua booking sudah dibayar</p>
                    </div>
                <?php else: ?>
                    <div class="payment-list">
                        <?php foreach (array_slice($payment_ready, 0, 4) as $booking): ?>
                            <div class="payment-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong><?php echo $booking->kode_booking; ?></strong>
                                        <br>
                                        <small class="text-muted"><?php echo $booking->customer_name; ?></small>
                                        <br>
                                        <strong class="text-success">
                                            Rp <?php echo number_format($booking->harga_total, 0, ',', '.'); ?>
                                        </strong>
                                    </div>
                                    <div>
                                        <a href="<?php echo site_url('transaksi?booking_id=' . $booking->id); ?>" 
                                           class="btn btn-sm btn-success">
                                            <i class="fas fa-cash-register"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Recent Transactions -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-history"></i> Transaksi Terakhir
                </h6>
            </div>
            <div class="card-body p-0">
                <?php if (empty($recent_transactions)): ?>
                    <div class="text-center py-3">
                        <i class="fas fa-receipt fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">Belum ada transaksi</p>
                    </div>
                <?php else: ?>
                    <div class="transaction-list">
                        <?php foreach (array_slice($recent_transactions, 0, 3) as $transaction): ?>
                            <div class="transaction-item">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <strong><?php echo $transaction->kode_booking; ?></strong>
                                        <br>
                                        <small class="text-muted">
                                            <?php echo date('d M H:i', strtotime($transaction->tanggal_transaksi)); ?>
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <strong class="text-success">
                                            Rp <?php echo number_format($transaction->jumlah_bayar, 0, ',', '.'); ?>
                                        </strong>
                                        <br>
                                        <span class="badge bg-<?php echo ($transaction->metode_pembayaran == 'tunai') ? 'success' : 'info'; ?> badge-sm">
                                            <?php echo ucfirst($transaction->metode_pembayaran); ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Performance Chart -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-line"></i> Performance Minggu Ini
                        </h5>
                    </div>
                    <div class="col-auto">
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary active" onclick="changeChart('week')">
                                Minggu
                            </button>
                            <button type="button" class="btn btn-outline-primary" onclick="changeChart('month')">
                                Bulan
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <canvas id="performanceChart" height="100"></canvas>
            </div>
        </div>
    </div>
</div>

<style>
/* Card enhancements */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 12px;
}

.card-header {
    background: linear-gradient(45deg, #f8f9fa, #ffffff);
    border-bottom: 1px solid #e3e6f0;
    border-radius: 12px 12px 0 0;
}

/* Stats cards */
.card.bg-primary, .card.bg-info, .card.bg-success, .card.bg-warning {
    border: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* Schedule list */
.schedule-list {
    max-height: 300px;
    overflow-y: auto;
}

.schedule-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
}

.schedule-item:last-child {
    border-bottom: none;
}

.schedule-item .time {
    width: 60px;
    font-weight: 600;
    color: #007bff;
    font-size: 14px;
}

.schedule-item .details {
    flex: 1;
    margin-left: 12px;
}

/* Payment list */
.payment-list {
    max-height: 250px;
    overflow-y: auto;
}

.payment-item {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
}

.payment-item:last-child {
    border-bottom: none;
}

/* Transaction list */
.transaction-list {
    max-height: 200px;
    overflow-y: auto;
}

.transaction-item {
    padding: 10px 16px;
    border-bottom: 1px solid #f0f0f0;
}

.transaction-item:last-child {
    border-bottom: none;
}

/* Badge sizes */
.badge-sm {
    font-size: 0.65em;
    padding: 0.25em 0.5em;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-body.p-0 {
        padding: 0.5rem !important;
    }
    
    .btn-group-sm .btn {
        padding: 0.125rem 0.25rem;
        font-size: 0.75rem;
    }
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Chart container */
#performanceChart {
    max-height: 300px;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
let performanceChart;

$(document).ready(function() {
    // Initialize performance chart
    initPerformanceChart();
    
    // Auto-refresh stats every 30 seconds
    setInterval(refreshStats, 30000);
    
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

function quickConfirm(bookingId) {
    if (confirm('Konfirmasi booking ini?')) {
        // Show loading state
        const btn = event.target.closest('button');
        const originalContent = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        btn.disabled = true;
        
        // Submit form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `<?php echo site_url('booking/update_status/'); ?>${bookingId}`;
        
        const statusInput = document.createElement('input');
        statusInput.type = 'hidden';
        statusInput.name = 'status';
        statusInput.value = 'dikonfirmasi';
        
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '<?php echo $this->security->get_csrf_token_name(); ?>';
        csrfInput.value = '<?php echo $this->security->get_csrf_hash(); ?>';
        
        form.appendChild(statusInput);
        form.appendChild(csrfInput);
        document.body.appendChild(form);
        form.submit();
    }
}

function refreshStats() {
    fetch('<?php echo site_url('dashboard/get_kasir_stats_ajax'); ?>')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('pending-count').textContent = data.stats.pending_bookings;
                document.getElementById('confirmed-count').textContent = data.stats.confirmed_bookings;
                document.getElementById('today-transactions').textContent = data.stats.today_transactions;
                document.getElementById('my-transactions').textContent = data.stats.my_transactions;
            }
        })
        .catch(error => console.log('Refresh failed:', error));
}

function initPerformanceChart() {
    const ctx = document.getElementById('performanceChart').getContext('2d');
    
    // Sample data - replace with actual data from PHP
    const chartData = {
        labels: ['Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab', 'Min'],
        datasets: [{
            label: 'Booking',
            data: [12, 19, 8, 15, 25, 22, 18],
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4,
            fill: true
        }, {
            label: 'Transaksi',
            data: [8, 15, 6, 12, 20, 18, 15],
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            tension: 0.4,
            fill: true
        }]
    };
    
    performanceChart = new Chart(ctx, {
        type: 'line',
        data: chartData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            },
            elements: {
                point: {
                    radius: 4,
                    hoverRadius: 6
                }
            }
        }
    });
}

function changeChart(period) {
    // Update active button
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // Update chart data based on period
    if (period === 'month') {
        // Sample monthly data
        performanceChart.data.labels = ['Week 1', 'Week 2', 'Week 3', 'Week 4'];
        performanceChart.data.datasets[0].data = [45, 52, 38, 48];
        performanceChart.data.datasets[1].data = [38, 45, 32, 42];
    } else {
        // Weekly data
        performanceChart.data.labels = ['Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab', 'Min'];
        performanceChart.data.datasets[0].data = [12, 19, 8, 15, 25, 22, 18];
        performanceChart.data.datasets[1].data = [8, 15, 6, 12, 20, 18, 15];
    }
    
    performanceChart.update();
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl + B = New booking
    if (e.ctrlKey && e.key === 'b') {
        e.preventDefault();
        window.location.href = '<?php echo site_url('booking/create'); ?>';
    }
    
    // Ctrl + P = Payment
    if (e.ctrlKey && e.key === 'p') {
        e.preventDefault();
        window.location.href = '<?php echo site_url('transaksi'); ?>';
    }
    
    // Ctrl + M = Member search
    if (e.ctrlKey && e.key === 'm') {
        e.preventDefault();
        window.location.href = '<?php echo site_url('member/search'); ?>';
    }
});

// Show keyboard shortcuts hint
setTimeout(() => {
    if (localStorage.getItem('kasir_shortcuts_shown') !== 'true') {
        const toast = document.createElement('div');
        toast.className = 'toast position-fixed bottom-0 end-0 m-3';
        toast.innerHTML = `
            <div class="toast-header">
                <i class="fas fa-keyboard me-2"></i>
                <strong class="me-auto">Keyboard Shortcuts</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                <small>
                    <strong>Ctrl + B:</strong> Booking Baru<br>
                    <strong>Ctrl + P:</strong> Pembayaran<br>
                    <strong>Ctrl + M:</strong> Cari Member
                </small>
            </div>
        `;
        
        document.body.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        localStorage.setItem('kasir_shortcuts_shown', 'true');
    }
}, 3000);
</script>