<!-- <PERSON> Header -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">
                <i class="fas fa-user-circle"></i>
                Profil Saya
            </h1>
            <p class="page-subtitle">Kelola informasi profil dan keamanan akun</p>
        </div>
        <div class="page-actions">
            <a href="<?php echo site_url('dashboard'); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                Kembali ke Dashboard
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- Profile Information -->
    <div class="col-md-8">
        <!-- Basic Info Card -->
        <div class="card">
            <div class="card-header">
                <h3>
                    <i class="fas fa-user"></i>
                    Informasi Profil
                </h3>
            </div>
            <div class="card-body">
                <?php echo form_open('users/update_profile', array('id' => 'profile-form')); ?>
                
                    <!-- Full Name -->
                    <div class="form-group">
                        <label for="full_name" class="form-label required">
                            <i class="fas fa-id-card"></i>
                            Nama Lengkap
                        </label>
                        <input type="text" 
                               id="full_name" 
                               name="full_name" 
                               class="form-control <?php echo form_error('full_name') ? 'is-invalid' : ''; ?>"
                               value="<?php echo set_value('full_name', $user->full_name); ?>"
                               required>
                        
                        <?php if (form_error('full_name')): ?>
                        <div class="invalid-feedback">
                            <?php echo form_error('full_name'); ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Email -->
                    <div class="form-group">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope"></i>
                            Email
                        </label>
                        <input type="email" 
                               id="email" 
                               name="email" 
                               class="form-control <?php echo form_error('email') ? 'is-invalid' : ''; ?>"
                               value="<?php echo set_value('email', $user->email); ?>"
                               placeholder="<EMAIL>">
                        
                        <?php if (form_error('email')): ?>
                        <div class="invalid-feedback">
                            <?php echo form_error('email'); ?>
                        </div>
                        <?php endif; ?>
                        
                        <small class="form-text text-muted">
                            Email untuk notifikasi sistem
                        </small>
                    </div>
                    
                    <!-- Phone -->
                    <div class="form-group">
                        <label for="phone" class="form-label">
                            <i class="fas fa-phone"></i>
                            Nomor Telepon
                        </label>
                        <input type="tel" 
                               id="phone" 
                               name="phone" 
                               class="form-control <?php echo form_error('phone') ? 'is-invalid' : ''; ?>"
                               value="<?php echo set_value('phone', $user->phone); ?>"
                               placeholder="08123456789">
                        
                        <?php if (form_error('phone')): ?>
                        <div class="invalid-feedback">
                            <?php echo form_error('phone'); ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary" id="update-profile-btn">
                            <i class="fas fa-save"></i>
                            Simpan Perubahan
                        </button>
                    </div>
                    
                <?php echo form_close(); ?>
            </div>
        </div>

        <!-- Change Password Card -->
        <div class="card mt-4">
            <div class="card-header">
                <h3>
                    <i class="fas fa-key"></i>
                    Ubah Password
                </h3>
            </div>
            <div class="card-body">
                <?php echo form_open('users/change_password', array('id' => 'password-form')); ?>
                
                    <!-- Current Password -->
                    <div class="form-group">
                        <label for="current_password" class="form-label required">
                            <i class="fas fa-lock"></i>
                            Password Saat Ini
                        </label>
                        <div class="password-input-group">
                            <input type="password" 
                                   id="current_password" 
                                   name="current_password" 
                                   class="form-control <?php echo form_error('current_password') ? 'is-invalid' : ''; ?>"
                                   required>
                            <button type="button" class="password-toggle" onclick="togglePassword('current_password')">
                                <i class="fas fa-eye" id="current_password-icon"></i>
                            </button>
                        </div>
                        
                        <?php if (form_error('current_password')): ?>
                        <div class="invalid-feedback">
                            <?php echo form_error('current_password'); ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- New Password -->
                    <div class="form-group">
                        <label for="new_password" class="form-label required">
                            <i class="fas fa-key"></i>
                            Password Baru
                        </label>
                        <div class="password-input-group">
                            <input type="password" 
                                   id="new_password" 
                                   name="new_password" 
                                   class="form-control <?php echo form_error('new_password') ? 'is-invalid' : ''; ?>"
                                   required>
                            <button type="button" class="password-toggle" onclick="togglePassword('new_password')">
                                <i class="fas fa-eye" id="new_password-icon"></i>
                            </button>
                        </div>
                        
                        <?php if (form_error('new_password')): ?>
                        <div class="invalid-feedback">
                            <?php echo form_error('new_password'); ?>
                        </div>
                        <?php endif; ?>
                        
                        <small class="form-text text-muted">
                            Minimal 6 karakter
                        </small>
                    </div>
                    
                    <!-- Confirm New Password -->
                    <div class="form-group">
                        <label for="confirm_password" class="form-label required">
                            <i class="fas fa-check-circle"></i>
                            Konfirmasi Password Baru
                        </label>
                        <div class="password-input-group">
                            <input type="password" 
                                   id="confirm_password" 
                                   name="confirm_password" 
                                   class="form-control <?php echo form_error('confirm_password') ? 'is-invalid' : ''; ?>"
                                   required>
                            <button type="button" class="password-toggle" onclick="togglePassword('confirm_password')">
                                <i class="fas fa-eye" id="confirm_password-icon"></i>
                            </button>
                        </div>
                        
                        <?php if (form_error('confirm_password')): ?>
                        <div class="invalid-feedback">
                            <?php echo form_error('confirm_password'); ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="form-actions">
                        <button type="submit" class="btn btn-warning" id="change-password-btn">
                            <i class="fas fa-key"></i>
                            Ubah Password
                        </button>
                    </div>
                    
                <?php echo form_close(); ?>
            </div>
        </div>
    </div>

    <!-- Sidebar Info -->
    <div class="col-md-4">
        <!-- Profile Summary -->
        <div class="card">
            <div class="card-header">
                <h3>
                    <i class="fas fa-info-circle"></i>
                    Ringkasan Akun
                </h3>
            </div>
            <div class="card-body">
                <div class="profile-summary">
                    <div class="profile-avatar">
                        <div class="avatar-placeholder">
                            <i class="fas fa-user fa-3x"></i>
                        </div>
                        <div class="profile-name">
                            <strong><?php echo $user->full_name; ?></strong>
                            <small class="d-block text-muted">@<?php echo $user->username; ?></small>
                        </div>
                    </div>
                    
                    <div class="profile-info mt-4">
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-user-tag"></i>
                                Role
                            </div>
                            <div class="info-value">
                                <?php 
                                $role_class = '';
                                $role_icon = '';
                                switch($user->role) {
                                    case 'admin':
                                        $role_class = 'badge-primary';
                                        $role_icon = '🔧';
                                        break;
                                    case 'kasir':
                                        $role_class = 'badge-info';
                                        $role_icon = '💰';
                                        break;
                                    case 'pimpinan':
                                        $role_class = 'badge-success';
                                        $role_icon = '📊';
                                        break;
                                }
                                ?>
                                <span class="badge <?php echo $role_class; ?> badge-lg">
                                    <?php echo $role_icon; ?> <?php echo ucfirst($user->role); ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-toggle-on"></i>
                                Status
                            </div>
                            <div class="info-value">
                                <?php if ($user->status == 'aktif'): ?>
                                    <span class="badge badge-success badge-lg">
                                        <i class="fas fa-check"></i>
                                        Aktif
                                    </span>
                                <?php else: ?>
                                    <span class="badge badge-danger badge-lg">
                                        <i class="fas fa-times"></i>
                                        Tidak Aktif
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-calendar-plus"></i>
                                Terdaftar
                            </div>
                            <div class="info-value">
                                <?php echo date('d M Y', strtotime($user->created_at)); ?>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-calendar-check"></i>
                                Terakhir Update
                            </div>
                            <div class="info-value">
                                <?php echo date('d M Y H:i', strtotime($user->updated_at)); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Tips -->
        <div class="card mt-4">
            <div class="card-header">
                <h3>
                    <i class="fas fa-shield-alt"></i>
                    Tips Keamanan
                </h3>
            </div>
            <div class="card-body">
                <div class="security-tips">
                    <div class="tip-item">
                        <i class="fas fa-check-circle text-success"></i>
                        <span>Gunakan password yang kuat (minimal 6 karakter)</span>
                    </div>
                    <div class="tip-item">
                        <i class="fas fa-check-circle text-success"></i>
                        <span>Jangan bagikan password ke orang lain</span>
                    </div>
                    <div class="tip-item">
                        <i class="fas fa-check-circle text-success"></i>
                        <span>Logout setelah selesai menggunakan sistem</span>
                    </div>
                    <div class="tip-item">
                        <i class="fas fa-check-circle text-success"></i>
                        <span>Perbarui informasi profil secara berkala</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Role Permissions -->
        <div class="card mt-4">
            <div class="card-header">
                <h3>
                    <i class="fas fa-user-shield"></i>
                    Hak Akses Anda
                </h3>
            </div>
            <div class="card-body">
                <div class="permissions-list">
                    <?php if ($user->role == 'admin'): ?>
                        <div class="permission-item active">
                            <i class="fas fa-users"></i>
                            <span>Kelola Pengguna</span>
                        </div>
                        <div class="permission-item active">
                            <i class="fas fa-map-marked-alt"></i>
                            <span>Kelola Lapangan</span>
                        </div>
                        <div class="permission-item active">
                            <i class="fas fa-calendar-alt"></i>
                            <span>Kelola Booking</span>
                        </div>
                        <div class="permission-item active">
                            <i class="fas fa-cog"></i>
                            <span>Akses Penuh Sistem</span>
                        </div>
                    <?php elseif ($user->role == 'kasir'): ?>
                        <div class="permission-item active">
                            <i class="fas fa-cash-register"></i>
                            <span>Proses Pembayaran</span>
                        </div>
                        <div class="permission-item active">
                            <i class="fas fa-list"></i>
                            <span>Lihat Booking</span>
                        </div>
                        <div class="permission-item active">
                            <i class="fas fa-receipt"></i>
                            <span>Transaksi Kasir</span>
                        </div>
                        <div class="permission-item inactive">
                            <i class="fas fa-users"></i>
                            <span>Kelola Pengguna</span>
                        </div>
                    <?php elseif ($user->role == 'pimpinan'): ?>
                        <div class="permission-item active">
                            <i class="fas fa-chart-bar"></i>
                            <span>Lihat Laporan</span>
                        </div>
                        <div class="permission-item active">
                            <i class="fas fa-analytics"></i>
                            <span>Dashboard Analitik</span>
                        </div>
                        <div class="permission-item active">
                            <i class="fas fa-eye"></i>
                            <span>Monitoring Bisnis</span>
                        </div>
                        <div class="permission-item inactive">
                            <i class="fas fa-edit"></i>
                            <span>Edit Data</span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.profile-summary {
    text-align: center;
}

.profile-avatar {
    margin-bottom: 20px;
}

.avatar-placeholder {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    color: white;
}

.profile-name strong {
    font-size: 1.2rem;
    color: #2c3e50;
}

.profile-info .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f1f1;
}

.profile-info .info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: #6c757d;
    font-size: 14px;
}

.info-label i {
    margin-right: 8px;
    color: #3498db;
}

.badge-lg {
    font-size: 0.85rem;
    padding: 6px 12px;
}

.password-input-group {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    font-size: 14px;
    padding: 0;
    z-index: 3;
}

.password-toggle:hover {
    color: #3498db;
}

.security-tips .tip-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 12px;
    font-size: 14px;
}

.security-tips .tip-item:last-child {
    margin-bottom: 0;
}

.security-tips .tip-item i {
    margin-right: 10px;
    margin-top: 2px;
}

.permissions-list .permission-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    font-size: 14px;
    border-bottom: 1px solid #f1f1f1;
}

.permissions-list .permission-item:last-child {
    border-bottom: none;
}

.permissions-list .permission-item i {
    margin-right: 12px;
    width: 20px;
    text-align: center;
}

.permissions-list .permission-item.active {
    color: #28a745;
}

.permissions-list .permission-item.inactive {
    color: #6c757d;
    opacity: 0.6;
}

.form-actions {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.form-label.required::after {
    content: ' *';
    color: #e74c3c;
}

@media (max-width: 768px) {
    .profile-info .info-item {
        flex-direction: column;
        align-items: flex-start;
        text-align: left;
    }
    
    .profile-info .info-value {
        margin-top: 5px;
    }
    
    .page-header .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
    }
    
    .page-actions {
        margin-top: 15px;
        width: 100%;
    }
}
</style>

<script>
$(document).ready(function() {
    // Password confirmation validation
    $('#confirm_password').on('input', function() {
        const newPassword = $('#new_password').val();
        const confirmPassword = $(this).val();
        
        if (confirmPassword.length > 0) {
            if (newPassword === confirmPassword) {
                $(this).removeClass('is-invalid').addClass('is-valid');
            } else {
                $(this).removeClass('is-valid').addClass('is-invalid');
            }
        } else {
            $(this).removeClass('is-valid is-invalid');
        }
    });
    
    // Profile form submission
    $('#profile-form').on('submit', function(e) {
        e.preventDefault();
        
        if (validateProfileForm()) {
            showLoading($('#update-profile-btn'));
            this.submit();
        }
    });
    
    // Password form submission
    $('#password-form').on('submit', function(e) {
        e.preventDefault();
        
        if (validatePasswordForm()) {
            showLoading($('#change-password-btn'));
            this.submit();
        }
    });
});

function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '-icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

function validateProfileForm() {
    let isValid = true;
    
    // Check required fields
    const fullName = $('#full_name').val().trim();
    
    if (!fullName) {
        $('#full_name').addClass('is-invalid');
        isValid = false;
    } else {
        $('#full_name').removeClass('is-invalid');
    }
    
    // Validate email if provided
    const email = $('#email').val().trim();
    if (email && !isValidEmail(email)) {
        $('#email').addClass('is-invalid');
        isValid = false;
    } else {
        $('#email').removeClass('is-invalid');
    }
    
    if (!isValid) {
        showAlert('error', 'Mohon perbaiki kesalahan pada form');
    }
    
    return isValid;
}

function validatePasswordForm() {
    let isValid = true;
    
    // Check required fields
    const currentPassword = $('#current_password').val();
    const newPassword = $('#new_password').val();
    const confirmPassword = $('#confirm_password').val();
    
    if (!currentPassword) {
        $('#current_password').addClass('is-invalid');
        isValid = false;
    } else {
        $('#current_password').removeClass('is-invalid');
    }
    
    if (!newPassword || newPassword.length < 6) {
        $('#new_password').addClass('is-invalid');
        isValid = false;
    } else {
        $('#new_password').removeClass('is-invalid');
    }
    
    if (!confirmPassword || newPassword !== confirmPassword) {
        $('#confirm_password').addClass('is-invalid');
        isValid = false;
    } else {
        $('#confirm_password').removeClass('is-invalid');
    }
    
    if (!isValid) {
        showAlert('error', 'Mohon perbaiki kesalahan pada form password');
    }
    
    return isValid;
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function showLoading(element) {
    element.prop('disabled', true);
    const originalText = element.html();
    element.data('original-text', originalText);
    element.html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');
}
</script>