<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h1><i class="fas fa-eye"></i> Detail Booking</h1>
            <p class="text-muted">Informasi lengkap booking: <strong><?php echo $booking->kode_booking; ?></strong></p>
        </div>
        <div class="col-auto">
            <div class="btn-group" role="group">
                <a href="<?php echo site_url('booking'); ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali
                </a>
                
                <?php if (in_array($this->session->userdata('role'), ['admin', 'kasir']) && $booking->status_booking == 'pending'): ?>
                    <a href="<?php echo site_url('booking/edit/' . $booking->id); ?>" class="btn btn-warning">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                <?php endif; ?>
                
                <button type="button" class="btn btn-info" onclick="window.print()">
                    <i class="fas fa-print"></i> Print
                </button>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Main Booking Info -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-calendar-alt"></i> Informasi Booking
                        </h5>
                    </div>
                    <div class="col-auto">
                        <?php
                        $status_class = '';
                        $status_icon = '';
                        switch ($booking->status_booking) {
                            case 'pending':
                                $status_class = 'bg-warning';
                                $status_icon = 'fas fa-clock';
                                break;
                            case 'dikonfirmasi':
                                $status_class = 'bg-info';
                                $status_icon = 'fas fa-check';
                                break;
                            case 'selesai':
                                $status_class = 'bg-success';
                                $status_icon = 'fas fa-check-double';
                                break;
                            case 'dibatalkan':
                                $status_class = 'bg-danger';
                                $status_icon = 'fas fa-times';
                                break;
                        }
                        ?>
                        <span class="badge <?php echo $status_class; ?> fs-6">
                            <i class="<?php echo $status_icon; ?>"></i>
                            <?php echo ucfirst($booking->status_booking); ?>
                        </span>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold" width="40%">Kode Booking:</td>
                                <td><?php echo $booking->kode_booking; ?></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Tanggal Booking:</td>
                                <td><?php echo date('d F Y', strtotime($booking->tanggal_booking)); ?></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Waktu:</td>
                                <td>
                                    <?php echo date('H:i', strtotime($booking->jam_mulai)); ?> - 
                                    <?php echo date('H:i', strtotime($booking->jam_selesai)); ?>
                                    <span class="badge bg-primary ms-2"><?php echo $booking->durasi_jam; ?> jam</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Lapangan:</td>
                                <td>
                                    <strong><?php echo $booking->nama_lapangan; ?></strong>
                                    <?php if ($booking->lapangan_deskripsi): ?>
                                        <br><small class="text-muted"><?php echo $booking->lapangan_deskripsi; ?></small>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold" width="40%">Tipe Customer:</td>
                                <td>
                                    <?php if ($booking->customer_type == 'member'): ?>
                                        <span class="badge bg-success">
                                            <i class="fas fa-star"></i> Member
                                        </span>
                                        <?php if ($booking->member_code): ?>
                                            <br><span class="badge bg-info mt-1"><?php echo $booking->member_code; ?></span>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-user"></i> Non-Member
                                        </span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Nama Pemesan:</td>
                                <td><?php echo $booking->nama_pemesan; ?></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Telepon:</td>
                                <td>
                                    <a href="tel:<?php echo $booking->telepon_pemesan; ?>" class="text-decoration-none">
                                        <?php echo $booking->telepon_pemesan; ?>
                                    </a>
                                </td>
                            </tr>
                            <?php if ($booking->member_email): ?>
                            <tr>
                                <td class="fw-bold">Email:</td>
                                <td>
                                    <a href="mailto:<?php echo $booking->member_email; ?>" class="text-decoration-none">
                                        <?php echo $booking->member_email; ?>
                                    </a>
                                </td>
                            </tr>
                            <?php endif; ?>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transaction Info (if exists) -->
        <?php if ($booking->transaksi_id): ?>
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-receipt"></i> Informasi Transaksi
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold" width="40%">Tanggal Bayar:</td>
                                <td><?php echo date('d F Y H:i', strtotime($booking->tanggal_transaksi)); ?></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Metode Pembayaran:</td>
                                <td>
                                    <span class="badge bg-<?php echo ($booking->metode_pembayaran == 'tunai') ? 'success' : 'info'; ?>">
                                        <i class="fas fa-<?php echo ($booking->metode_pembayaran == 'tunai') ? 'money-bill' : 'credit-card'; ?>"></i>
                                        <?php echo ucfirst($booking->metode_pembayaran); ?>
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold" width="40%">Jumlah Bayar:</td>
                                <td>Rp <?php echo number_format($booking->jumlah_bayar, 0, ',', '.'); ?></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Kembalian:</td>
                                <td>Rp <?php echo number_format($booking->kembalian, 0, ',', '.'); ?></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Kasir:</td>
                                <td><?php echo $booking->kasir_name; ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- History & Notes -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history"></i> History & Catatan
                </h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">Booking Dibuat</h6>
                            <p class="timeline-text">
                                Booking dibuat oleh: <strong><?php echo $booking->created_by_name; ?></strong>
                            </p>
                            <small class="text-muted">
                                <i class="fas fa-clock"></i> 
                                <?php echo date('d F Y H:i', strtotime($booking->created_at)); ?>
                            </small>
                        </div>
                    </div>
                    
                    <?php if ($booking->updated_at != $booking->created_at): ?>
                    <div class="timeline-item">
                        <div class="timeline-marker bg-warning"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">Booking Diupdate</h6>
                            <p class="timeline-text">Booking telah diperbarui</p>
                            <small class="text-muted">
                                <i class="fas fa-clock"></i> 
                                <?php echo date('d F Y H:i', strtotime($booking->updated_at)); ?>
                            </small>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($booking->transaksi_id): ?>
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">Pembayaran Selesai</h6>
                            <p class="timeline-text">
                                Pembayaran diproses oleh: <strong><?php echo $booking->kasir_name; ?></strong>
                            </p>
                            <small class="text-muted">
                                <i class="fas fa-clock"></i> 
                                <?php echo date('d F Y H:i', strtotime($booking->tanggal_transaksi)); ?>
                            </small>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Price Breakdown -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calculator"></i> Rincian Harga
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tbody>
                        <tr>
                            <td>Harga per jam:</td>
                            <td class="text-end">Rp <?php echo number_format($booking->harga_per_jam, 0, ',', '.'); ?></td>
                        </tr>
                        <tr>
                            <td>Durasi:</td>
                            <td class="text-end"><?php echo $booking->durasi_jam; ?> jam</td>
                        </tr>
                        <tr>
                            <td>Subtotal:</td>
                            <td class="text-end">
                                Rp <?php echo number_format(($booking->price_before_discount ?: $booking->harga_total), 0, ',', '.'); ?>
                            </td>
                        </tr>
                        
                        <?php if ($booking->discount_percent > 0): ?>
                        <tr>
                            <td>Diskon (<?php echo $booking->discount_percent; ?>%):</td>
                            <td class="text-end text-success">
                                - Rp <?php echo number_format(($booking->price_before_discount - $booking->harga_total), 0, ',', '.'); ?>
                            </td>
                        </tr>
                        <?php endif; ?>
                        
                        <tr class="table-active">
                            <td><strong>Total:</strong></td>
                            <td class="text-end"><strong class="text-primary fs-5">Rp <?php echo number_format($booking->harga_total, 0, ',', '.'); ?></strong></td>
                        </tr>
                    </tbody>
                </table>
                
                <?php if ($booking->customer_type == 'member' && $booking->discount_percent > 0): ?>
                <div class="alert alert-success mt-3">
                    <i class="fas fa-star"></i>
                    <small>Hemat Rp <?php echo number_format(($booking->price_before_discount - $booking->harga_total), 0, ',', '.'); ?> dengan member discount!</small>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Quick Actions -->
        <?php if (in_array($this->session->userdata('role'), ['admin', 'kasir'])): ?>
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt"></i> Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <?php if ($booking->status_booking == 'pending'): ?>
                        <button type="button" class="btn btn-info btn-sm" 
                                onclick="updateStatus('dikonfirmasi')">
                            <i class="fas fa-check"></i> Konfirmasi Booking
                        </button>
                        
                        <button type="button" class="btn btn-danger btn-sm" 
                                onclick="updateStatus('dibatalkan')">
                            <i class="fas fa-times"></i> Batalkan Booking
                        </button>
                    <?php endif; ?>
                    
                    <?php if ($booking->status_booking == 'dikonfirmasi'): ?>
                        <button type="button" class="btn btn-success btn-sm" 
                                onclick="updateStatus('selesai')">
                            <i class="fas fa-check-double"></i> Tandai Selesai
                        </button>
                        
                        <a href="<?php echo site_url('transaksi?booking_id=' . $booking->id); ?>" 
                           class="btn btn-primary btn-sm">
                            <i class="fas fa-cash-register"></i> Proses Pembayaran
                        </a>
                    <?php endif; ?>
                    
                    <?php if ($booking->status_booking == 'pending'): ?>
                        <a href="<?php echo site_url('booking/edit/' . $booking->id); ?>" 
                           class="btn btn-warning btn-sm">
                            <i class="fas fa-edit"></i> Edit Booking
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Contact Info -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-phone"></i> Kontak Pemesan
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="tel:<?php echo $booking->telepon_pemesan; ?>" 
                       class="btn btn-outline-success btn-sm">
                        <i class="fas fa-phone"></i> Telepon
                    </a>
                    
                    <a href="https://wa.me/62<?php echo ltrim($booking->telepon_pemesan, '0'); ?>?text=Halo,%20mengenai%20booking%20<?php echo $booking->kode_booking; ?>" 
                       target="_blank" class="btn btn-outline-success btn-sm">
                        <i class="fab fa-whatsapp"></i> WhatsApp
                    </a>
                    
                    <?php if ($booking->member_email): ?>
                    <a href="mailto:<?php echo $booking->member_email; ?>?subject=Booking%20<?php echo $booking->kode_booking; ?>" 
                       class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-envelope"></i> Email
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Update Modal -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Konfirmasi Perubahan Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="statusMessage">Apakah Anda yakin ingin mengubah status booking ini?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-primary" id="confirmStatusUpdate">Ya, Ubah Status</button>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.timeline-content {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 0.375rem;
    border-left: 3px solid #007bff;
}

.timeline-title {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    font-weight: 600;
}

.timeline-text {
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
}

@media print {
    .btn, .btn-group, .page-header .col-auto {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
}
</style>

<script>
let currentStatus = null;

function updateStatus(status) {
    currentStatus = status;
    
    const statusText = {
        'pending': 'Pending',
        'dikonfirmasi': 'Dikonfirmasi',
        'selesai': 'Selesai',
        'dibatalkan': 'Dibatalkan'
    };
    
    document.getElementById('statusMessage').innerHTML = 
        `Apakah Anda yakin ingin mengubah status booking ini menjadi <strong>${statusText[status]}</strong>?`;
    
    new bootstrap.Modal(document.getElementById('statusModal')).show();
}

document.getElementById('confirmStatusUpdate').addEventListener('click', function() {
    if (currentStatus) {
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `<?php echo site_url('booking/update_status/' . $booking->id); ?>`;
        
        const statusInput = document.createElement('input');
        statusInput.type = 'hidden';
        statusInput.name = 'status';
        statusInput.value = currentStatus;
        
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '<?php echo $this->security->get_csrf_token_name(); ?>';
        csrfInput.value = '<?php echo $this->security->get_csrf_hash(); ?>';
        
        form.appendChild(statusInput);
        form.appendChild(csrfInput);
        document.body.appendChild(form);
        form.submit();
    }
});
</script>