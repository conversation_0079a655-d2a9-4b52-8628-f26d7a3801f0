<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| URI ROUTING
| -------------------------------------------------------------------------
| This file lets you re-map URI requests to specific controller functions.
|
| Typically there is a one-to-one relationship between a URL string
| and its corresponding controller class/method. The segments in a
| URL normally follow this pattern:
|
|	example.com/class/method/id/
|
| In some instances, however, you may want to remap this relationship
| so that a different class/function is called than the one
| corresponding to the URL.
|
| Please see the user guide for complete details:
|
|	https://codeigniter.com/userguide3/general/routing.html
|
| -------------------------------------------------------------------------
| RESERVED ROUTES
| -------------------------------------------------------------------------
|
| There are three reserved routes:
|
|	$route['default_controller'] = 'welcome';
|
| This route indicates which controller class should be loaded if the
| URI contains no data. In the above example, the "welcome" class
| would be loaded.
|
|	$route['404_override'] = 'errors/page_missing';
|
| This route will tell the Router which controller/method to use if those
| provided in the URL cannot be matched to a valid route.
|
|	$route['translate_uri_dashes'] = FALSE;
|
| This is not exactly a route, but allows you to automatically route
| controller and method names that contain dashes. '-' isn't a valid
| class or method name character, so it requires translation.
| When you set this option to TRUE, it will replace ALL dashes in the
| controller and method URI segments.
|
| Examples:	my-controller/index	-> my_controller/index
|		my-controller/my-method	-> my_controller/my_method
*/
// Ganti default controller ke auth
$route['default_controller'] = 'auth';
$route['404_override'] = '';
$route['translate_uri_dashes'] = FALSE;

// Auth routes
$route['login'] = 'auth/login';
$route['logout'] = 'auth/logout';
$route['process-login'] = 'auth/process_login';

// Dashboard
$route['dashboard'] = 'dashboard/index';

// Admin routes
$route['users'] = 'users/index';
$route['users/create'] = 'users/create';
$route['users/edit/(:num)'] = 'users/edit/$1';
$route['users/delete/(:num)'] = 'users/delete/$1';

$route['lapangan'] = 'lapangan/index';
$route['lapangan/create'] = 'lapangan/create';
$route['lapangan/edit/(:num)'] = 'lapangan/edit/$1';
$route['lapangan/delete/(:num)'] = 'lapangan/delete/$1';

$route['booking'] = 'booking/index';
$route['booking/create'] = 'booking/create';
$route['booking/edit/(:num)'] = 'booking/edit/$1';
$route['booking/detail/(:num)'] = 'booking/detail/$1';
$route['booking/delete/(:num)'] = 'booking/delete/$1';

// Kasir routes
$route['transaksi'] = 'transaksi/index';
$route['transaksi/pembayaran/(:num)'] = 'transaksi/pembayaran/$1';

// Pimpinan routes
$route['laporan'] = 'laporan/index';
$route['laporan/booking'] = 'laporan/booking';
$route['laporan/pendapatan'] = 'laporan/pendapatan';

// API routes
$route['api/check-availability'] = 'api/booking/check_availability';
$route['api/get-lapangan-harga/(:num)'] = 'api/booking/get_lapangan_harga/$1';

// Testing routes (bisa dihapus setelah selesai setup)
$route['test/database'] = 'test/database';
$route['test/create-tables'] = 'test/create_tables';
$route['setup'] = 'setup/index';