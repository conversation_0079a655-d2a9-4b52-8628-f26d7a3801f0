<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Dashboard extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        
        // Check if user is logged in - fix session check
        if (!$this->session->userdata('user_id')) {
            redirect('auth/login');
        }
        
        // Load models
        $this->load->model('User_model');
        $this->load->model('Booking_model');
    }

    /**
     * Dashboard index based on user role
     */
    public function index()
    {
        $user_role = $this->session->userdata('role');
        
        switch ($user_role) {
            case 'admin':
                $this->admin_dashboard();
                break;
            case 'kasir':
                $this->kasir_dashboard();
                break;
            case 'pimpinan':
                $this->pimpinan_dashboard();
                break;
            default:
                redirect('auth/logout');
                break;
        }
    }

    /**
     * Admin Dashboard
     */
    private function admin_dashboard()
    {
        $data['title'] = 'Dashboard Admin - Padel Booking System';
        $data['page'] = 'dashboard_admin';
        
        // Get basic statistics
        $data['stats'] = $this->get_basic_stats();
        
        // Initialize empty data for now
        $data['recent_bookings'] = array();
        $data['today_schedule'] = array();
        $data['chart_data'] = array();
        
        // Load views
        $this->load->view('template/header', $data);
        $this->load->view('dashboard/admin', $data);
        $this->load->view('template/footer', $data);
    }

    /**
     * Kasir Dashboard - FIXED
     */
    private function kasir_dashboard()
    {
        $data['title'] = 'Dashboard Kasir - Padel Booking System';
        $data['page'] = 'dashboard_kasir';
        
        // Get kasir-specific stats - FIX ALL ERRORS
        $data['stats'] = $this->get_kasir_stats();
        
        // Get data for kasir dashboard - FIX UNDEFINED VARIABLES
        $data['today_bookings'] = $this->get_today_bookings();
        $data['pending_bookings'] = $this->get_pending_bookings();
        $data['payment_ready'] = $this->get_payment_ready_bookings();
        $data['recent_transactions'] = $this->get_recent_transactions();
        
        // Load views
        $this->load->view('template/header', $data);
        $this->load->view('dashboard/kasir', $data);
        $this->load->view('template/footer', $data);
    }

    /**
     * Pimpinan Dashboard
     */
    private function pimpinan_dashboard()
    {
        $data['title'] = 'Dashboard Pimpinan - Padel Booking System';
        $data['page'] = 'dashboard_pimpinan';
        
        // Basic stats
        $data['stats'] = $this->get_basic_stats();
        $data['monthly_revenue'] = array();
        $data['top_courts'] = array();
        $data['booking_trends'] = array();
        
        // Load views
        $this->load->view('template/header', $data);
        $this->load->view('dashboard/pimpinan', $data);
        $this->load->view('template/footer', $data);
    }

    /**
     * Get kasir-specific statistics - FIX UNDEFINED ARRAY KEYS
     */
    private function get_kasir_stats()
    {
        $stats = array();
        
        try {
            // Pending bookings
            $this->db->where('status_booking', 'pending');
            $stats['pending_bookings'] = $this->db->count_all_results('booking');
            
            // Confirmed bookings (ready for payment)
            $this->db->where('status_booking', 'dikonfirmasi');
            $stats['confirmed_bookings'] = $this->db->count_all_results('booking');
            
            // Today's transactions count
            $this->db->where('DATE(tanggal_transaksi)', date('Y-m-d'));
            $stats['today_transactions'] = $this->db->count_all_results('transaksi');
            
            // Today's revenue
            $this->db->select('SUM(jumlah_bayar) as total');
            $this->db->where('DATE(tanggal_transaksi)', date('Y-m-d'));
            $result = $this->db->get('transaksi')->row();
            $stats['today_revenue'] = $result && $result->total ? $result->total : 0;
            
            // My transactions this month
            $user_id = $this->session->userdata('user_id');
            $this->db->where('kasir_id', $user_id);
            $this->db->where('MONTH(tanggal_transaksi)', date('m'));
            $this->db->where('YEAR(tanggal_transaksi)', date('Y'));
            $stats['my_transactions'] = $this->db->count_all_results('transaksi');
            
        } catch (Exception $e) {
            // Return default values if error
            $stats = array(
                'pending_bookings' => 0,
                'confirmed_bookings' => 0,
                'today_transactions' => 0,
                'today_revenue' => 0,
                'my_transactions' => 0
            );
        }
        
        return $stats;
    }

    /**
     * Get today's bookings - FIX UNDEFINED VARIABLE
     */
    private function get_today_bookings()
    {
        try {
            $this->db->select('b.*, l.nama_lapangan');
            $this->db->from('booking b');
            $this->db->join('lapangan l', 'b.lapangan_id = l.id', 'left');
            $this->db->where('b.tanggal_booking', date('Y-m-d'));
            $this->db->order_by('b.jam_mulai', 'ASC');
            return $this->db->get()->result();
        } catch (Exception $e) {
            return array();
        }
    }

    /**
     * Get pending bookings - FIX UNDEFINED VARIABLE
     */
    private function get_pending_bookings()
    {
        try {
            $this->db->select('b.*, l.nama_lapangan');
            $this->db->from('booking b');
            $this->db->join('lapangan l', 'b.lapangan_id = l.id', 'left');
            $this->db->where('b.status_booking', 'pending');
            $this->db->order_by('b.created_at', 'DESC');
            $this->db->limit(10);
            return $this->db->get()->result();
        } catch (Exception $e) {
            return array();
        }
    }

    /**
     * Get bookings ready for payment - FIX UNDEFINED VARIABLE
     */
    private function get_payment_ready_bookings()
    {
        try {
            $this->load->model('Booking_model');
            $all_ready = $this->Booking_model->get_confirmed_unpaid_bookings();
            return array_slice($all_ready, 0, 10); // Limit to 10 items
        } catch (Exception $e) {
            return array();
        }
    }

    /**
     * Get recent transactions - FIX UNDEFINED VARIABLE
     */
    private function get_recent_transactions()
    {
        try {
            $user_id = $this->session->userdata('user_id');
            $this->db->select('t.*, b.kode_booking');
            $this->db->from('transaksi t');
            $this->db->join('booking b', 't.booking_id = b.id', 'left');
            $this->db->where('t.kasir_id', $user_id);
            $this->db->order_by('t.tanggal_transaksi', 'DESC');
            $this->db->limit(5);
            return $this->db->get()->result();
        } catch (Exception $e) {
            return array();
        }
    }

    /**
     * Get basic statistics without complex queries
     */
    private function get_basic_stats()
    {
        $stats = array();
        
        try {
            // Check if tables exist before querying
            if ($this->db->table_exists('booking')) {
                // Total bookings
                $stats['total_bookings'] = $this->db->count_all('booking');
                
                // Today's bookings
                $this->db->where('tanggal_booking', date('Y-m-d'));
                $stats['today_bookings'] = $this->db->count_all_results('booking');
                
                // Pending bookings
                $this->db->where('status_booking', 'pending');
                $stats['pending_bookings'] = $this->db->count_all_results('booking');
                
                // Monthly revenue (simple query)
                $this->db->select('SUM(harga_total) as total');
                $this->db->where('status_booking', 'selesai');
                $this->db->where('MONTH(tanggal_booking)', date('m'));
                $this->db->where('YEAR(tanggal_booking)', date('Y'));
                $result = $this->db->get('booking')->row();
                $stats['monthly_revenue'] = $result && $result->total ? $result->total : 0;
                
                // Completed bookings this month
                $this->db->where('status_booking', 'selesai');
                $this->db->where('MONTH(tanggal_booking)', date('m'));
                $this->db->where('YEAR(tanggal_booking)', date('Y'));
                $stats['completed_bookings'] = $this->db->count_all_results('booking');
                
                // Average booking value
                $this->db->select('AVG(harga_total) as avg_value');
                $this->db->where('status_booking', 'selesai');
                $result = $this->db->get('booking')->row();
                $stats['avg_booking_value'] = $result && $result->avg_value ? $result->avg_value : 0;
            } else {
                // Default values if booking table doesn't exist
                $stats['total_bookings'] = 0;
                $stats['today_bookings'] = 0;
                $stats['pending_bookings'] = 0;
                $stats['monthly_revenue'] = 0;
                $stats['completed_bookings'] = 0;
                $stats['avg_booking_value'] = 0;
            }
            
            // Total users
            $stats['total_users'] = $this->db->count_all('users');
            
            // Active courts
            if ($this->db->table_exists('lapangan')) {
                $this->db->where('status', 'aktif');
                $stats['active_courts'] = $this->db->count_all_results('lapangan');
            } else {
                $stats['active_courts'] = 0;
            }
            
        } catch (Exception $e) {
            // If any error occurs, return default values
            $stats = array(
                'total_bookings' => 0,
                'today_bookings' => 0,
                'pending_bookings' => 0,
                'monthly_revenue' => 0,
                'total_users' => $this->db->count_all('users'),
                'active_courts' => 0,
                'completed_bookings' => 0,
                'avg_booking_value' => 0
            );
        }
        
        return $stats;
    }

    /**
     * AJAX: Get kasir dashboard statistics - FIXED METHOD NAME
     */
    public function get_kasir_stats_ajax()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $stats = $this->get_kasir_stats();
        
        echo json_encode(array(
            'success' => true,
            'stats' => $stats
        ));
    }

    /**
     * AJAX: Get dashboard statistics
     */
    public function get_stats()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $stats = $this->get_basic_stats();
        
        echo json_encode(array(
            'success' => true,
            'data' => $stats
        ));
    }

    /**
     * AJAX: Get chart data (placeholder)
     */
    public function get_chart_data()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $type = $this->input->post('type');
        
        // Return dummy data for now
        $data = array();
        
        switch ($type) {
            case 'booking_chart':
                for ($i = 6; $i >= 0; $i--) {
                    $date = date('d M', strtotime("-$i days"));
                    $data[] = array(
                        'date' => $date,
                        'bookings' => rand(0, 10),
                        'revenue' => rand(100000, 500000)
                    );
                }
                break;
            case 'monthly_revenue':
                for ($i = 11; $i >= 0; $i--) {
                    $month = date('M Y', strtotime("-$i months"));
                    $data[] = array(
                        'month' => $month,
                        'revenue' => rand(1000000, 5000000)
                    );
                }
                break;
            default:
                $data = array();
                break;
        }
        
        echo json_encode(array(
            'success' => true,
            'data' => $data
        ));
    }
}