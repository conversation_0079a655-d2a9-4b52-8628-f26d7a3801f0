<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Profile extends CI_Controller {

    public function __construct() {
        parent::__construct();
        
        // Check if user is logged in
        if (!$this->session->userdata('user_id')) {
            redirect('auth/login');
        }
        
        // Load required models and libraries
        $this->load->model('User_model');
        $this->load->model('Booking_model');
        $this->load->library('form_validation');
        $this->load->library('upload');
    }

    public function index() {
        $data['title'] = 'Profil Saya';
        
        // Get current user data
        $user_id = $this->session->userdata('user_id');
        $data['user'] = $this->User_model->get_user_by_id($user_id);
        
        if (!$data['user']) {
            show_error('User tidak ditemukan', 404, 'User Not Found');
        }
        
        $this->load->view('template/header', $data);
        $this->load->view('profile/index', $data);
        $this->load->view('template/footer');
    }

    public function edit() {
        $data['title'] = 'Edit Profil';
        
        $user_id = $this->session->userdata('user_id');
        $user = $this->User_model->get_user_by_id($user_id);
        
        if (!$user) {
            show_error('User tidak ditemukan', 404, 'User Not Found');
        }
        
        if ($this->input->post()) {
            $this->_process_edit_profile($user_id);
        } else {
            $data['user'] = $user;
            
            $this->load->view('template/header', $data);
            $this->load->view('profile/edit', $data);
            $this->load->view('template/footer');
        }
    }

    private function _process_edit_profile($user_id) {
        // Set validation rules
        $this->form_validation->set_rules('full_name', 'Nama Lengkap', 'required|max_length[100]');
        $this->form_validation->set_rules('email', 'Email', 'required|valid_email|max_length[100]');
        $this->form_validation->set_rules('phone', 'Telepon', 'max_length[20]');
        
        // Check if email is unique (exclude current user)
        $current_user = $this->User_model->get_user_by_id($user_id);
        $email = $this->input->post('email');
        
        if ($email != $current_user->email) {
            $existing_email = $this->User_model->get_user_by_email($email);
            if ($existing_email) {
                $this->form_validation->set_rules('email', 'Email', 'required|valid_email|is_unique[users.email]');
            }
        }

        if ($this->form_validation->run() === FALSE) {
            $data['title'] = 'Edit Profil';
            $data['user'] = $current_user;
            
            $this->load->view('template/header', $data);
            $this->load->view('profile/edit', $data);
            $this->load->view('template/footer');
            return;
        }

        // Prepare update data
        $update_data = array(
            'full_name' => $this->input->post('full_name'),
            'email' => $this->input->post('email'),
            'phone' => $this->input->post('phone')
        );

        // Update user data
        if ($this->User_model->update_user($user_id, $update_data)) {
            // Update session data
            $this->session->set_userdata('full_name', $update_data['full_name']);
            
            $this->session->set_flashdata('success', 'Profil berhasil diperbarui');
            redirect('profile');
        } else {
            $this->session->set_flashdata('error', 'Gagal memperbarui profil. Silakan coba lagi.');
            redirect('profile/edit');
        }
    }

    public function change_password() {
        $data['title'] = 'Ubah Password';
        
        if ($this->input->post()) {
            $this->_process_change_password();
        } else {
            $this->load->view('template/header', $data);
            $this->load->view('profile/change_password', $data);
            $this->load->view('template/footer');
        }
    }

    private function _process_change_password() {
        // Set validation rules
        $this->form_validation->set_rules('current_password', 'Password Lama', 'required');
        $this->form_validation->set_rules('new_password', 'Password Baru', 'required|min_length[6]');
        $this->form_validation->set_rules('confirm_password', 'Konfirmasi Password', 'required|matches[new_password]');

        if ($this->form_validation->run() === FALSE) {
            $data['title'] = 'Ubah Password';
            
            $this->load->view('template/header', $data);
            $this->load->view('profile/change_password', $data);
            $this->load->view('template/footer');
            return;
        }

        $user_id = $this->session->userdata('user_id');
        $current_password = $this->input->post('current_password');
        $new_password = $this->input->post('new_password');

        // Get current user data
        $user = $this->User_model->get_user_by_id($user_id);

        // Verify current password
        if (!password_verify($current_password, $user->password)) {
            $this->session->set_flashdata('error', 'Password lama tidak sesuai');
            redirect('profile/change_password');
            return;
        }

        // Hash new password
        $hashed_password = password_hash($new_password, PASSWORD_BCRYPT);

        // Update password
        $update_data = array(
            'password' => $hashed_password
        );

        if ($this->User_model->update_user($user_id, $update_data)) {
            $this->session->set_flashdata('success', 'Password berhasil diubah');
            redirect('profile');
        } else {
            $this->session->set_flashdata('error', 'Gagal mengubah password. Silakan coba lagi.');
            redirect('profile/change_password');
        }
    }

    public function activity_log() {
        $data['title'] = 'Log Aktivitas';
        
        $user_id = $this->session->userdata('user_id');
        
        // Get user activity logs
        $data['logs'] = $this->User_model->get_user_logs($user_id, 50); // Get last 50 logs
        
        $this->load->view('template/header', $data);
        $this->load->view('profile/activity_log', $data);
        $this->load->view('template/footer');
    }

    public function upload_avatar() {
        if (!$this->input->post()) {
            show_404();
        }

        $user_id = $this->session->userdata('user_id');
        
        // Configure upload
        $config['upload_path'] = './uploads/avatars/';
        $config['allowed_types'] = 'gif|jpg|jpeg|png';
        $config['max_size'] = 2048; // 2MB
        $config['max_width'] = 1024;
        $config['max_height'] = 1024;
        $config['file_name'] = 'avatar_' . $user_id . '_' . time();

        // Create directory if not exists
        if (!is_dir($config['upload_path'])) {
            mkdir($config['upload_path'], 0755, true);
        }

        $this->upload->initialize($config);

        if ($this->upload->do_upload('avatar')) {
            $upload_data = $this->upload->data();
            
            // Get current user to delete old avatar
            $user = $this->User_model->get_user_by_id($user_id);
            
            // Delete old avatar file if exists
            if ($user->avatar && file_exists('./uploads/avatars/' . $user->avatar)) {
                unlink('./uploads/avatars/' . $user->avatar);
            }
            
            // Update avatar in database
            $update_data = array(
                'avatar' => $upload_data['file_name']
            );
            
            if ($this->User_model->update_user($user_id, $update_data)) {
                $this->session->set_flashdata('success', 'Avatar berhasil diperbarui');
            } else {
                $this->session->set_flashdata('error', 'Gagal menyimpan avatar ke database');
            }
        } else {
            $error = $this->upload->display_errors('', '');
            $this->session->set_flashdata('error', 'Gagal upload avatar: ' . $error);
        }

        redirect('profile');
    }

    public function delete_avatar() {
        $user_id = $this->session->userdata('user_id');
        $user = $this->User_model->get_user_by_id($user_id);
        
        if ($user->avatar) {
            // Delete file
            if (file_exists('./uploads/avatars/' . $user->avatar)) {
                unlink('./uploads/avatars/' . $user->avatar);
            }
            
            // Update database
            $update_data = array(
                'avatar' => null
            );
            
            if ($this->User_model->update_user($user_id, $update_data)) {
                $this->session->set_flashdata('success', 'Avatar berhasil dihapus');
            } else {
                $this->session->set_flashdata('error', 'Gagal menghapus avatar');
            }
        }
        
        redirect('profile');
    }

    public function export_data() {
        $user_id = $this->session->userdata('user_id');
        
        // Get user data
        $user = $this->User_model->get_user_by_id($user_id);
        $logs = $this->User_model->get_user_logs($user_id);
        
        // Prepare export data
        $export_data = array(
            'profile' => array(
                'username' => $user->username,
                'full_name' => $user->full_name,
                'email' => $user->email,
                'phone' => $user->phone,
                'role' => $user->role,
                'status' => $user->status,
                'created_at' => $user->created_at
            ),
            'activity_logs' => $logs,
            'exported_at' => date('Y-m-d H:i:s')
        );
        
        // Set headers for download
        header('Content-Type: application/json');
        header('Content-Disposition: attachment; filename="profile_data_' . $user->username . '_' . date('Y-m-d') . '.json"');
        
        echo json_encode($export_data, JSON_PRETTY_PRINT);
    }

    public function security_settings() {
        $data['title'] = 'Pengaturan Keamanan';
        
        $user_id = $this->session->userdata('user_id');
        $data['user'] = $this->User_model->get_user_by_id($user_id);
        $data['recent_logins'] = $this->User_model->get_user_logs($user_id, 10, 'login');
        
        $this->load->view('template/header', $data);
        $this->load->view('profile/security', $data);
        $this->load->view('template/footer');
    }

    public function clear_sessions() {
        $user_id = $this->session->userdata('user_id');
        
        // Log the action
        $this->User_model->log_user_activity($user_id, 'clear_sessions', $this->input->ip_address(), $this->input->user_agent());
        
        $this->session->set_flashdata('success', 'Semua sesi telah dibersihkan');
        redirect('profile/security_settings');
    }
}