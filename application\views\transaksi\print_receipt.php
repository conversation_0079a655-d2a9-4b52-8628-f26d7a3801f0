<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Struk Pembayaran - <?php echo $transaksi->kode_transaksi; ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
            background: #fff;
            padding: 20px;
            max-width: 400px;
            margin: 0 auto;
        }
        
        .receipt-header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
        }
        
        .receipt-header h1 {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .receipt-header p {
            font-size: 11px;
            margin-bottom: 2px;
        }
        
        .section {
            margin-bottom: 15px;
        }
        
        .section-title {
            font-weight: bold;
            margin-bottom: 5px;
            text-transform: uppercase;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
        }
        
        .info-label {
            font-weight: bold;
            width: 40%;
        }
        
        .info-value {
            width: 60%;
            text-align: right;
        }
        
        .booking-details {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
        }
        
        .payment-summary {
            border-top: 1px solid #000;
            padding-top: 10px;
        }
        
        .total-row {
            border-top: 2px solid #000;
            padding-top: 5px;
            margin-top: 5px;
            font-weight: bold;
            font-size: 14px;
        }
        
        .receipt-footer {
            text-align: center;
            margin-top: 20px;
            padding-top: 10px;
            border-top: 2px solid #000;
            font-size: 11px;
        }
        
        .dashed-line {
            border-top: 1px dashed #000;
            margin: 10px 0;
        }
        
        @media print {
            body {
                padding: 0;
                margin: 0;
            }
            
            .no-print {
                display: none !important;
            }
        }
        
        @page {
            size: 80mm auto;
            margin: 0;
        }
    </style>
</head>
<body>
    <!-- Print Button -->
    <div class="no-print" style="text-align: center; margin-bottom: 20px;">
        <button onclick="window.print()" style="padding: 10px 20px; font-size: 14px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
            Cetak Struk
        </button>
        <button onclick="window.close()" style="padding: 10px 20px; font-size: 14px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">
            Tutup
        </button>
    </div>

    <!-- Receipt Content -->
    <div class="receipt-header">
        <h1>PADEL COURT</h1>
        <p>Jl. Contoh No. 123, Kota</p>
        <p>Telp: (*************</p>
        <p>Email: <EMAIL></p>
    </div>

    <div class="section">
        <div class="info-row">
            <span class="info-label">No. Transaksi:</span>
            <span class="info-value"><?php echo $transaksi->kode_transaksi; ?></span>
        </div>
        <div class="info-row">
            <span class="info-label">Tanggal:</span>
            <span class="info-value"><?php echo date('d/m/Y H:i', strtotime($transaksi->tanggal_transaksi)); ?></span>
        </div>
        <div class="info-row">
            <span class="info-label">Kasir:</span>
            <span class="info-value"><?php echo $transaksi->kasir_name; ?></span>
        </div>
    </div>

    <div class="dashed-line"></div>

    <div class="section">
        <div class="section-title">Data Customer</div>
        <div class="info-row">
            <span class="info-label">Booking:</span>
            <span class="info-value"><?php echo $transaksi->kode_booking; ?></span>
        </div>
        <div class="info-row">
            <span class="info-label">Nama:</span>
            <span class="info-value"><?php echo $transaksi->customer_name; ?></span>
        </div>
        <div class="info-row">
            <span class="info-label">Telepon:</span>
            <span class="info-value"><?php echo $transaksi->customer_phone; ?></span>
        </div>
        <?php if ($transaksi->member_name): ?>
        <div class="info-row">
            <span class="info-label">Member:</span>
            <span class="info-value"><?php echo $transaksi->member_name; ?></span>
        </div>
        <?php endif; ?>
    </div>

    <div class="dashed-line"></div>

    <div class="section">
        <div class="section-title">Detail Booking</div>
        <div class="booking-details">
            <div class="info-row">
                <span class="info-label">Lapangan:</span>
                <span class="info-value"><?php echo $transaksi->nama_lapangan; ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Tanggal:</span>
                <span class="info-value"><?php echo date('d/m/Y', strtotime($transaksi->tanggal_booking)); ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Waktu:</span>
                <span class="info-value"><?php echo $transaksi->jam_mulai; ?> - <?php echo $transaksi->jam_selesai; ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Durasi:</span>
                <span class="info-value"><?php echo $transaksi->durasi; ?> jam</span>
            </div>
        </div>
    </div>

    <div class="section payment-summary">
        <div class="section-title">Rincian Pembayaran</div>
        <div class="info-row">
            <span class="info-label">Harga/Jam:</span>
            <span class="info-value">Rp <?php echo number_format($transaksi->harga_per_jam, 0, ',', '.'); ?></span>
        </div>
        <div class="info-row">
            <span class="info-label">Subtotal:</span>
            <span class="info-value">Rp <?php echo number_format($transaksi->subtotal, 0, ',', '.'); ?></span>
        </div>
        <?php if ($transaksi->diskon > 0): ?>
        <div class="info-row">
            <span class="info-label">Diskon:</span>
            <span class="info-value">-Rp <?php echo number_format($transaksi->diskon, 0, ',', '.'); ?></span>
        </div>
        <?php endif; ?>
        
        <div class="total-row">
            <div class="info-row">
                <span class="info-label">TOTAL:</span>
                <span class="info-value">Rp <?php echo number_format($transaksi->total_harga, 0, ',', '.'); ?></span>
            </div>
        </div>
        
        <div class="dashed-line"></div>
        
        <div class="info-row">
            <span class="info-label">Bayar (<?php echo ucfirst($transaksi->metode_pembayaran); ?>):</span>
            <span class="info-value">Rp <?php echo number_format($transaksi->jumlah_bayar, 0, ',', '.'); ?></span>
        </div>
        <div class="info-row">
            <span class="info-label">Kembalian:</span>
            <span class="info-value">Rp <?php echo number_format($transaksi->kembalian, 0, ',', '.'); ?></span>
        </div>
    </div>

    <div class="receipt-footer">
        <p><strong>TERIMA KASIH</strong></p>
        <p>Atas kunjungan Anda!</p>
        <p style="margin-top: 10px;">Simpan struk ini sebagai</p>
        <p>bukti pembayaran yang sah</p>
        <p style="margin-top: 10px; font-size: 10px;">
            Dicetak: <?php echo date('d/m/Y H:i:s'); ?>
        </p>
    </div>

    <script>
        // Auto print when page loads
        window.onload = function() {
            setTimeout(function() {
                window.print();
            }, 500);
        };
        
        // Close window after printing
        window.onafterprint = function() {
            setTimeout(function() {
                window.close();
            }, 1000);
        };
    </script>
</body>
</html>
