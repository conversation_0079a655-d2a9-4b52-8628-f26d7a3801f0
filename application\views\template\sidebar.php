<aside class="sidebar" id="sidebar">
    <nav class="sidebar-nav">
        <ul>
            <!-- Dashboard - untuk semua role -->
            <li>
                <a href="<?php echo site_url('dashboard'); ?>" class="<?php echo ($this->uri->segment(1) == 'dashboard') ? 'active' : ''; ?>">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
            </li>
            
            <?php if ($this->session->userdata('role') == 'admin'): ?>
                <!-- Menu Admin -->
                <li class="nav-divider">
                    <span>MANAJEMEN</span>
                </li>
                
                <li>
                    <a href="<?php echo site_url('users'); ?>" class="<?php echo ($this->uri->segment(1) == 'users') ? 'active' : ''; ?>">
                        <i class="fas fa-users"></i>
                        <span>Pengguna</span>
                    </a>
                </li>
                
                <li>
                    <a href="<?php echo site_url('lapangan'); ?>" class="<?php echo ($this->uri->segment(1) == 'lapangan') ? 'active' : ''; ?>">
                        <i class="fas fa-map-marked-alt"></i>
                        <span>Lapangan</span>
                    </a>
                </li>
                
                <li>
                    <a href="<?php echo site_url('member'); ?>" class="<?php echo ($this->uri->segment(1) == 'member') ? 'active' : ''; ?>">
                        <i class="fas fa-star"></i>
                        <span>Member</span>
                    </a>
                </li>
                
                <li class="nav-divider">
                    <span>BOOKING & PEMESANAN</span>
                </li>
                
                <li>
                    <a href="<?php echo site_url('booking'); ?>" class="<?php echo ($this->uri->segment(1) == 'booking' && ($this->uri->segment(2) == '' || !$this->uri->segment(2))) ? 'active' : ''; ?>">
                        <i class="fas fa-calendar-alt"></i>
                        <span>Daftar Booking</span>
                    </a>
                </li>
                
                <li>
                    <a href="<?php echo site_url('booking/create'); ?>" class="<?php echo ($this->uri->segment(1) == 'booking' && $this->uri->segment(2) == 'create') ? 'active' : ''; ?>">
                        <i class="fas fa-plus-circle"></i>
                        <span>Buat Booking</span>
                    </a>
                </li>
                
                <li class="nav-divider">
                    <span>TRANSAKSI</span>
                </li>
                
                <li>
                    <a href="<?php echo site_url('transaksi'); ?>" class="<?php echo ($this->uri->segment(1) == 'transaksi' && ($this->uri->segment(2) == '' || !$this->uri->segment(2))) ? 'active' : ''; ?>">
                        <i class="fas fa-cash-register"></i>
                        <span>Pembayaran</span>
                    </a>
                </li>
                
                <li>
                    <a href="<?php echo site_url('transaksi/riwayat'); ?>" class="<?php echo ($this->uri->segment(1) == 'transaksi' && $this->uri->segment(2) == 'riwayat') ? 'active' : ''; ?>">
                        <i class="fas fa-history"></i>
                        <span>Riwayat Transaksi</span>
                    </a>
                </li>
                
                <li class="nav-divider">
                    <span>LAPORAN</span>
                </li>
                
                <li>
                    <a href="<?php echo site_url('laporan'); ?>" class="<?php echo ($this->uri->segment(1) == 'laporan' && ($this->uri->segment(2) == '' || !$this->uri->segment(2))) ? 'active' : ''; ?>">
                        <i class="fas fa-chart-bar"></i>
                        <span>Ringkasan</span>
                    </a>
                </li>
                
                <li>
                    <a href="<?php echo site_url('laporan/booking'); ?>" class="<?php echo ($this->uri->segment(1) == 'laporan' && $this->uri->segment(2) == 'booking') ? 'active' : ''; ?>">
                        <i class="fas fa-calendar-check"></i>
                        <span>Laporan Booking</span>
                    </a>
                </li>
                
                <li>
                    <a href="<?php echo site_url('laporan/pendapatan'); ?>" class="<?php echo ($this->uri->segment(1) == 'laporan' && $this->uri->segment(2) == 'pendapatan') ? 'active' : ''; ?>">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>Laporan Pendapatan</span>
                    </a>
                </li>
                
                <li>
                    <a href="<?php echo site_url('laporan/member'); ?>" class="<?php echo ($this->uri->segment(1) == 'laporan' && $this->uri->segment(2) == 'member') ? 'active' : ''; ?>">
                        <i class="fas fa-users-cog"></i>
                        <span>Laporan Member</span>
                    </a>
                </li>
                
            <?php elseif ($this->session->userdata('role') == 'kasir'): ?>
                <!-- Menu Kasir -->
                <li class="nav-divider">
                    <span>BOOKING & PEMESANAN</span>
                </li>
                
                <li>
                    <a href="<?php echo site_url('booking'); ?>" class="<?php echo ($this->uri->segment(1) == 'booking' && ($this->uri->segment(2) == '' || !$this->uri->segment(2))) ? 'active' : ''; ?>">
                        <i class="fas fa-calendar-alt"></i>
                        <span>Daftar Booking</span>
                    </a>
                </li>
                
                <li>
                    <a href="<?php echo site_url('booking/create'); ?>" class="<?php echo ($this->uri->segment(1) == 'booking' && $this->uri->segment(2) == 'create') ? 'active' : ''; ?>">
                        <i class="fas fa-plus-circle"></i>
                        <span>Buat Booking</span>
                    </a>
                </li>
                
                <li>
                    <a href="<?php echo site_url('booking'); ?>?status=pending" class="<?php echo ($this->uri->segment(1) == 'booking' && $this->input->get('status') == 'pending') ? 'active' : ''; ?>">
                        <i class="fas fa-clock"></i>
                        <span>Booking Pending</span>
                    </a>
                </li>
                
                <li>
                    <a href="<?php echo site_url('booking'); ?>?status=dikonfirmasi" class="<?php echo ($this->uri->segment(1) == 'booking' && $this->input->get('status') == 'dikonfirmasi') ? 'active' : ''; ?>">
                        <i class="fas fa-check-circle"></i>
                        <span>Booking Dikonfirmasi</span>
                    </a>
                </li>
                
                <li class="nav-divider">
                    <span>TRANSAKSI & PEMBAYARAN</span>
                </li>
                
                <li>
                    <a href="<?php echo site_url('transaksi'); ?>" class="<?php echo ($this->uri->segment(1) == 'transaksi' && ($this->uri->segment(2) == '' || !$this->uri->segment(2))) ? 'active' : ''; ?>">
                        <i class="fas fa-cash-register"></i>
                        <span>Pembayaran</span>
                    </a>
                </li>
                
                <li>
                    <a href="<?php echo site_url('transaksi/riwayat'); ?>" class="<?php echo ($this->uri->segment(1) == 'transaksi' && $this->uri->segment(2) == 'riwayat') ? 'active' : ''; ?>">
                        <i class="fas fa-history"></i>
                        <span>Riwayat Transaksi</span>
                    </a>
                </li>
                
                <li>
                    <a href="<?php echo site_url('transaksi/hari-ini'); ?>" class="<?php echo ($this->uri->segment(1) == 'transaksi' && $this->uri->segment(2) == 'hari-ini') ? 'active' : ''; ?>">
                        <i class="fas fa-calendar-day"></i>
                        <span>Transaksi Hari Ini</span>
                    </a>
                </li>
                
                <li class="nav-divider">
                    <span>MEMBER</span>
                </li>
                
                <li>
                    <a href="<?php echo site_url('member/search'); ?>" class="<?php echo ($this->uri->segment(1) == 'member' && $this->uri->segment(2) == 'search') ? 'active' : ''; ?>">
                        <i class="fas fa-search"></i>
                        <span>Cari Member</span>
                    </a>
                </li>
                
                <li>
                    <a href="<?php echo site_url('member'); ?>" class="<?php echo ($this->uri->segment(1) == 'member' && ($this->uri->segment(2) == '' || !$this->uri->segment(2))) ? 'active' : ''; ?>">
                        <i class="fas fa-star"></i>
                        <span>Daftar Member</span>
                    </a>
                </li>
                
                <li class="nav-divider">
                    <span>LAPANGAN</span>
                </li>
                
                <li>
                    <a href="<?php echo site_url('lapangan'); ?>" class="<?php echo ($this->uri->segment(1) == 'lapangan') ? 'active' : ''; ?>">
                        <i class="fas fa-map-marked-alt"></i>
                        <span>Info Lapangan</span>
                    </a>
                </li>
                
            <?php elseif ($this->session->userdata('role') == 'pimpinan'): ?>
                <!-- Menu Pimpinan -->
                <li class="nav-divider">
                    <span>RINGKASAN</span>
                </li>
                
                <li>
                    <a href="<?php echo site_url('laporan'); ?>" class="<?php echo ($this->uri->segment(1) == 'laporan' && ($this->uri->segment(2) == '' || !$this->uri->segment(2))) ? 'active' : ''; ?>">
                        <i class="fas fa-chart-bar"></i>
                        <span>Dashboard Laporan</span>
                    </a>
                </li>
                
                <li class="nav-divider">
                    <span>LAPORAN BOOKING</span>
                </li>
                
                <li>
                    <a href="<?php echo site_url('laporan/booking'); ?>" class="<?php echo ($this->uri->segment(1) == 'laporan' && $this->uri->segment(2) == 'booking') ? 'active' : ''; ?>">
                        <i class="fas fa-calendar-check"></i>
                        <span>Laporan Booking</span>
                    </a>
                </li>
                
                <li>
                    <a href="<?php echo site_url('laporan/booking/harian'); ?>" class="<?php echo ($this->uri->segment(1) == 'laporan' && $this->uri->segment(2) == 'booking' && $this->uri->segment(3) == 'harian') ? 'active' : ''; ?>">
                        <i class="fas fa-calendar-day"></i>
                        <span>Laporan Harian</span>
                    </a>
                </li>
                
                <li>
                    <a href="<?php echo site_url('laporan/booking/bulanan'); ?>" class="<?php echo ($this->uri->segment(1) == 'laporan' && $this->uri->segment(2) == 'booking' && $this->uri->segment(3) == 'bulanan') ? 'active' : ''; ?>">
                        <i class="fas fa-calendar-alt"></i>
                        <span>Laporan Bulanan</span>
                    </a>
                </li>
                
                <li class="nav-divider">
                    <span>LAPORAN KEUANGAN</span>
                </li>
                
                <li>
                    <a href="<?php echo site_url('laporan/pendapatan'); ?>" class="<?php echo ($this->uri->segment(1) == 'laporan' && $this->uri->segment(2) == 'pendapatan') ? 'active' : ''; ?>">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>Laporan Pendapatan</span>
                    </a>
                </li>
                
                <li>
                    <a href="<?php echo site_url('laporan/pendapatan/harian'); ?>" class="<?php echo ($this->uri->segment(1) == 'laporan' && $this->uri->segment(2) == 'pendapatan' && $this->uri->segment(3) == 'harian') ? 'active' : ''; ?>">
                        <i class="fas fa-chart-line"></i>
                        <span>Pendapatan Harian</span>
                    </a>
                </li>
                
                <li>
                    <a href="<?php echo site_url('laporan/pendapatan/bulanan'); ?>" class="<?php echo ($this->uri->segment(1) == 'laporan' && $this->uri->segment(2) == 'pendapatan' && $this->uri->segment(3) == 'bulanan') ? 'active' : ''; ?>">
                        <i class="fas fa-chart-area"></i>
                        <span>Pendapatan Bulanan</span>
                    </a>
                </li>
                
                <li class="nav-divider">
                    <span>LAPORAN MEMBER</span>
                </li>
                
                <li>
                    <a href="<?php echo site_url('laporan/member'); ?>" class="<?php echo ($this->uri->segment(1) == 'laporan' && $this->uri->segment(2) == 'member') ? 'active' : ''; ?>">
                        <i class="fas fa-users-cog"></i>
                        <span>Laporan Member</span>
                    </a>
                </li>
                
                <li>
                    <a href="<?php echo site_url('laporan/member/statistik'); ?>" class="<?php echo ($this->uri->segment(1) == 'laporan' && $this->uri->segment(2) == 'member' && $this->uri->segment(3) == 'statistik') ? 'active' : ''; ?>">
                        <i class="fas fa-chart-pie"></i>
                        <span>Statistik Member</span>
                    </a>
                </li>
                
                <li class="nav-divider">
                    <span>LAPORAN LAPANGAN</span>
                </li>
                
                <li>
                    <a href="<?php echo site_url('laporan/lapangan'); ?>" class="<?php echo ($this->uri->segment(1) == 'laporan' && $this->uri->segment(2) == 'lapangan') ? 'active' : ''; ?>">
                        <i class="fas fa-map-marked-alt"></i>
                        <span>Laporan Lapangan</span>
                    </a>
                </li>
                
                <li>
                    <a href="<?php echo site_url('laporan/lapangan/utilisasi'); ?>" class="<?php echo ($this->uri->segment(1) == 'laporan' && $this->uri->segment(2) == 'lapangan' && $this->uri->segment(3) == 'utilisasi') ? 'active' : ''; ?>">
                        <i class="fas fa-percentage"></i>
                        <span>Utilisasi Lapangan</span>
                    </a>
                </li>
                
                <li class="nav-divider">
                    <span>DATA OPERASIONAL</span>
                </li>
                
                <li>
                    <a href="<?php echo site_url('booking'); ?>?view=readonly" class="<?php echo ($this->uri->segment(1) == 'booking' && $this->input->get('view') == 'readonly') ? 'active' : ''; ?>">
                        <i class="fas fa-calendar-alt"></i>
                        <span>Data Booking</span>
                    </a>
                </li>
                
                <li>
                    <a href="<?php echo site_url('transaksi'); ?>?view=readonly" class="<?php echo ($this->uri->segment(1) == 'transaksi' && $this->input->get('view') == 'readonly') ? 'active' : ''; ?>">
                        <i class="fas fa-receipt"></i>
                        <span>Data Transaksi</span>
                    </a>
                </li>
                
                <li>
                    <a href="<?php echo site_url('member'); ?>?view=readonly" class="<?php echo ($this->uri->segment(1) == 'member' && $this->input->get('view') == 'readonly') ? 'active' : ''; ?>">
                        <i class="fas fa-star"></i>
                        <span>Data Member</span>
                    </a>
                </li>
            <?php endif; ?>
            
            <!-- Menu umum -->
            <li class="nav-divider">
                <span>AKUN</span>
            </li>
            
            <li>
                <a href="<?php echo site_url('profile'); ?>" class="<?php echo ($this->uri->segment(1) == 'profile') ? 'active' : ''; ?>">
                    <i class="fas fa-user-cog"></i>
                    <span>Profil</span>
                </a>
            </li>
            
            <li>
                <a href="<?php echo site_url('profile/change_password'); ?>" class="<?php echo ($this->uri->segment(1) == 'profile' && $this->uri->segment(2) == 'change_password') ? 'active' : ''; ?>">
                    <i class="fas fa-key"></i>
                    <span>Ubah Password</span>
                </a>
            </li>
            
            <li>
                <a href="<?php echo site_url('profile/activity_log'); ?>" class="<?php echo ($this->uri->segment(1) == 'profile' && $this->uri->segment(2) == 'activity_log') ? 'active' : ''; ?>">
                    <i class="fas fa-history"></i>
                    <span>Log Aktivitas</span>
                </a>
            </li>
            
            <li class="nav-divider">
                <span>SISTEM</span>
            </li>
            
            <?php if ($this->session->userdata('role') == 'admin'): ?>
            <li>
                <a href="<?php echo site_url('system/backup'); ?>" class="<?php echo ($this->uri->segment(1) == 'system' && $this->uri->segment(2) == 'backup') ? 'active' : ''; ?>">
                    <i class="fas fa-database"></i>
                    <span>Backup Data</span>
                </a>
            </li>
            
            <li>
                <a href="<?php echo site_url('system/settings'); ?>" class="<?php echo ($this->uri->segment(1) == 'system' && $this->uri->segment(2) == 'settings') ? 'active' : ''; ?>">
                    <i class="fas fa-cogs"></i>
                    <span>Pengaturan</span>
                </a>
            </li>
            <?php endif; ?>
            
            <li>
                <a href="<?php echo site_url('help'); ?>" class="<?php echo ($this->uri->segment(1) == 'help') ? 'active' : ''; ?>">
                    <i class="fas fa-question-circle"></i>
                    <span>Bantuan</span>
                </a>
            </li>
            
            <li>
                <a href="<?php echo site_url('auth/logout'); ?>" onclick="return confirm('Yakin ingin logout?')">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </li>
        </ul>
    </nav>
</aside>