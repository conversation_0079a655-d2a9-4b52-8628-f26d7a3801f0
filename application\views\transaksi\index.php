<!-- Modern Header with Gradient -->
<div class="payment-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col">
                <div class="header-content">
                    <div class="header-icon">
                        <i class="fas fa-cash-register"></i>
                    </div>
                    <div class="header-text">
                        <h1>Pembayaran Booking</h1>
                        <p>Proses pembayaran untuk booking yang sudah dikonfirmasi</p>
                    </div>
                </div>
            </div>
            <div class="col-auto">
                <div class="header-actions">
                    <a href="<?php echo site_url('transaksi/riwayat'); ?>" class="btn btn-light btn-modern">
                        <i class="fas fa-history"></i>
                        <span>Riwayat</span>
                    </a>
                    <a href="<?php echo site_url('booking'); ?>" class="btn btn-light btn-modern">
                        <i class="fas fa-calendar-alt"></i>
                        <span>Booking</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modern Stats Cards -->
<div class="container-fluid">
    <div class="stats-container">
        <div class="stats-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h3><?php echo count($pending_payments); ?></h3>
                        <small>Menunggu Pembayaran</small>
                    </div>
                    <div class="ms-3">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="stats-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h3>Rp <?php
                            $total_pending = 0;
                            foreach($pending_payments as $booking) {
                                $total_pending += $booking->harga_total;
                            }
                            echo number_format($total_pending, 0, ',', '.');
                        ?></h3>
                        <small>Total Pending</small>
                    </div>
                    <div class="ms-3">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="stats-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h3><?php echo date('d M Y'); ?></h3>
                        <small>Hari Ini</small>
                    </div>
                    <div class="ms-3">
                        <i class="fas fa-calendar-day"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <!-- Booking List -->
        <div class="col-lg-8">
            <div class="modern-card booking-table">
                <div class="card-header">
                    <h5>
                        <i class="fas fa-list"></i> Booking Siap Bayar
                    </h5>
                </div>
                <div class="card-body p-0">
                <?php if (empty($pending_payments)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                        <h5 class="text-muted">Semua Booking Sudah Dibayar</h5>
                        <p class="text-muted">Tidak ada booking yang menunggu pembayaran</p>
                        <a href="<?php echo site_url('booking'); ?>" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Lihat Semua Booking
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Kode Booking</th>
                                    <th>Customer</th>
                                    <th>Lapangan</th>
                                    <th>Tanggal & Waktu</th>
                                    <th>Total</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($pending_payments as $booking): ?>
                                <tr class="booking-row" data-booking-id="<?php echo $booking->id; ?>">
                                    <td>
                                        <strong><?php echo $booking->kode_booking; ?></strong>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo $booking->customer_name; ?></strong>
                                            <?php if ($booking->member_name): ?>
                                                <br><small class="text-primary">
                                                    <i class="fas fa-star"></i> <?php echo $booking->member_name; ?>
                                                </small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td><?php echo $booking->nama_lapangan; ?></td>
                                    <td>
                                        <div>
                                            <?php echo date('d M Y', strtotime($booking->tanggal_booking)); ?>
                                            <br><small class="text-muted">
                                                <?php echo $booking->jam_mulai; ?> - <?php echo $booking->jam_selesai; ?>
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <strong class="text-success">
                                            Rp <?php echo number_format($booking->harga_total, 0, ',', '.'); ?>
                                        </strong>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-primary btn-sm" 
                                                onclick="selectBooking(<?php echo $booking->id; ?>)">
                                            <i class="fas fa-cash-register"></i> Bayar
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

        <!-- Payment Form -->
        <div class="col-lg-4">
            <div class="payment-form-container">
                <div class="payment-form-header">
                    <h5>
                        <i class="fas fa-credit-card"></i> Form Pembayaran
                    </h5>
                </div>
                <div class="payment-form-body">
                    <div id="no-booking-selected" class="no-booking-selected" <?php echo isset($selected_booking) ? 'style="display: none;"' : ''; ?>>
                        <i class="fas fa-hand-pointer"></i>
                        <h6>Pilih Booking</h6>
                        <p>Pilih booking dari daftar untuk memproses pembayaran</p>
                    </div>

                <div id="payment-form" <?php echo !isset($selected_booking) ? 'style="display: none;"' : ''; ?>>
                    <form id="paymentForm" method="post" action="<?php echo site_url('transaksi/process'); ?>">
                        <input type="hidden" id="booking_id" name="booking_id" value="<?php echo isset($selected_booking) ? $selected_booking->id : ''; ?>">
                        
                            <!-- Booking Info -->
                            <div id="booking-info" class="mb-4">
                                <?php if (isset($selected_booking)): ?>
                                    <div class="booking-info-alert">
                                        <h6><?php echo $selected_booking->kode_booking; ?></h6>
                                        <div class="small">
                                            <strong><?php echo $selected_booking->customer_name; ?></strong><br>
                                            <?php echo $selected_booking->nama_lapangan; ?><br>
                                            <?php echo date('d M Y', strtotime($selected_booking->tanggal_booking)); ?>
                                            (<?php echo $selected_booking->jam_mulai; ?> - <?php echo $selected_booking->jam_selesai; ?>)
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>

                        <!-- Payment Details -->
                        <div class="mb-3">
                            <label class="form-label">Total Harga</label>
                            <div class="input-group">
                                <span class="input-group-text">Rp</span>
                                <input type="text" id="total_harga" class="form-control" readonly 
                                       value="<?php echo isset($selected_booking) ? number_format($selected_booking->harga_total, 0, ',', '.') : ''; ?>">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="jumlah_bayar" class="form-label">Jumlah Bayar <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">Rp</span>
                                <input type="number" id="jumlah_bayar" name="jumlah_bayar" class="form-control" 
                                       placeholder="0" required min="0" step="1000">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Kembalian</label>
                            <div class="input-group">
                                <span class="input-group-text">Rp</span>
                                <input type="text" id="kembalian" class="form-control" readonly value="0">
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="metode_pembayaran" class="form-label">Metode Pembayaran <span class="text-danger">*</span></label>
                            <select id="metode_pembayaran" name="metode_pembayaran" class="form-control" required>
                                <option value="">Pilih Metode</option>
                                <option value="tunai">Tunai</option>
                                <option value="transfer">Transfer Bank</option>
                                <option value="kartu">Kartu Debit/Kredit</option>
                            </select>
                        </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-payment">
                                    <i class="fas fa-check"></i> Proses Pembayaran
                                </button>
                                <button type="button" class="btn btn-cancel" onclick="clearForm()">
                                    <i class="fas fa-times"></i> Batal
                                </button>
                            </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* ============================================================================
   MODERN PAYMENT INTERFACE STYLES
   ============================================================================ */

/* Payment Header */
.payment-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.payment-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s infinite linear;
    pointer-events: none;
}

@keyframes float {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.header-content {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 1;
}

.header-icon {
    width: 60px;
    height: 60px;
    background: rgba(255,255,255,0.2);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1.5rem;
    backdrop-filter: blur(10px);
}

.header-icon i {
    font-size: 24px;
    color: white;
}

.header-text h1 {
    margin: 0 0 0.5rem;
    font-size: 2rem;
    font-weight: 700;
    color: white;
}

.header-text p {
    margin: 0;
    color: rgba(255,255,255,0.9);
    font-size: 1rem;
}

.header-actions {
    display: flex;
    gap: 0.75rem;
    position: relative;
    z-index: 1;
}

.btn-modern {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

/* Stats Cards */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stats-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 1px solid #f0f0f0;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.stats-card .card-body {
    padding: 0;
}

.stats-card .d-flex {
    align-items: center;
}

.stats-card h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stats-card small {
    color: #6c757d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.75rem;
}

.stats-card i {
    font-size: 2.5rem;
    opacity: 0.3;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Modern Cards */
.modern-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 1px solid #f0f0f0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.modern-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.modern-card .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-bottom: 1px solid #e9ecef;
    padding: 1.25rem 1.5rem;
}

.modern-card .card-header h5 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    display: flex;
    align-items: center;
}

.modern-card .card-header i {
    margin-right: 0.75rem;
    color: #667eea;
    font-size: 1.2rem;
}

.modern-card .card-body {
    padding: 1.5rem;
}

/* Booking List Table */
.booking-table {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.booking-row {
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.booking-row::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 0;
    background: linear-gradient(135deg, #667eea, #764ba2);
    transition: width 0.3s ease;
}

.booking-row:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    transform: translateX(5px);
}

.booking-row:hover::before {
    width: 4px;
}

.booking-row.selected {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border-left: 4px solid #667eea;
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(102,126,234,0.2);
}

.table {
    margin: 0;
    font-size: 0.9rem;
}

.table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 1rem 0.75rem;
}

.table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid #f0f0f0;
}

/* Payment Form */
.payment-form-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
    position: sticky;
    top: 2rem;
}

.payment-form-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.25rem 1.5rem;
}

.payment-form-header h5 {
    margin: 0;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.payment-form-header i {
    margin-right: 0.75rem;
    font-size: 1.2rem;
}

.payment-form-body {
    padding: 1.5rem;
}

.no-booking-selected {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.no-booking-selected i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Form Styling */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102,126,234,0.1);
    transform: translateY(-1px);
}

.input-group {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.input-group-text {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #e9ecef;
    border-right: none;
    font-weight: 600;
    color: #495057;
}

.input-group .form-control {
    border-left: none;
    border-radius: 0 10px 10px 0;
}

/* Booking Info Alert */
.booking-info-alert {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border: 1px solid rgba(102,126,234,0.2);
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.booking-info-alert h6 {
    color: #667eea;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.booking-info-alert .small {
    color: #495057;
    line-height: 1.4;
}

/* Kembalian Display */
#kembalian {
    font-weight: 700;
    font-size: 1.1rem;
    color: #27ae60;
}

#kembalian.text-danger {
    color: #e74c3c !important;
}

/* Action Buttons */
.btn-payment {
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
    border: none;
    color: white;
    padding: 0.875rem 2rem;
    border-radius: 10px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(39,174,96,0.3);
}

.btn-payment:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(39,174,96,0.4);
    color: white;
}

.btn-cancel {
    background: #6c757d;
    border: none;
    color: white;
    padding: 0.875rem 2rem;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-cancel:hover {
    background: #5a6268;
    transform: translateY(-1px);
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .payment-header {
        padding: 1.5rem 0;
    }

    .header-content {
        flex-direction: column;
        text-align: center;
    }

    .header-icon {
        margin-right: 0;
        margin-bottom: 1rem;
    }

    .header-actions {
        margin-top: 1rem;
        justify-content: center;
    }

    .stats-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .payment-form-container {
        position: static;
        margin-top: 2rem;
    }

    .table-responsive {
        border-radius: 10px;
        margin: 0 -1rem;
    }
}

/* Loading Animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Success Animation */
.success-checkmark {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: block;
    stroke-width: 2;
    stroke: #27ae60;
    stroke-miterlimit: 10;
    margin: 10% auto;
    box-shadow: inset 0px 0px 0px #27ae60;
    animation: fill .4s ease-in-out .4s forwards, scale .3s ease-in-out .9s both;
}

@keyframes fill {
    100% {
        box-shadow: inset 0px 0px 0px 30px #27ae60;
    }
}

@keyframes scale {
    0%, 100% {
        transform: none;
    }
    50% {
        transform: scale3d(1.1, 1.1, 1);
    }
}
</style>

<script>
let selectedBookingId = <?php echo isset($selected_booking) ? $selected_booking->id : 'null'; ?>;
let bookingData = {};

$(document).ready(function() {
    // Auto-select if booking ID is provided
    <?php if (isset($selected_booking)): ?>
        selectBooking(<?php echo $selected_booking->id; ?>);
    <?php endif; ?>
    
    // Calculate change when payment amount or method changes
    $('#jumlah_bayar, #metode_pembayaran').on('input change keyup', function() {
        calculateChange();
    });

    // Format number input for jumlah bayar
    $('#jumlah_bayar').on('input', function() {
        let value = $(this).val().replace(/[^0-9]/g, '');
        if (value) {
            $(this).val(value); // Keep raw number for calculation
        }
        calculateChange();
    });

    // Form validation
    $('#paymentForm').on('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
        } else {
            // Show loading state
            const submitBtn = $(this).find('button[type="submit"]');
            const originalText = submitBtn.html();
            submitBtn.html('<span class="loading-spinner"></span> Memproses...').prop('disabled', true);
        }
    });
});

function selectBooking(bookingId) {
    // Remove previous selection
    $('.booking-row').removeClass('selected');
    
    // Add selection to current row
    $(`.booking-row[data-booking-id="${bookingId}"]`).addClass('selected');
    
    selectedBookingId = bookingId;
    
    // Get booking details via AJAX
    $.ajax({
        url: '<?php echo site_url('transaksi/get_booking_detail'); ?>',
        type: 'POST',
        data: {
            booking_id: bookingId,
            '<?php echo $this->security->get_csrf_token_name(); ?>': '<?php echo $this->security->get_csrf_hash(); ?>'
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                bookingData = response.data;
                updatePaymentForm(response.data);
            } else {
                alert(response.message);
            }
        },
        error: function() {
            alert('Terjadi kesalahan saat mengambil data booking');
        }
    });
}

function updatePaymentForm(data) {
    // Update form fields
    $('#booking_id').val(selectedBookingId);
    $('#total_harga').val(data.harga_total);

    // Update booking info with modern styling
    const bookingInfo = `
        <div class="booking-info-alert">
            <h6>${data.kode_booking}</h6>
            <div class="small">
                <strong>${data.customer_name}</strong><br>
                ${data.nama_lapangan}<br>
                ${data.tanggal_booking} (${data.jam_mulai} - ${data.jam_selesai})
                ${data.member_name ? '<br><span class="text-primary"><i class="fas fa-star"></i> ' + data.member_name + '</span>' : ''}
            </div>
        </div>
    `;

    $('#booking-info').html(bookingInfo);

    // Show payment form with animation
    $('#no-booking-selected').fadeOut(300, function() {
        $('#payment-form').fadeIn(300);
    });

    // Reset form
    $('#jumlah_bayar').val('').focus();
    $('#kembalian').val('0');
    $('#metode_pembayaran').val('');

    calculateChange();

    // Add success animation to selected row
    $(`.booking-row[data-booking-id="${selectedBookingId}"]`).addClass('selected');
}

function calculateChange() {
    // Get total harga from hidden input or booking data
    const totalHargaInput = $('#total_harga').val();
    const totalHarga = parseFloat(totalHargaInput) || 0;

    // Get jumlah bayar from input
    const jumlahBayarInput = $('#jumlah_bayar').val().replace(/[^\d]/g, ''); // Remove non-numeric characters
    const jumlahBayar = parseFloat(jumlahBayarInput) || 0;

    // Calculate kembalian
    const kembalian = jumlahBayar - totalHarga;

    // Format and display kembalian
    const kembalianFormatted = kembalian >= 0 ?
        'Rp ' + kembalian.toLocaleString('id-ID') :
        'Rp 0';

    $('#kembalian').text(kembalianFormatted);

    // Update kembalian color and validation
    const kembalianElement = $('#kembalian');
    if (kembalian < 0) {
        kembalianElement.removeClass('text-success').addClass('text-danger');
        kembalianElement.text('Jumlah bayar kurang!');
    } else {
        kembalianElement.removeClass('text-danger').addClass('text-success');
    }

    // Enable/disable submit button based on validation
    const submitBtn = $('button[type="submit"]');
    if (kembalian >= 0 && jumlahBayar > 0 && $('#metode_pembayaran').val()) {
        submitBtn.prop('disabled', false).removeClass('btn-secondary').addClass('btn-payment');
    } else {
        submitBtn.prop('disabled', true).removeClass('btn-payment').addClass('btn-secondary');
    }
}

function validateForm() {
    const totalHarga = parseFloat($('#total_harga').val()) || 0;
    const jumlahBayarInput = $('#jumlah_bayar').val().replace(/[^\d]/g, '');
    const jumlahBayar = parseFloat(jumlahBayarInput) || 0;
    const metode = $('#metode_pembayaran').val();

    if (!selectedBookingId) {
        showAlert('error', 'Pilih booking terlebih dahulu');
        return false;
    }

    if (jumlahBayar < totalHarga) {
        showAlert('error', `Jumlah bayar tidak boleh kurang dari total harga: Rp ${totalHarga.toLocaleString('id-ID')}`);
        $('#jumlah_bayar').focus();
        return false;
    }

    if (!metode) {
        showAlert('error', 'Pilih metode pembayaran');
        $('#metode_pembayaran').focus();
        return false;
    }

    return confirm('Apakah Anda yakin ingin memproses pembayaran ini?');
}

// Helper function to show alerts
function showAlert(type, message) {
    const alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : 'check-circle'}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // Remove existing alerts
    $('.alert').remove();

    // Add new alert at the top of payment form
    $('.payment-form-body').prepend(alertHtml);

    // Auto-hide after 5 seconds
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 5000);
}
}

function clearForm() {
    selectedBookingId = null;
    bookingData = {};

    $('.booking-row').removeClass('selected');
    $('#no-booking-selected').show();
    $('#payment-form').hide();
}

// Additional initialization after DOM is ready
$(document).ready(function() {
    // Initialize tooltips if using Bootstrap
    if (typeof bootstrap !== 'undefined') {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    // Auto-focus on search when filter is opened
    $('#toggleFilter').on('click', function() {
        setTimeout(() => {
            $('#search').focus();
        }, 300);
    });
});
</script>
