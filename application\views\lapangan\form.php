<!-- <PERSON> Header -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">
                <i class="fas fa-map-marked-alt"></i>
                <?php echo $action == 'create' ? 'Tambah Lapangan' : 'Edit Lapangan'; ?>
            </h1>
            <p class="page-subtitle">
                <?php echo $action == 'create' ? 'Tambah lapangan padel baru' : 'Perbarui data lapangan'; ?>
            </p>
        </div>
        <div class="page-actions">
            <a href="<?php echo site_url('lapangan'); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                Kembali
            </a>
        </div>
    </div>
</div>

<!-- Form Card -->
<div class="card">
    <div class="card-header">
        <h3>
            <i class="fas fa-<?php echo $action == 'create' ? 'plus' : 'edit'; ?>"></i>
            <?php echo $action == 'create' ? 'Data Lapangan Baru' : 'Edit Data Lapangan'; ?>
        </h3>
    </div>
    <div class="card-body">
        
        <!-- Form -->
        <?php if ($action == 'create'): ?>
            <?php echo form_open('lapangan/store', array('id' => 'lapangan-form', 'class' => 'needs-validation', 'novalidate' => '')); ?>
        <?php else: ?>
            <?php echo form_open('lapangan/update/' . $lapangan->id, array('id' => 'lapangan-form', 'class' => 'needs-validation', 'novalidate' => '')); ?>
        <?php endif; ?>
        
            <div class="row">
                <!-- Left Column -->
                <div class="col-md-8">
                    
                    <!-- Nama Lapangan -->
                    <div class="form-group">
                        <label for="nama_lapangan" class="form-label required">
                            <i class="fas fa-map-marker-alt"></i>
                            Nama Lapangan
                        </label>
                        <input type="text" 
                               id="nama_lapangan" 
                               name="nama_lapangan" 
                               class="form-control <?php echo form_error('nama_lapangan') ? 'is-invalid' : ''; ?>"
                               value="<?php echo set_value('nama_lapangan', $lapangan ? $lapangan->nama_lapangan : ''); ?>"
                               placeholder="Contoh: Lapangan 1, Court A, dll"
                               required>
                        
                        <!-- Real-time validation -->
                        <div class="invalid-feedback" id="nama-feedback">
                            <?php echo form_error('nama_lapangan'); ?>
                        </div>
                        
                        <div class="valid-feedback">
                            Nama lapangan tersedia!
                        </div>
                        
                        <small class="form-text text-muted">
                            Nama lapangan harus unik dan mudah diingat
                        </small>
                    </div>
                    
                    <!-- Deskripsi -->
                    <div class="form-group">
                        <label for="deskripsi" class="form-label">
                            <i class="fas fa-align-left"></i>
                            Deskripsi
                        </label>
                        <textarea id="deskripsi" 
                                  name="deskripsi" 
                                  class="form-control <?php echo form_error('deskripsi') ? 'is-invalid' : ''; ?>"
                                  rows="4"
                                  placeholder="Deskripsi lapangan (opsional)&#10;Contoh: Lapangan indoor dengan pencahayaan LED, lantai karet anti slip, tersedia AC, dll"><?php echo set_value('deskripsi', $lapangan ? $lapangan->deskripsi : ''); ?></textarea>
                        
                        <?php if (form_error('deskripsi')): ?>
                        <div class="invalid-feedback">
                            <?php echo form_error('deskripsi'); ?>
                        </div>
                        <?php endif; ?>
                        
                        <small class="form-text text-muted">
                            <span id="desc-counter">0</span>/500 karakter
                        </small>
                    </div>
                    
                </div>
                
                <!-- Right Column -->
                <div class="col-md-4">
                    
                    <!-- Harga per Jam -->
                    <div class="form-group">
                        <label for="harga_per_jam" class="form-label required">
                            <i class="fas fa-money-bill-wave"></i>
                            Harga per Jam
                        </label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">Rp</span>
                            </div>
                            <input type="text" 
                                   id="harga_per_jam" 
                                   name="harga_per_jam" 
                                   class="form-control currency-input <?php echo form_error('harga_per_jam') ? 'is-invalid' : ''; ?>"
                                   value="<?php echo set_value('harga_per_jam', $lapangan ? number_format($lapangan->harga_per_jam, 0, ',', '.') : ''); ?>"
                                   placeholder="50.000"
                                   required>
                        </div>
                        
                        <?php if (form_error('harga_per_jam')): ?>
                        <div class="invalid-feedback">
                            <?php echo form_error('harga_per_jam'); ?>
                        </div>
                        <?php endif; ?>
                        
                        <small class="form-text text-muted">
                            Masukkan harga sewa per jam
                        </small>
                    </div>
                    
                    <!-- Status -->
                    <div class="form-group">
                        <label for="status" class="form-label required">
                            <i class="fas fa-toggle-on"></i>
                            Status
                        </label>
                        <select id="status" 
                                name="status" 
                                class="form-control <?php echo form_error('status') ? 'is-invalid' : ''; ?>"
                                required>
                            <option value="">Pilih Status</option>
                            <option value="aktif" <?php echo set_select('status', 'aktif', ($lapangan && $lapangan->status == 'aktif') || (!$lapangan)); ?>>
                                ✅ Aktif
                            </option>
                            <option value="tidak_aktif" <?php echo set_select('status', 'tidak_aktif', $lapangan && $lapangan->status == 'tidak_aktif'); ?>>
                                ❌ Tidak Aktif
                            </option>
                        </select>
                        
                        <?php if (form_error('status')): ?>
                        <div class="invalid-feedback">
                            <?php echo form_error('status'); ?>
                        </div>
                        <?php endif; ?>
                        
                        <small class="form-text text-muted">
                            Lapangan aktif dapat dibooking
                        </small>
                    </div>
                    
                    <!-- Preview Harga -->
                    <div class="price-preview">
                        <div class="price-preview-header">
                            <i class="fas fa-calculator"></i>
                            Simulasi Harga
                        </div>
                        <div class="price-preview-body">
                            <div class="price-item">
                                <span>1 jam:</span>
                                <span id="price-1h">Rp 0</span>
                            </div>
                            <div class="price-item">
                                <span>2 jam:</span>
                                <span id="price-2h">Rp 0</span>
                            </div>
                            <div class="price-item">
                                <span>3 jam:</span>
                                <span id="price-3h">Rp 0</span>
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
            
            <!-- Form Actions -->
            <div class="form-actions">
                <hr>
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <?php if ($action == 'edit' && $lapangan): ?>
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            Terakhir diperbarui: <?php echo date('d M Y H:i', strtotime($lapangan->updated_at)); ?>
                        </small>
                        <?php endif; ?>
                    </div>
                    <div>
                        <a href="<?php echo site_url('lapangan'); ?>" class="btn btn-secondary mr-2">
                            <i class="fas fa-times"></i>
                            Batal
                        </a>
                        <button type="submit" class="btn btn-primary" id="submit-btn">
                            <i class="fas fa-save"></i>
                            <?php echo $action == 'create' ? 'Simpan Lapangan' : 'Perbarui Lapangan'; ?>
                        </button>
                    </div>
                </div>
            </div>
            
        <?php echo form_close(); ?>
        
    </div>
</div>

<style>
.price-preview {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-top: 20px;
}

.price-preview-header {
    background: #e9ecef;
    padding: 10px 15px;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    border-radius: 7px 7px 0 0;
}

.price-preview-body {
    padding: 15px;
}

.price-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
}

.price-item:last-child {
    margin-bottom: 0;
}

.price-item span:last-child {
    font-weight: 600;
    color: #28a745;
}

.form-label.required::after {
    content: ' *';
    color: #e74c3c;
}

.currency-input {
    text-align: right;
}

.form-actions {
    margin-top: 30px;
}

.is-invalid {
    border-color: #e74c3c;
}

.is-valid {
    border-color: #28a745;
}

.valid-feedback,
.invalid-feedback {
    display: block;
    font-size: 14px;
    margin-top: 5px;
}

.valid-feedback {
    color: #28a745;
}

.invalid-feedback {
    color: #e74c3c;
}

@media (max-width: 768px) {
    .price-preview {
        margin-top: 15px;
    }
    
    .form-actions .d-flex {
        flex-direction: column;
        align-items: stretch !important;
    }
    
    .form-actions .d-flex > div:last-child {
        margin-top: 15px;
        display: flex;
        gap: 10px;
    }
    
    .form-actions .btn {
        flex: 1;
    }
}
</style>

<script>
$(document).ready(function() {
    // Currency formatting
    $('#harga_per_jam').on('input', function() {
        let value = $(this).val().replace(/[^\d]/g, '');
        if (value) {
            $(this).val(formatCurrency(value));
            updatePricePreview(parseInt(value));
        } else {
            updatePricePreview(0);
        }
    });
    
    // Character counter for description
    $('#deskripsi').on('input', function() {
        const maxLength = 500;
        const currentLength = $(this).val().length;
        $('#desc-counter').text(currentLength);
        
        if (currentLength > maxLength) {
            $(this).addClass('is-invalid');
            $('#desc-counter').parent().addClass('text-danger');
        } else {
            $(this).removeClass('is-invalid');
            $('#desc-counter').parent().removeClass('text-danger');
        }
    });
    
    // Real-time name validation
    let nameCheckTimeout;
    $('#nama_lapangan').on('input', function() {
        const namaLapangan = $(this).val().trim();
        const excludeId = <?php echo $lapangan ? $lapangan->id : 'null'; ?>;
        
        clearTimeout(nameCheckTimeout);
        
        if (namaLapangan.length >= 2) {
            nameCheckTimeout = setTimeout(function() {
                checkLapanganName(namaLapangan, excludeId);
            }, 500);
        } else {
            $('#nama_lapangan').removeClass('is-valid is-invalid');
        }
    });
    
    // Form submission
    $('#lapangan-form').on('submit', function(e) {
        e.preventDefault();
        
        if (validateForm()) {
            showLoading($('#submit-btn'));
            
            // Convert currency to number
            const hargaInput = $('#harga_per_jam');
            const hargaValue = hargaInput.val().replace(/[^\d]/g, '');
            hargaInput.val(hargaValue);
            
            this.submit();
        }
    });
    
    // Initialize price preview
    const initialPrice = $('#harga_per_jam').val().replace(/[^\d]/g, '');
    if (initialPrice) {
        updatePricePreview(parseInt(initialPrice));
    }
    
    // Initialize character counter
    $('#deskripsi').trigger('input');
});

function formatCurrency(value) {
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
}

function updatePricePreview(harga) {
    $('#price-1h').text('Rp ' + formatCurrency(harga));
    $('#price-2h').text('Rp ' + formatCurrency(harga * 2));
    $('#price-3h').text('Rp ' + formatCurrency(harga * 3));
}

function checkLapanganName(namaLapangan, excludeId) {
    $.ajax({
        url: '<?php echo site_url("lapangan/check_name"); ?>',
        type: 'POST',
        data: {
            nama_lapangan: namaLapangan,
            exclude_id: excludeId
        },
        dataType: 'json',
        success: function(response) {
            const input = $('#nama_lapangan');
            const feedback = $('#nama-feedback');
            
            if (response.exists) {
                input.removeClass('is-valid').addClass('is-invalid');
                feedback.text('Nama lapangan sudah digunakan');
            } else {
                input.removeClass('is-invalid').addClass('is-valid');
                feedback.text('');
            }
        },
        error: function() {
            console.log('Error checking lapangan name');
        }
    });
}

function validateForm() {
    let isValid = true;
    
    // Check required fields
    $('#lapangan-form [required]').each(function() {
        const field = $(this);
        const value = field.val().trim();
        
        if (!value) {
            field.addClass('is-invalid');
            isValid = false;
        } else {
            field.removeClass('is-invalid');
        }
    });
    
    // Check if name is unique
    if ($('#nama_lapangan').hasClass('is-invalid')) {
        isValid = false;
    }
    
    // Check price
    const harga = $('#harga_per_jam').val().replace(/[^\d]/g, '');
    if (!harga || parseInt(harga) <= 0) {
        $('#harga_per_jam').addClass('is-invalid');
        isValid = false;
    }
    
    if (!isValid) {
        showAlert('error', 'Mohon perbaiki kesalahan pada form');
        
        // Scroll to first error
        const firstError = $('.is-invalid:first');
        if (firstError.length) {
            $('html, body').animate({
                scrollTop: firstError.offset().top - 100
            }, 500);
        }
    }
    
    return isValid;
}

function showLoading(element) {
    element.prop('disabled', true);
    const originalText = element.html();
    element.data('original-text', originalText);
    element.html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');
}
</script>