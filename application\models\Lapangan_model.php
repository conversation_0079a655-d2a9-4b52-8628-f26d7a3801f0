<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Lapangan_model extends CI_Model {

    private $table = 'lapangan';

    public function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    /**
     * Get all lapangan
     */
    public function get_all_lapangan($search = '')
    {
        if (!empty($search)) {
            $this->db->like('nama_lapangan', $search);
            $this->db->or_like('deskripsi', $search);
        }
        
        $this->db->order_by('nama_lapangan', 'ASC');
        return $this->db->get($this->table)->result();
    }

    /**
     * Get active lapangan only
     */
    public function get_active_lapangan()
    {
        $this->db->where('status', 'aktif');
        $this->db->order_by('nama_lapangan', 'ASC');
        return $this->db->get($this->table)->result();
    }

    /**
     * Get lapangan by ID
     */
    public function get_lapangan_by_id($id)
    {
        $this->db->where('id', $id);
        return $this->db->get($this->table)->row();
    }

    /**
     * Create new lapangan
     */
    public function create_lapangan($data)
    {
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->db->insert($this->table, $data);
    }

    /**
     * Update lapangan
     */
    public function update_lapangan($id, $data)
    {
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $this->db->where('id', $id);
        return $this->db->update($this->table, $data);
    }

    /**
     * Delete lapangan
     */
    public function delete_lapangan($id)
    {
        $this->db->where('id', $id);
        return $this->db->delete($this->table);
    }

    /**
     * Toggle lapangan status
     */
    public function toggle_status($id)
    {
        $lapangan = $this->get_lapangan_by_id($id);
        
        if ($lapangan) {
            $new_status = ($lapangan->status == 'aktif') ? 'tidak_aktif' : 'aktif';
            
            $data = array(
                'status' => $new_status,
                'updated_at' => date('Y-m-d H:i:s')
            );
            
            $this->db->where('id', $id);
            return $this->db->update($this->table, $data);
        }
        
        return false;
    }

    /**
     * Count active courts
     */
    public function count_active_courts()
    {
        $this->db->where('status', 'aktif');
        return $this->db->count_all_results($this->table);
    }

    /**
     * Count all courts
     */
    public function count_all_courts()
    {
        return $this->db->count_all($this->table);
    }

    /**
     * Check if lapangan name exists
     */
    public function name_exists($nama_lapangan, $exclude_id = null)
    {
        $this->db->where('nama_lapangan', $nama_lapangan);
        
        if ($exclude_id) {
            $this->db->where('id !=', $exclude_id);
        }
        
        return $this->db->count_all_results($this->table) > 0;
    }

    /**
     * Get lapangan with booking statistics
     */
    public function get_lapangan_with_stats()
    {
        $this->db->select('l.*, 
                          COUNT(b.id) as total_bookings,
                          SUM(CASE WHEN b.status_booking = "selesai" THEN b.harga_total ELSE 0 END) as total_revenue,
                          AVG(b.harga_total) as avg_booking_value');
        $this->db->from($this->table . ' l');
        $this->db->join('booking b', 'b.lapangan_id = l.id', 'left');
        $this->db->group_by('l.id');
        $this->db->order_by('l.nama_lapangan', 'ASC');
        
        return $this->db->get()->result();
    }

    /**
     * Get most booked lapangan
     */
    public function get_most_booked_lapangan($limit = 5)
    {
        $this->db->select('l.nama_lapangan, COUNT(b.id) as total_bookings');
        $this->db->from($this->table . ' l');
        $this->db->join('booking b', 'b.lapangan_id = l.id', 'left');
        $this->db->where('b.status_booking !=', 'dibatalkan');
        $this->db->group_by('l.id');
        $this->db->order_by('total_bookings', 'DESC');
        $this->db->limit($limit);
        
        return $this->db->get()->result();
    }

    /**
     * Get lapangan revenue ranking
     */
    public function get_revenue_ranking($period = 'month')
    {
        $this->db->select('l.nama_lapangan, SUM(b.harga_total) as total_revenue, COUNT(b.id) as total_bookings');
        $this->db->from($this->table . ' l');
        $this->db->join('booking b', 'b.lapangan_id = l.id', 'left');
        $this->db->where('b.status_booking', 'selesai');
        
        switch ($period) {
            case 'week':
                $this->db->where('b.tanggal_booking >=', date('Y-m-d', strtotime('-7 days')));
                break;
            case 'month':
                $this->db->where('DATE_FORMAT(b.tanggal_booking, "%Y-%m")', date('Y-m'));
                break;
            case 'year':
                $this->db->where('YEAR(b.tanggal_booking)', date('Y'));
                break;
        }
        
        $this->db->group_by('l.id');
        $this->db->order_by('total_revenue', 'DESC');
        
        return $this->db->get()->result();
    }

    /**
     * Get lapangan availability for a specific date
     */
    public function get_availability($date)
    {
        $this->db->select('l.*, 
                          COUNT(b.id) as booked_slots,
                          GROUP_CONCAT(CONCAT(b.jam_mulai, "-", b.jam_selesai) ORDER BY b.jam_mulai) as booked_times');
        $this->db->from($this->table . ' l');
        $this->db->join('booking b', 'b.lapangan_id = l.id AND b.tanggal_booking = "' . $date . '" AND b.status_booking != "dibatalkan"', 'left');
        $this->db->where('l.status', 'aktif');
        $this->db->group_by('l.id');
        $this->db->order_by('l.nama_lapangan', 'ASC');
        
        return $this->db->get()->result();
    }

    /**
     * Search available lapangan for specific time slot
     */
    public function search_available($date, $jam_mulai, $jam_selesai)
    {
        $this->db->select('l.*');
        $this->db->from($this->table . ' l');
        $this->db->where('l.status', 'aktif');
        
        // Subquery to exclude booked courts
        $this->db->where('l.id NOT IN (
            SELECT DISTINCT lapangan_id 
            FROM booking 
            WHERE tanggal_booking = "' . $date . '" 
            AND status_booking != "dibatalkan"
            AND (
                (jam_mulai < "' . $jam_selesai . '" AND jam_selesai > "' . $jam_mulai . '")
            )
        )');
        
        $this->db->order_by('l.harga_per_jam', 'ASC');
        
        return $this->db->get()->result();
    }

    /**
     * Get lapangan utilization rate
     */
    public function get_utilization_rate($lapangan_id, $period = 'month')
    {
        // Calculate total possible hours (assuming 16 hours per day)
        $days_in_period = 30; // Default for month
        
        switch ($period) {
            case 'week':
                $days_in_period = 7;
                $date_condition = 'b.tanggal_booking >= "' . date('Y-m-d', strtotime('-7 days')) . '"';
                break;
            case 'month':
                $days_in_period = date('j'); // Current day of month
                $date_condition = 'DATE_FORMAT(b.tanggal_booking, "%Y-%m") = "' . date('Y-m') . '"';
                break;
            case 'year':
                $days_in_period = date('z') + 1; // Day of year
                $date_condition = 'YEAR(b.tanggal_booking) = ' . date('Y');
                break;
        }
        
        $total_possible_hours = $days_in_period * 16; // 16 operating hours per day
        
        // Get actual booked hours
        $this->db->select('SUM(durasi_jam) as total_booked_hours');
        $this->db->from('booking b');
        $this->db->where('b.lapangan_id', $lapangan_id);
        $this->db->where('b.status_booking !=', 'dibatalkan');
        $this->db->where($date_condition);
        
        $result = $this->db->get()->row();
        $booked_hours = $result->total_booked_hours ? $result->total_booked_hours : 0;
        
        if ($total_possible_hours > 0) {
            return ($booked_hours / $total_possible_hours) * 100;
        }
        
        return 0;
    }

    /**
     * Get lapangan peak hours
     */
    public function get_peak_hours($lapangan_id)
    {
        $this->db->select('HOUR(jam_mulai) as hour, COUNT(*) as booking_count');
        $this->db->from('booking');
        $this->db->where('lapangan_id', $lapangan_id);
        $this->db->where('status_booking !=', 'dibatalkan');
        $this->db->where('tanggal_booking >=', date('Y-m-01')); // This month
        $this->db->group_by('HOUR(jam_mulai)');
        $this->db->order_by('booking_count', 'DESC');
        $this->db->limit(3);
        
        return $this->db->get()->result();
    }

    /**
     * Get maintenance schedule (if implemented)
     */
    public function get_maintenance_schedule($lapangan_id)
    {
        // This would be for future maintenance feature
        // For now, return empty array
        return array();
    }

    /**
     * Check if lapangan can be deleted
     */
    public function can_be_deleted($id)
    {
        // Check if there are any bookings for this lapangan
        $this->db->where('lapangan_id', $id);
        $booking_count = $this->db->count_all_results('booking');
        
        return $booking_count == 0;
    }

    /**
     * Get lapangan booking history
     */
    public function get_booking_history($lapangan_id, $limit = 10, $offset = 0)
    {
        $this->db->select('b.*, u.full_name as created_by_name');
        $this->db->from('booking b');
        $this->db->join('users u', 'u.id = b.created_by', 'left');
        $this->db->where('b.lapangan_id', $lapangan_id);
        $this->db->order_by('b.tanggal_booking', 'DESC');
        $this->db->order_by('b.jam_mulai', 'DESC');
        $this->db->limit($limit, $offset);
        
        return $this->db->get()->result();
    }

    /**
     * Count booking history
     */
    public function count_booking_history($lapangan_id)
    {
        $this->db->where('lapangan_id', $lapangan_id);
        return $this->db->count_all_results('booking');
    }

    /**
     * Get price range of all courts
     */
    public function get_price_range()
    {
        $this->db->select('MIN(harga_per_jam) as min_price, MAX(harga_per_jam) as max_price, AVG(harga_per_jam) as avg_price');
        $this->db->where('status', 'aktif');
        
        return $this->db->get($this->table)->row();
    }

    /**
     * Get courts by price range
     */
    public function get_by_price_range($min_price, $max_price)
    {
        $this->db->where('status', 'aktif');
        $this->db->where('harga_per_jam >=', $min_price);
        $this->db->where('harga_per_jam <=', $max_price);
        $this->db->order_by('harga_per_jam', 'ASC');
        
        return $this->db->get($this->table)->result();
    }
}