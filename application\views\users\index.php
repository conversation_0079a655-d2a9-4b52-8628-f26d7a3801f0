<!-- <PERSON>er -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">
                <i class="fas fa-users"></i>
                Manajemen Pengguna
            </h1>
            <p class="page-subtitle">Kelola pengguna sistem booking padel</p>
        </div>
        <div class="page-actions">
            <a href="<?php echo site_url('users/create'); ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                Tambah Pengguna
            </a>
        </div>
    </div>
</div>

<!-- Filters and Search -->
<div class="card">
    <div class="card-body">
        <?php echo form_open('users', array('method' => 'GET', 'class' => 'filter-form')); ?>
        <div class="row">
            <div class="col-md-4">
                <div class="form-group">
                    <label for="search"><PERSON>i <PERSON></label>
                    <input type="text" 
                           id="search" 
                           name="search" 
                           class="form-control" 
                           placeholder="Username, nama, atau email..."
                           value="<?php echo $search; ?>">
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label for="role">Filter Role</label>
                    <select id="role" name="role" class="form-control">
                        <option value="">Semua Role</option>
                        <option value="admin" <?php echo ($role_filter == 'admin') ? 'selected' : ''; ?>>Admin</option>
                        <option value="kasir" <?php echo ($role_filter == 'kasir') ? 'selected' : ''; ?>>Kasir</option>
                        <option value="pimpinan" <?php echo ($role_filter == 'pimpinan') ? 'selected' : ''; ?>>Pimpinan</option>
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label for="status">Filter Status</label>
                    <select id="status" name="status" class="form-control">
                        <option value="">Semua Status</option>
                        <option value="aktif" <?php echo ($status_filter == 'aktif') ? 'selected' : ''; ?>>Aktif</option>
                        <option value="tidak_aktif" <?php echo ($status_filter == 'tidak_aktif') ? 'selected' : ''; ?>>Tidak Aktif</option>
                    </select>
                </div>
            </div>
            <div class="col-md-2">
                <div class="form-group">
                    <label>&nbsp;</label>
                    <div class="d-flex">
                        <button type="submit" class="btn btn-secondary mr-2">
                            <i class="fas fa-search"></i>
                            Cari
                        </button>
                        <a href="<?php echo site_url('users'); ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                            Reset
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <?php echo form_close(); ?>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h3>
                <i class="fas fa-list"></i>
                Daftar Pengguna
                <span class="badge badge-primary"><?php echo $total_records; ?> pengguna</span>
            </h3>
            <div class="card-actions">
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                            type="button" 
                            data-toggle="dropdown">
                        <i class="fas fa-download"></i>
                        Export
                    </button>
                    <div class="dropdown-menu">
                        <a class="dropdown-item" href="<?php echo site_url('users/export_csv'); ?>">
                            <i class="fas fa-file-csv"></i>
                            Export CSV
                        </a>
                        <a class="dropdown-item" href="<?php echo site_url('users/print_list'); ?>" target="_blank">
                            <i class="fas fa-print"></i>
                            Print List
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <?php if (!empty($users)): ?>
        
        <!-- Bulk Actions -->
        <div class="bulk-actions mb-3" style="display: none;">
            <div class="d-flex align-items-center">
                <span class="mr-3">
                    <span id="selected-count">0</span> pengguna dipilih
                </span>
                <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-success" onclick="bulkAction('activate')">
                        <i class="fas fa-check"></i>
                        Aktifkan
                    </button>
                    <button type="button" class="btn btn-sm btn-warning" onclick="bulkAction('deactivate')">
                        <i class="fas fa-times"></i>
                        Nonaktifkan
                    </button>
                    <button type="button" class="btn btn-sm btn-danger" onclick="bulkAction('delete')">
                        <i class="fas fa-trash"></i>
                        Hapus
                    </button>
                </div>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-hover" id="users-table">
                <thead>
                    <tr>
                        <th width="30">
                            <input type="checkbox" id="select-all">
                        </th>
                        <th>Username</th>
                        <th>Nama Lengkap</th>
                        <th>Email</th>
                        <th>Telepon</th>
                        <th>Role</th>
                        <th>Status</th>
                        <th>Dibuat</th>
                        <th width="150" class="no-print">Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($users as $user): ?>
                    <tr>
                        <td>
                            <input type="checkbox" 
                                   class="user-checkbox" 
                                   value="<?php echo $user->id; ?>"
                                   <?php echo ($user->id == $this->session->userdata('user_id')) ? 'disabled' : ''; ?>>
                        </td>
                        <td>
                            <strong><?php echo $user->username; ?></strong>
                            <?php if ($user->id == $this->session->userdata('user_id')): ?>
                                <small class="text-primary">(Anda)</small>
                            <?php endif; ?>
                        </td>
                        <td><?php echo $user->full_name; ?></td>
                        <td><?php echo $user->email; ?></td>
                        <td><?php echo $user->phone; ?></td>
                        <td>
                            <?php 
                            $role_class = '';
                            switch($user->role) {
                                case 'admin':
                                    $role_class = 'badge-primary';
                                    break;
                                case 'kasir':
                                    $role_class = 'badge-info';
                                    break;
                                case 'pimpinan':
                                    $role_class = 'badge-success';
                                    break;
                            }
                            ?>
                            <span class="badge <?php echo $role_class; ?>">
                                <?php echo ucfirst($user->role); ?>
                            </span>
                        </td>
                        <td>
                            <div class="status-toggle">
                                <?php if ($user->status == 'aktif'): ?>
                                    <span class="badge badge-success status-badge" 
                                          data-id="<?php echo $user->id; ?>"
                                          onclick="toggleStatus(<?php echo $user->id; ?>)"
                                          style="cursor: pointer;">
                                        <i class="fas fa-check"></i>
                                        Aktif
                                    </span>
                                <?php else: ?>
                                    <span class="badge badge-danger status-badge" 
                                          data-id="<?php echo $user->id; ?>"
                                          onclick="toggleStatus(<?php echo $user->id; ?>)"
                                          style="cursor: pointer;">
                                        <i class="fas fa-times"></i>
                                        Tidak Aktif
                                    </span>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <small>
                                <?php echo date('d M Y', strtotime($user->created_at)); ?><br>
                                <?php echo date('H:i', strtotime($user->created_at)); ?>
                            </small>
                        </td>
                        <td class="no-print">
                            <div class="btn-group btn-group-sm">
                                <a href="<?php echo site_url('users/edit/' . $user->id); ?>" 
                                   class="btn btn-outline-primary"
                                   title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                
                                <?php if ($user->id != $this->session->userdata('user_id')): ?>
                                <button type="button" 
                                        class="btn btn-outline-danger"
                                        onclick="deleteUser(<?php echo $user->id; ?>, '<?php echo $user->username; ?>')"
                                        title="Hapus">
                                    <i class="fas fa-trash"></i>
                                </button>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <?php if (!empty($pagination)): ?>
        <div class="d-flex justify-content-between align-items-center mt-3">
            <div>
                <small class="text-muted">
                    Menampilkan <?php echo count($users); ?> dari <?php echo $total_records; ?> pengguna
                </small>
            </div>
            <div>
                <?php echo $pagination; ?>
            </div>
        </div>
        <?php endif; ?>
        
        <?php else: ?>
        <!-- Empty State -->
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h4>Tidak ada pengguna ditemukan</h4>
            <p class="text-muted">
                <?php if (!empty($search) || !empty($role_filter) || !empty($status_filter)): ?>
                    Coba ubah filter pencarian Anda.
                <?php else: ?>
                    Belum ada pengguna yang terdaftar dalam sistem.
                <?php endif; ?>
            </p>
            <a href="<?php echo site_url('users/create'); ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                Tambah Pengguna Pertama
            </a>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- JavaScript -->
<script>
$(document).ready(function() {
    // Select all checkbox
    $('#select-all').on('change', function() {
        $('.user-checkbox:not(:disabled)').prop('checked', this.checked);
        updateBulkActions();
    });
    
    // Individual checkbox
    $('.user-checkbox').on('change', function() {
        updateBulkActions();
        
        // Update select all checkbox
        const totalCheckboxes = $('.user-checkbox:not(:disabled)').length;
        const checkedCheckboxes = $('.user-checkbox:checked').length;
        $('#select-all').prop('checked', totalCheckboxes === checkedCheckboxes);
    });
    
    // Initialize search with debounce
    initializeSearch('#search', '#users-table');
});

function updateBulkActions() {
    const checkedCount = $('.user-checkbox:checked').length;
    $('#selected-count').text(checkedCount);
    
    if (checkedCount > 0) {
        $('.bulk-actions').show();
    } else {
        $('.bulk-actions').hide();
    }
}

function toggleStatus(userId) {
    if (userId == <?php echo $this->session->userdata('user_id'); ?>) {
        showAlert('error', 'Anda tidak dapat mengubah status akun sendiri.');
        return;
    }
    
    const badge = $(`.status-badge[data-id="${userId}"]`);
    const originalHtml = badge.html();
    
    badge.html('<i class="fas fa-spinner fa-spin"></i> Loading...');
    
    $.ajax({
        url: '<?php echo site_url("users/toggle_status"); ?>/' + userId,
        type: 'POST',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Update badge
                if (response.new_status === 'aktif') {
                    badge.removeClass('badge-danger').addClass('badge-success');
                    badge.html('<i class="fas fa-check"></i> Aktif');
                } else {
                    badge.removeClass('badge-success').addClass('badge-danger');
                    badge.html('<i class="fas fa-times"></i> Tidak Aktif');
                }
                
                showAlert('success', response.message);
            } else {
                badge.html(originalHtml);
                showAlert('error', response.message);
            }
        },
        error: function() {
            badge.html(originalHtml);
            showAlert('error', 'Terjadi kesalahan saat mengubah status.');
        }
    });
}

function deleteUser(userId, username) {
    if (userId == <?php echo $this->session->userdata('user_id'); ?>) {
        showAlert('error', 'Anda tidak dapat menghapus akun sendiri.');
        return;
    }
    
    if (confirm(`Yakin ingin menghapus pengguna "${username}"?\n\nTindakan ini tidak dapat dibatalkan.`)) {
        window.location.href = '<?php echo site_url("users/delete"); ?>/' + userId;
    }
}

function bulkAction(action) {
    const selectedIds = $('.user-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (selectedIds.length === 0) {
        showAlert('warning', 'Pilih pengguna terlebih dahulu.');
        return;
    }
    
    let actionText = '';
    switch(action) {
        case 'activate':
            actionText = 'mengaktifkan';
            break;
        case 'deactivate':
            actionText = 'menonaktifkan';
            break;
        case 'delete':
            actionText = 'menghapus';
            break;
    }
    
    if (confirm(`Yakin ingin ${actionText} ${selectedIds.length} pengguna yang dipilih?`)) {
        showLoading();
        
        $.ajax({
            url: '<?php echo site_url("users/bulk_action"); ?>',
            type: 'POST',
            data: {
                action: action,
                user_ids: selectedIds
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    showAlert('success', response.message);
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    showAlert('error', response.message);
                }
            },
            error: function() {
                showAlert('error', 'Terjadi kesalahan saat memproses data.');
            },
            complete: function() {
                hideLoading();
            }
        });
    }
}

// Initialize table search
function initializeSearch(searchInput, targetTable) {
    const searchFunction = debounce(function() {
        const searchTerm = $(searchInput).val().toLowerCase();
        const rows = $(targetTable + ' tbody tr');
        
        rows.each(function() {
            const row = $(this);
            const text = row.text().toLowerCase();
            
            if (text.includes(searchTerm)) {
                row.show();
            } else {
                row.hide();
            }
        });
    }, 300);
    
    $(searchInput).on('input', searchFunction);
}
</script>