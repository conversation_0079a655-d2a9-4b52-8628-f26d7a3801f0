<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h1><i class="fas fa-edit"></i> Edit Profil</h1>
            <p class="text-muted">Perbarui informasi profil Anda</p>
        </div>
        <div class="col-auto">
            <a href="<?php echo site_url('profile'); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-edit"></i> Edit Informasi Profil
                </h5>
            </div>
            <div class="card-body">
                <?php echo form_open('profile/edit', array('id' => 'editProfileForm')); ?>
                
                <!-- Username (Read Only) -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" value="<?php echo $user->username; ?>" readonly>
                        <small class="text-muted">Username tidak dapat diubah</small>
                    </div>
                    <div class="col-md-6">
                        <label for="role" class="form-label">Role</label>
                        <input type="text" class="form-control" id="role" value="<?php echo ucfirst($user->role); ?>" readonly>
                        <small class="text-muted">Role ditentukan oleh administrator</small>
                    </div>
                </div>

                <!-- Full Name -->
                <div class="mb-3">
                    <label for="full_name" class="form-label">Nama Lengkap <span class="text-danger">*</span></label>
                    <input type="text" class="form-control <?php echo form_error('full_name') ? 'is-invalid' : ''; ?>" 
                           id="full_name" name="full_name" 
                           value="<?php echo set_value('full_name', $user->full_name); ?>"
                           placeholder="Masukkan nama lengkap">
                    <?php echo form_error('full_name', '<div class="invalid-feedback">', '</div>'); ?>
                </div>

                <!-- Email -->
                <div class="mb-3">
                    <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                    <input type="email" class="form-control <?php echo form_error('email') ? 'is-invalid' : ''; ?>" 
                           id="email" name="email" 
                           value="<?php echo set_value('email', $user->email); ?>"
                           placeholder="Masukkan alamat email">
                    <?php echo form_error('email', '<div class="invalid-feedback">', '</div>'); ?>
                </div>

                <!-- Phone -->
                <div class="mb-3">
                    <label for="phone" class="form-label">Telepon</label>
                    <input type="text" class="form-control <?php echo form_error('phone') ? 'is-invalid' : ''; ?>" 
                           id="phone" name="phone" 
                           value="<?php echo set_value('phone', $user->phone); ?>"
                           placeholder="Masukkan nomor telepon">
                    <?php echo form_error('phone', '<div class="invalid-feedback">', '</div>'); ?>
                    <small class="text-muted">Format: *********** atau +*************</small>
                </div>

                <!-- Account Info -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <label class="form-label">Status Akun</label>
                        <div class="form-control-plaintext">
                            <span class="badge bg-<?php echo ($user->status == 'aktif') ? 'success' : 'secondary'; ?>">
                                <i class="fas fa-<?php echo ($user->status == 'aktif') ? 'check-circle' : 'pause-circle'; ?>"></i>
                                <?php echo ucfirst($user->status); ?>
                            </span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Bergabung Sejak</label>
                        <div class="form-control-plaintext">
                            <?php echo date('d F Y', strtotime($user->created_at)); ?>
                        </div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="d-flex justify-content-end gap-2">
                    <a href="<?php echo site_url('profile'); ?>" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Batal
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Simpan Perubahan
                    </button>
                </div>

                <?php echo form_close(); ?>
            </div>
        </div>

        <!-- Additional Actions -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cog"></i> Pengaturan Lainnya
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Keamanan</h6>
                        <p class="text-muted">Ubah password dan pengaturan keamanan akun</p>
                        <a href="<?php echo site_url('profile/change_password'); ?>" class="btn btn-outline-warning">
                            <i class="fas fa-key"></i> Ubah Password
                        </a>
                    </div>
                    <div class="col-md-6">
                        <h6>Aktivitas</h6>
                        <p class="text-muted">Lihat riwayat aktivitas dan login terakhir</p>
                        <a href="<?php echo site_url('profile/activity_log'); ?>" class="btn btn-outline-info">
                            <i class="fas fa-history"></i> Log Aktivitas
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Form validation
    $('#editProfileForm').on('submit', function(e) {
        let isValid = true;
        
        // Reset previous validation
        $('.form-control').removeClass('is-invalid');
        $('.invalid-feedback').remove();
        
        // Validate full name
        if ($('#full_name').val().trim() === '') {
            $('#full_name').addClass('is-invalid');
            $('#full_name').after('<div class="invalid-feedback">Nama lengkap harus diisi</div>');
            isValid = false;
        }
        
        // Validate email
        const email = $('#email').val().trim();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (email === '') {
            $('#email').addClass('is-invalid');
            $('#email').after('<div class="invalid-feedback">Email harus diisi</div>');
            isValid = false;
        } else if (!emailRegex.test(email)) {
            $('#email').addClass('is-invalid');
            $('#email').after('<div class="invalid-feedback">Format email tidak valid</div>');
            isValid = false;
        }
        
        // Validate phone (optional but if filled, must be valid)
        const phone = $('#phone').val().trim();
        if (phone !== '') {
            const phoneRegex = /^(\+62|62|0)[0-9]{9,13}$/;
            if (!phoneRegex.test(phone.replace(/[\s-]/g, ''))) {
                $('#phone').addClass('is-invalid');
                $('#phone').after('<div class="invalid-feedback">Format nomor telepon tidak valid</div>');
                isValid = false;
            }
        }
        
        if (!isValid) {
            e.preventDefault();
            return false;
        }
        
        // Show loading state
        const submitBtn = $(this).find('button[type="submit"]');
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');
    });
    
    // Phone number formatting
    $('#phone').on('input', function() {
        let value = $(this).val().replace(/\D/g, '');
        
        // Auto add +62 prefix for Indonesian numbers
        if (value.startsWith('8')) {
            value = '62' + value;
        } else if (value.startsWith('08')) {
            value = '62' + value.substring(1);
        }
        
        // Format display
        if (value.startsWith('62')) {
            value = '+' + value;
        }
        
        $(this).val(value);
    });
    
    // Real-time validation feedback
    $('.form-control').on('blur', function() {
        const field = $(this);
        const fieldName = field.attr('name');
        
        // Remove previous validation
        field.removeClass('is-invalid is-valid');
        field.next('.invalid-feedback').remove();
        
        if (fieldName === 'full_name' && field.val().trim() !== '') {
            field.addClass('is-valid');
        } else if (fieldName === 'email' && field.val().trim() !== '') {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (emailRegex.test(field.val().trim())) {
                field.addClass('is-valid');
            }
        } else if (fieldName === 'phone' && field.val().trim() !== '') {
            const phoneRegex = /^(\+62|62|0)[0-9]{9,13}$/;
            if (phoneRegex.test(field.val().replace(/[\s-]/g, ''))) {
                field.addClass('is-valid');
            }
        }
    });
});
</script>