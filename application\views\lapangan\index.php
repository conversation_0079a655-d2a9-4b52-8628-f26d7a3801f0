<!-- <PERSON>er -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">
                <i class="fas fa-map-marked-alt"></i>
                Manaj<PERSON><PERSON>
            </h1>
            <p class="page-subtitle">Kelola lapangan padel</p>
        </div>
        <div class="page-actions">
            <a href="<?php echo site_url('lapangan/create'); ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                Tambah Lapangan
            </a>
        </div>
    </div>
</div>

<!-- Search -->
<div class="card">
    <div class="card-body">
        <?php echo form_open('lapangan', array('method' => 'GET', 'class' => 'search-form')); ?>
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="search"><PERSON><PERSON></label>
                    <div class="input-group">
                        <input type="text" 
                               id="search" 
                               name="search" 
                               class="form-control" 
                               placeholder="Nama lapangan atau deskripsi..."
                               value="<?php echo $search; ?>">
                        <div class="input-group-append">
                            <button type="submit" class="btn btn-secondary">
                                <i class="fas fa-search"></i>
                                Cari
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label>&nbsp;</label>
                    <div class="d-flex">
                        <a href="<?php echo site_url('lapangan'); ?>" class="btn btn-outline-secondary mr-2">
                            <i class="fas fa-times"></i>
                            Reset
                        </a>
                        <button type="button" class="btn btn-info" onclick="getStatistics()">
                            <i class="fas fa-chart-bar"></i>
                            Statistik
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <?php echo form_close(); ?>
    </div>
</div>

<!-- Lapangan Table -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h3>
                <i class="fas fa-list"></i>
                Daftar Lapangan
                <span class="badge badge-primary"><?php echo count($lapangan); ?> lapangan</span>
            </h3>
            <div class="card-actions">
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                            type="button" 
                            data-toggle="dropdown">
                        <i class="fas fa-download"></i>
                        Export
                    </button>
                    <div class="dropdown-menu">
                        <a class="dropdown-item" href="<?php echo site_url('lapangan/export_csv'); ?>">
                            <i class="fas fa-file-csv"></i>
                            Export CSV
                        </a>
                        <a class="dropdown-item" href="#" onclick="printTable('#lapangan-table', 'Daftar Lapangan')">
                            <i class="fas fa-print"></i>
                            Print List
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <?php if (!empty($lapangan)): ?>
        
        <div class="table-responsive">
            <table class="table table-hover" id="lapangan-table">
                <thead>
                    <tr>
                        <th>Nama Lapangan</th>
                        <th>Deskripsi</th>
                        <th>Harga per Jam</th>
                        <th>Status</th>
                        <th>Dibuat</th>
                        <th width="150" class="no-print">Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($lapangan as $lap): ?>
                    <tr>
                        <td>
                            <div class="lapangan-info">
                                <strong><?php echo $lap->nama_lapangan; ?></strong>
                                <small class="d-block text-muted">ID: <?php echo $lap->id; ?></small>
                            </div>
                        </td>
                        <td>
                            <div class="description-cell">
                                <?php if (!empty($lap->deskripsi)): ?>
                                    <?php echo substr($lap->deskripsi, 0, 100); ?>
                                    <?php if (strlen($lap->deskripsi) > 100): ?>
                                        <span class="text-muted">...</span>
                                        <br>
                                        <small>
                                            <a href="#" onclick="showFullDescription('<?php echo addslashes($lap->deskripsi); ?>')" 
                                               class="text-primary">
                                                Lihat selengkapnya
                                            </a>
                                        </small>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <div class="price-cell">
                                <strong class="text-success">
                                    Rp <?php echo number_format($lap->harga_per_jam, 0, ',', '.'); ?>
                                </strong>
                                <small class="d-block text-muted">/jam</small>
                            </div>
                        </td>
                        <td>
                            <div class="status-toggle">
                                <?php if ($lap->status == 'aktif'): ?>
                                    <span class="badge badge-success status-badge" 
                                          data-id="<?php echo $lap->id; ?>"
                                          onclick="toggleStatus(<?php echo $lap->id; ?>)"
                                          style="cursor: pointer;"
                                          title="Klik untuk nonaktifkan">
                                        <i class="fas fa-check"></i>
                                        Aktif
                                    </span>
                                <?php else: ?>
                                    <span class="badge badge-danger status-badge" 
                                          data-id="<?php echo $lap->id; ?>"
                                          onclick="toggleStatus(<?php echo $lap->id; ?>)"
                                          style="cursor: pointer;"
                                          title="Klik untuk aktifkan">
                                        <i class="fas fa-times"></i>
                                        Tidak Aktif
                                    </span>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <small>
                                <?php echo date('d M Y', strtotime($lap->created_at)); ?><br>
                                <?php echo date('H:i', strtotime($lap->created_at)); ?>
                            </small>
                        </td>
                        <td class="no-print">
                            <div class="btn-group btn-group-sm">
                                <a href="<?php echo site_url('lapangan/detail/' . $lap->id); ?>" 
                                   class="btn btn-outline-info"
                                   title="Detail">
                                    <i class="fas fa-eye"></i>
                                </a>
                                
                                <a href="<?php echo site_url('lapangan/edit/' . $lap->id); ?>" 
                                   class="btn btn-outline-primary"
                                   title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                
                                <button type="button" 
                                        class="btn btn-outline-danger"
                                        onclick="deleteLapangan(<?php echo $lap->id; ?>, '<?php echo addslashes($lap->nama_lapangan); ?>')"
                                        title="Hapus">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <?php else: ?>
        <!-- Empty State -->
        <div class="text-center py-5">
            <i class="fas fa-map-marked-alt fa-3x text-muted mb-3"></i>
            <h4>Tidak ada lapangan ditemukan</h4>
            <p class="text-muted">
                <?php if (!empty($search)): ?>
                    Coba ubah kata kunci pencarian Anda.
                <?php else: ?>
                    Belum ada lapangan yang terdaftar dalam sistem.
                <?php endif; ?>
            </p>
            <a href="<?php echo site_url('lapangan/create'); ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                Tambah Lapangan Pertama
            </a>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Statistics Modal -->
<div class="modal fade" id="statisticsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-chart-bar"></i>
                    Statistik Lapangan
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="stats-grid-modal">
                    <div class="stat-item">
                        <div class="stat-label">Total Lapangan</div>
                        <div class="stat-value" id="stat-total">-</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Lapangan Aktif</div>
                        <div class="stat-value text-success" id="stat-active">-</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Lapangan Nonaktif</div>
                        <div class="stat-value text-danger" id="stat-inactive">-</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Harga Terendah</div>
                        <div class="stat-value text-info" id="stat-min-price">-</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Harga Tertinggi</div>
                        <div class="stat-value text-warning" id="stat-max-price">-</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Harga Rata-rata</div>
                        <div class="stat-value text-primary" id="stat-avg-price">-</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<!-- Description Modal -->
<div class="modal fade" id="descriptionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle"></i>
                    Deskripsi Lapangan
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p id="full-description"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<style>
.lapangan-info strong {
    color: #2c3e50;
    font-size: 1.1rem;
}

.description-cell {
    max-width: 300px;
}

.price-cell strong {
    font-size: 1.1rem;
}

.status-badge {
    transition: all 0.3s ease;
    cursor: pointer;
}

.status-badge:hover {
    transform: scale(1.1);
}

.stats-grid-modal {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.stat-item {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.stat-label {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 10px;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
}

@media (max-width: 768px) {
    .stats-grid-modal {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .page-header .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
    }
    
    .page-actions {
        margin-top: 15px;
        width: 100%;
    }
    
    .search-form .row {
        flex-direction: column;
    }
    
    .btn-group {
        flex-direction: column;
    }
}
</style>

<script>
$(document).ready(function() {
    // Initialize search
    initializeSearch('#search', '#lapangan-table');
});

function toggleStatus(lapanganId) {
    const badge = $(`.status-badge[data-id="${lapanganId}"]`);
    const originalHtml = badge.html();
    
    badge.html('<i class="fas fa-spinner fa-spin"></i> Loading...');
    
    $.ajax({
        url: '<?php echo site_url("lapangan/toggle_status"); ?>/' + lapanganId,
        type: 'POST',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Update badge
                if (response.new_status === 'aktif') {
                    badge.removeClass('badge-danger').addClass('badge-success');
                    badge.html('<i class="fas fa-check"></i> Aktif');
                    badge.attr('title', 'Klik untuk nonaktifkan');
                } else {
                    badge.removeClass('badge-success').addClass('badge-danger');
                    badge.html('<i class="fas fa-times"></i> Tidak Aktif');
                    badge.attr('title', 'Klik untuk aktifkan');
                }
                
                showAlert('success', response.message);
            } else {
                badge.html(originalHtml);
                showAlert('error', response.message);
            }
        },
        error: function() {
            badge.html(originalHtml);
            showAlert('error', 'Terjadi kesalahan saat mengubah status.');
        }
    });
}

function deleteLapangan(lapanganId, namaLapangan) {
    if (confirm(`Yakin ingin menghapus lapangan "${namaLapangan}"?\n\nTindakan ini tidak dapat dibatalkan.`)) {
        showLoading();
        window.location.href = '<?php echo site_url("lapangan/delete"); ?>/' + lapanganId;
    }
}

function getStatistics() {
    $('#statisticsModal').modal('show');
    
    // Reset stats
    $('.stat-value').text('Loading...');
    
    $.ajax({
        url: '<?php echo site_url("lapangan/statistics"); ?>',
        type: 'POST',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                const data = response.data;
                $('#stat-total').text(data.total);
                $('#stat-active').text(data.active);
                $('#stat-inactive').text(data.inactive);
                $('#stat-min-price').text('Rp ' + formatNumber(data.min_price));
                $('#stat-max-price').text('Rp ' + formatNumber(data.max_price));
                $('#stat-avg-price').text('Rp ' + formatNumber(Math.round(data.avg_price)));
            } else {
                showAlert('error', 'Gagal memuat statistik.');
            }
        },
        error: function() {
            showAlert('error', 'Terjadi kesalahan saat memuat statistik.');
        }
    });
}

function showFullDescription(description) {
    $('#full-description').text(description);
    $('#descriptionModal').modal('show');
}

function formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
}

// Initialize table search
function initializeSearch(searchInput, targetTable) {
    const searchFunction = debounce(function() {
        const searchTerm = $(searchInput).val().toLowerCase();
        const rows = $(targetTable + ' tbody tr');
        
        rows.each(function() {
            const row = $(this);
            const text = row.text().toLowerCase();
            
            if (text.includes(searchTerm)) {
                row.show();
            } else {
                row.hide();
            }
        });
    }, 300);
    
    $(searchInput).on('input', searchFunction);
}
</script>