<!-- Page Header -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">
                <i class="fas fa-map-marked-alt"></i>
                <?php echo $lapangan->nama_lapangan; ?>
            </h1>
            <p class="page-subtitle">Detail dan statistik lapangan</p>
        </div>
        <div class="page-actions">
            <a href="<?php echo site_url('lapangan'); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                Kembali
            </a>
            <a href="<?php echo site_url('lapangan/edit/' . $lapangan->id); ?>" class="btn btn-primary">
                <i class="fas fa-edit"></i>
                Edit Lapangan
            </a>
        </div>
    </div>
</div>

<!-- Main Info -->
<div class="row">
    <!-- Basic Information -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3>
                    <i class="fas fa-info-circle"></i>
                    Informasi Lapangan
                </h3>
            </div>
            <div class="card-body">
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">
                            <i class="fas fa-tag"></i>
                            ID Lapangan
                        </div>
                        <div class="info-value">#<?php echo $lapangan->id; ?></div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">
                            <i class="fas fa-map-marker-alt"></i>
                            Nama Lapangan
                        </div>
                        <div class="info-value">
                            <strong><?php echo $lapangan->nama_lapangan; ?></strong>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">
                            <i class="fas fa-money-bill-wave"></i>
                            Harga per Jam
                        </div>
                        <div class="info-value">
                            <span class="price-display">
                                Rp <?php echo number_format($lapangan->harga_per_jam, 0, ',', '.'); ?>
                            </span>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">
                            <i class="fas fa-toggle-on"></i>
                            Status
                        </div>
                        <div class="info-value">
                            <?php if ($lapangan->status == 'aktif'): ?>
                                <span class="badge badge-success badge-lg">
                                    <i class="fas fa-check"></i>
                                    Aktif
                                </span>
                            <?php else: ?>
                                <span class="badge badge-danger badge-lg">
                                    <i class="fas fa-times"></i>
                                    Tidak Aktif
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="info-item full-width">
                        <div class="info-label">
                            <i class="fas fa-align-left"></i>
                            Deskripsi
                        </div>
                        <div class="info-value">
                            <?php if (!empty($lapangan->deskripsi)): ?>
                                <p class="description-text"><?php echo nl2br($lapangan->deskripsi); ?></p>
                            <?php else: ?>
                                <span class="text-muted">Tidak ada deskripsi</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">
                            <i class="fas fa-calendar-plus"></i>
                            Dibuat
                        </div>
                        <div class="info-value">
                            <?php echo date('d M Y H:i', strtotime($lapangan->created_at)); ?>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">
                            <i class="fas fa-calendar-check"></i>
                            Terakhir Diperbarui
                        </div>
                        <div class="info-value">
                            <?php echo date('d M Y H:i', strtotime($lapangan->updated_at)); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Booking History -->
        <div class="card mt-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h3>
                        <i class="fas fa-history"></i>
                        Riwayat Booking
                    </h3>
                    <span class="badge badge-info">
                        <?php echo $total_history; ?> total booking
                    </span>
                </div>
            </div>
            <div class="card-body">
                <?php if (!empty($booking_history)): ?>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Kode Booking</th>
                                <th>Pemesan</th>
                                <th>Tanggal</th>
                                <th>Jam</th>
                                <th>Durasi</th>
                                <th>Total</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($booking_history as $booking): ?>
                            <tr>
                                <td>
                                    <strong><?php echo $booking->kode_booking; ?></strong>
                                </td>
                                <td><?php echo $booking->nama_pemesan; ?></td>
                                <td><?php echo date('d M Y', strtotime($booking->tanggal_booking)); ?></td>
                                <td>
                                    <?php echo date('H:i', strtotime($booking->jam_mulai)); ?> - 
                                    <?php echo date('H:i', strtotime($booking->jam_selesai)); ?>
                                </td>
                                <td><?php echo $booking->durasi_jam; ?> jam</td>
                                <td>
                                    <span class="text-success">
                                        Rp <?php echo number_format($booking->harga_total, 0, ',', '.'); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php 
                                    $status_class = '';
                                    switch($booking->status_booking) {
                                        case 'pending':
                                            $status_class = 'badge-warning';
                                            break;
                                        case 'dikonfirmasi':
                                            $status_class = 'badge-info';
                                            break;
                                        case 'selesai':
                                            $status_class = 'badge-success';
                                            break;
                                        case 'dibatalkan':
                                            $status_class = 'badge-danger';
                                            break;
                                    }
                                    ?>
                                    <span class="badge <?php echo $status_class; ?>">
                                        <?php echo ucfirst($booking->status_booking); ?>
                                    </span>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <?php if ($total_history > 10): ?>
                <div class="text-center mt-3">
                    <a href="<?php echo site_url('booking?lapangan_id=' . $lapangan->id); ?>" class="btn btn-outline-primary">
                        <i class="fas fa-eye"></i>
                        Lihat Semua Booking (<?php echo $total_history; ?>)
                    </a>
                </div>
                <?php endif; ?>
                
                <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Belum ada riwayat booking</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Statistics Sidebar -->
    <div class="col-md-4">
        <!-- Quick Stats -->
        <div class="card">
            <div class="card-header">
                <h3>
                    <i class="fas fa-chart-bar"></i>
                    Statistik
                </h3>
            </div>
            <div class="card-body">
                <div class="stat-cards">
                    <div class="stat-card-mini">
                        <div class="stat-icon text-primary">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">
                                <?php echo number_format($utilization_rate, 1); ?>%
                            </div>
                            <div class="stat-label">Tingkat Utilizasi</div>
                        </div>
                    </div>
                    
                    <div class="stat-card-mini">
                        <div class="stat-icon text-success">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">
                                <?php echo $total_history; ?>
                            </div>
                            <div class="stat-label">Total Booking</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Peak Hours -->
        <div class="card mt-4">
            <div class="card-header">
                <h3>
                    <i class="fas fa-clock"></i>
                    Jam Tersibuk
                </h3>
            </div>
            <div class="card-body">
                <?php if (!empty($peak_hours)): ?>
                <div class="peak-hours-list">
                    <?php foreach ($peak_hours as $index => $peak): ?>
                    <div class="peak-hour-item">
                        <div class="peak-rank">#{<?php echo $index + 1; ?>}</div>
                        <div class="peak-time">
                            <?php echo sprintf('%02d:00', $peak->hour); ?> - <?php echo sprintf('%02d:00', $peak->hour + 1); ?>
                        </div>
                        <div class="peak-count">
                            <span class="badge badge-primary"><?php echo $peak->booking_count; ?> booking</span>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <div class="text-center py-3">
                    <i class="fas fa-clock fa-2x text-muted mb-2"></i>
                    <p class="text-muted">Belum ada data jam tersibuk</p>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Price Comparison -->
        <div class="card mt-4">
            <div class="card-header">
                <h3>
                    <i class="fas fa-calculator"></i>
                    Simulasi Harga
                </h3>
            </div>
            <div class="card-body">
                <div class="price-simulation">
                    <div class="price-sim-item">
                        <span class="duration">1 jam</span>
                        <span class="price">Rp <?php echo number_format($lapangan->harga_per_jam, 0, ',', '.'); ?></span>
                    </div>
                    <div class="price-sim-item">
                        <span class="duration">2 jam</span>
                        <span class="price">Rp <?php echo number_format($lapangan->harga_per_jam * 2, 0, ',', '.'); ?></span>
                    </div>
                    <div class="price-sim-item">
                        <span class="duration">3 jam</span>
                        <span class="price">Rp <?php echo number_format($lapangan->harga_per_jam * 3, 0, ',', '.'); ?></span>
                    </div>
                    <div class="price-sim-item">
                        <span class="duration">4 jam</span>
                        <span class="price">Rp <?php echo number_format($lapangan->harga_per_jam * 4, 0, ',', '.'); ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card mt-4">
            <div class="card-header">
                <h3>
                    <i class="fas fa-bolt"></i>
                    Aksi Cepat
                </h3>
            </div>
            <div class="card-body">
                <div class="quick-actions-vertical">
                    <a href="<?php echo site_url('booking/create?lapangan_id=' . $lapangan->id); ?>" class="btn btn-success btn-block">
                        <i class="fas fa-plus"></i>
                        Buat Booking
                    </a>
                    
                    <a href="<?php echo site_url('lapangan/edit/' . $lapangan->id); ?>" class="btn btn-primary btn-block">
                        <i class="fas fa-edit"></i>
                        Edit Lapangan
                    </a>
                    
                    <a href="<?php echo site_url('booking?lapangan_id=' . $lapangan->id); ?>" class="btn btn-info btn-block">
                        <i class="fas fa-list"></i>
                        Lihat Semua Booking
                    </a>
                    
                    <button type="button" class="btn btn-warning btn-block" onclick="toggleStatus(<?php echo $lapangan->id; ?>)">
                        <i class="fas fa-toggle-on"></i>
                        <?php echo $lapangan->status == 'aktif' ? 'Nonaktifkan' : 'Aktifkan'; ?>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.info-item {
    display: flex;
    flex-direction: column;
}

.info-item.full-width {
    grid-column: 1 / -1;
}

.info-label {
    font-weight: 600;
    color: #6c757d;
    margin-bottom: 8px;
    font-size: 14px;
}

.info-label i {
    margin-right: 8px;
    color: #3498db;
}

.info-value {
    color: #2c3e50;
    font-size: 15px;
}

.price-display {
    font-size: 1.3rem;
    font-weight: 700;
    color: #28a745;
}

.badge-lg {
    font-size: 0.9rem;
    padding: 8px 12px;
}

.description-text {
    margin: 0;
    line-height: 1.6;
}

.stat-cards {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
}

.stat-card-mini {
    display: flex;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.stat-icon {
    font-size: 2rem;
    margin-right: 15px;
}

.stat-content .stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-content .stat-label {
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.peak-hours-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.peak-hour-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
}

.peak-rank {
    font-weight: 700;
    color: #3498db;
    min-width: 30px;
}

.peak-time {
    flex: 1;
    margin: 0 10px;
    font-weight: 600;
    color: #2c3e50;
}

.price-simulation {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.price-sim-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
}

.price-sim-item .duration {
    font-weight: 600;
    color: #495057;
}

.price-sim-item .price {
    font-weight: 700;
    color: #28a745;
}

.quick-actions-vertical {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

@media (max-width: 768px) {
    .info-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .page-header .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
    }
    
    .page-actions {
        margin-top: 15px;
        width: 100%;
        display: flex;
        gap: 10px;
    }
    
    .page-actions .btn {
        flex: 1;
    }
    
    .stat-cards {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
function toggleStatus(lapanganId) {
    const confirmText = <?php echo $lapangan->status == 'aktif' ? '"Nonaktifkan"' : '"Aktifkan"'; ?>;
    
    if (confirm(`${confirmText} lapangan ini?`)) {
        $.ajax({
            url: '<?php echo site_url("lapangan/toggle_status"); ?>/' + lapanganId,
            type: 'POST',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    showAlert('success', response.message);
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    showAlert('error', response.message);
                }
            },
            error: function() {
                showAlert('error', 'Terjadi kesalahan saat mengubah status.');
            }
        });
    }
}
</script>