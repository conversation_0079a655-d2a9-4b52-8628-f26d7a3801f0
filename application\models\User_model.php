<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class User_model extends CI_Model {

    private $table = 'users';

    public function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    /**
     * Verify login credentials
     */
    public function verify_login($username, $password)
    {
        // Get user by username
        $this->db->where('username', $username);
        $this->db->where('status', 'aktif');
        $query = $this->db->get($this->table);
        
        if ($query->num_rows() == 1) {
            $user = $query->row();
            
            // Verify password
            if (password_verify($password, $user->password)) {
                return $user;
            }
        }
        
        return false;
    }

    /**
     * Get user by ID
     */
    public function get_user_by_id($id)
    {
        $this->db->where('id', $id);
        $query = $this->db->get($this->table);
        
        if ($query->num_rows() == 1) {
            return $query->row();
        }
        
        return false;
    }

    /**
     * Get user by username
     */
    public function get_user_by_username($username)
    {
        $this->db->where('username', $username);
        $query = $this->db->get($this->table);
        
        if ($query->num_rows() == 1) {
            return $query->row();
        }
        
        return false;
    }

    /**
     * Get all users with pagination and filters
     */
    public function get_all_users($limit = null, $start = 0, $search = '', $role = '', $status = '')
    {
        $this->db->select('*');
        $this->db->from($this->table);
        
        // Search
        if (!empty($search)) {
            $this->db->group_start();
            $this->db->like('username', $search);
            $this->db->or_like('full_name', $search);
            $this->db->or_like('email', $search);
            $this->db->group_end();
        }
        
        // Role filter
        if (!empty($role)) {
            $this->db->where('role', $role);
        }
        
        // Status filter
        if (!empty($status)) {
            $this->db->where('status', $status);
        }
        
        $this->db->order_by('created_at', 'DESC');
        
        if ($limit) {
            $this->db->limit($limit, $start);
        }
        
        return $this->db->get()->result();
    }

    /**
     * Count all users with filters
     */
    public function count_all_users($search = '', $role = '', $status = '')
    {
        $this->db->from($this->table);
        
        // Search
        if (!empty($search)) {
            $this->db->group_start();
            $this->db->like('username', $search);
            $this->db->or_like('full_name', $search);
            $this->db->or_like('email', $search);
            $this->db->group_end();
        }
        
        // Role filter
        if (!empty($role)) {
            $this->db->where('role', $role);
        }
        
        // Status filter
        if (!empty($status)) {
            $this->db->where('status', $status);
        }
        
        return $this->db->count_all_results();
    }

    /**
     * Create new user
     */
    public function create_user($data)
    {
        // Hash password
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }
        
        // Set timestamps
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->db->insert($this->table, $data);
    }

    /**
     * Update user
     */
    public function update_user($id, $data)
    {
        // Hash password if provided
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        } else {
            // Remove password from data if empty
            unset($data['password']);
        }
        
        // Set timestamp
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $this->db->where('id', $id);
        return $this->db->update($this->table, $data);
    }

    /**
     * Delete user
     */
    public function delete_user($id)
    {
        $this->db->where('id', $id);
        return $this->db->delete($this->table);
    }

    /**
     * Check if username exists
     */
    public function username_exists($username, $exclude_id = null)
    {
        $this->db->where('username', $username);
        
        if ($exclude_id) {
            $this->db->where('id !=', $exclude_id);
        }
        
        $query = $this->db->get($this->table);
        
        return ($query->num_rows() > 0);
    }

    /**
     * Check if email exists
     */
    public function email_exists($email, $exclude_id = null)
    {
        $this->db->where('email', $email);
        
        if ($exclude_id) {
            $this->db->where('id !=', $exclude_id);
        }
        
        $query = $this->db->get($this->table);
        
        return ($query->num_rows() > 0);
    }

    /**
     * Get user statistics
     */
    public function get_user_stats()
    {
        $stats = array();
        
        // Total users
        $stats['total'] = $this->db->count_all($this->table);
        
        // Active users
        $this->db->where('status', 'aktif');
        $stats['active'] = $this->db->count_all_results($this->table);
        
        // Users by role
        $roles = ['admin', 'kasir', 'pimpinan'];
        foreach ($roles as $role) {
            $this->db->where('role', $role);
            $stats[$role] = $this->db->count_all_results($this->table);
        }
        
        return $stats;
    }

    /**
     * Log user login activity
     */
    public function log_login($user_id)
    {
        $data = array(
            'user_id' => $user_id,
            'activity' => 'login',
            'ip_address' => $this->input->ip_address(),
            'user_agent' => $this->input->user_agent(),
            'created_at' => date('Y-m-d H:i:s')
        );
        
        // Create user_logs table if not exists
        $this->_create_user_logs_table();
        
        return $this->db->insert('user_logs', $data);
    }

    /**
     * Log user logout activity
     */
    public function log_logout($user_id)
    {
        $data = array(
            'user_id' => $user_id,
            'activity' => 'logout',
            'ip_address' => $this->input->ip_address(),
            'user_agent' => $this->input->user_agent(),
            'created_at' => date('Y-m-d H:i:s')
        );
        
        // Create user_logs table if not exists
        $this->_create_user_logs_table();
        
        return $this->db->insert('user_logs', $data);
    }

    /**
     * Create user_logs table if not exists
     */
    private function _create_user_logs_table()
    {
        if (!$this->db->table_exists('user_logs')) {
            $this->db->query("
                CREATE TABLE user_logs (
                    id INT(11) NOT NULL AUTO_INCREMENT,
                    user_id INT(11) NOT NULL,
                    activity VARCHAR(50) NOT NULL,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (id),
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                )
            ");
        }
    }

    /**
     * Update user last login
     */
    public function update_last_login($user_id)
    {
        $data = array(
            'last_login' => date('Y-m-d H:i:s')
        );
        
        $this->db->where('id', $user_id);
        return $this->db->update($this->table, $data);
    }

    /**
     * Toggle user status
     */
    public function toggle_status($user_id)
    {
        $user = $this->get_user_by_id($user_id);
        
        if ($user) {
            $new_status = ($user->status == 'aktif') ? 'tidak_aktif' : 'aktif';
            
            $data = array(
                'status' => $new_status,
                'updated_at' => date('Y-m-d H:i:s')
            );
            
            $this->db->where('id', $user_id);
            return $this->db->update($this->table, $data);
        }
        
        return false;
    }

    /**
     * Check if user has bookings
     */
    public function has_bookings($user_id)
    {
        $this->db->where('created_by', $user_id);
        return $this->db->count_all_results('booking') > 0;
    }

    /**
     * Search users
     */
    public function search_users($keyword, $limit = 10)
    {
        $this->db->select('id, username, full_name, email, role, status');
        $this->db->from($this->table);
        
        $this->db->group_start();
        $this->db->like('username', $keyword);
        $this->db->or_like('full_name', $keyword);
        $this->db->or_like('email', $keyword);
        $this->db->group_end();
        
        $this->db->order_by('full_name', 'ASC');
        $this->db->limit($limit);
        
        return $this->db->get()->result();
    }
    
    public function count_users() {
        return $this->db->count_all('users');
    }

    /**
     * Get users by role
     */
    public function get_users_by_role($role) {
        $this->db->select('id, username, full_name, email, role, status');
        $this->db->from($this->table);
        $this->db->where('role', $role);
        $this->db->where('status', 'aktif');
        $this->db->order_by('full_name', 'ASC');

        return $this->db->get()->result();
    }
    
    public function get_user_logs($user_id, $limit = 50, $activity = null) {
        $this->db->where('user_id', $user_id);
        
        if ($activity) {
            $this->db->where('activity', $activity);
        }
        
        $this->db->order_by('created_at', 'DESC');
        $this->db->limit($limit);
        
        return $this->db->get('user_logs')->result();
    }
    
    public function log_user_activity($user_id, $activity, $ip_address, $user_agent) {
        $data = array(
            'user_id' => $user_id,
            'activity' => $activity,
            'ip_address' => $ip_address,
            'user_agent' => $user_agent
        );
        
        return $this->db->insert('user_logs', $data);
    }
    
}