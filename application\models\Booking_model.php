<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Booking_model extends CI_Model {

    public function __construct() {
        parent::__construct();
        $this->load->database();
    }

    /**
     * Get all bookings with optional filters
     */
    public function get_bookings_with_filters($filter_tanggal = null, $filter_status = null, $filter_lapangan = null, $filter_customer_type = null) {
        $this->db->select('
            b.*,
            l.nama_lapangan,
            l.harga_per_jam,
            u.full_name as created_by_name,
            mc.full_name as member_name
        ');
        $this->db->from('booking b');
        $this->db->join('lapangan l', 'b.lapangan_id = l.id', 'left');
        $this->db->join('users u', 'b.created_by = u.id', 'left');
        $this->db->join('member_codes mc', 'b.member_code = mc.member_code', 'left');
        
        // Apply filters
        if ($filter_tanggal) {
            $this->db->where('b.tanggal_booking', $filter_tanggal);
        }
        
        if ($filter_status) {
            $this->db->where('b.status_booking', $filter_status);
        }
        
        if ($filter_lapangan) {
            $this->db->where('b.lapangan_id', $filter_lapangan);
        }
        
        if ($filter_customer_type) {
            $this->db->where('b.customer_type', $filter_customer_type);
        }
        
        $this->db->order_by('b.tanggal_booking', 'DESC');
        $this->db->order_by('b.jam_mulai', 'ASC');
        
        return $this->db->get()->result();
    }

    /**
     * Get bookings for today
     */
    public function get_today_bookings() {
        $today = date('Y-m-d');
        
        $this->db->select('
            b.*,
            l.nama_lapangan
        ');
        $this->db->from('booking b');
        $this->db->join('lapangan l', 'b.lapangan_id = l.id', 'left');
        $this->db->where('b.tanggal_booking', $today);
        $this->db->order_by('b.jam_mulai', 'ASC');
        
        return $this->db->get()->result();
    }

    /**
     * Get bookings that need payment (for kasir)
     */
    public function get_bookings_for_payment() {
        $this->db->select('
            b.*,
            l.nama_lapangan,
            l.harga_per_jam
        ');
        $this->db->from('booking b');
        $this->db->join('lapangan l', 'b.lapangan_id = l.id', 'left');
        $this->db->where('b.status_booking', 'dikonfirmasi');
        $this->db->order_by('b.tanggal_booking', 'ASC');
        $this->db->order_by('b.jam_mulai', 'ASC');
        
        return $this->db->get()->result();
    }

    /**
     * Get booking detail with all related data
     */
    public function get_booking_detail($id) {
        $this->db->select('
            b.*,
            l.nama_lapangan,
            l.deskripsi as lapangan_deskripsi,
            l.harga_per_jam,
            u.full_name as created_by_name,
            mc.full_name as member_name,
            mc.email as member_email,
            t.id as transaksi_id,
            t.jumlah_bayar,
            t.kembalian,
            t.metode_pembayaran,
            t.tanggal_transaksi,
            uk.full_name as kasir_name
        ');
        $this->db->from('booking b');
        $this->db->join('lapangan l', 'b.lapangan_id = l.id', 'left');
        $this->db->join('users u', 'b.created_by = u.id', 'left');
        $this->db->join('member_codes mc', 'b.member_code = mc.member_code', 'left');
        $this->db->join('transaksi t', 'b.id = t.booking_id', 'left');
        $this->db->join('users uk', 't.kasir_id = uk.id', 'left');
        $this->db->where('b.id', $id);
        
        return $this->db->get()->row();
    }

    /**
     * Get booking by ID
     */
    public function get_booking_by_id($id) {
        $this->db->where('id', $id);
        return $this->db->get('booking')->row();
    }

    /**
     * Get booking by booking code
     */
    public function get_booking_by_code($kode_booking) {
        $this->db->where('kode_booking', $kode_booking);
        return $this->db->get('booking')->row();
    }

    /**
     * Insert new booking
     */
    public function insert_booking($data) {
        if ($this->db->insert('booking', $data)) {
            return $this->db->insert_id();
        }
        return false;
    }

    /**
     * Update booking
     */
    public function update_booking($id, $data) {
        $this->db->where('id', $id);
        return $this->db->update('booking', $data);
    }

    /**
     * Update booking status
     */
    public function update_status($id, $status) {
        $data = array(
            'status_booking' => $status,
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        $this->db->where('id', $id);
        return $this->db->update('booking', $data);
    }

    /**
     * Delete booking
     */
    public function delete_booking($id) {
        $this->db->where('id', $id);
        return $this->db->delete('booking');
    }

    /**
     * Check schedule conflict
     */
    public function check_schedule_conflict($lapangan_id, $tanggal_booking, $jam_mulai, $jam_selesai, $exclude_id = null) {
        $this->db->where('lapangan_id', $lapangan_id);
        $this->db->where('tanggal_booking', $tanggal_booking);
        $this->db->where('status_booking !=', 'dibatalkan');
        
        // Check for time overlap
        $this->db->group_start();
            // New booking starts during existing booking
            $this->db->group_start();
                $this->db->where('jam_mulai <=', $jam_mulai);
                $this->db->where('jam_selesai >', $jam_mulai);
            $this->db->group_end();
            
            $this->db->or_group_start();
                // New booking ends during existing booking
                $this->db->where('jam_mulai <', $jam_selesai);
                $this->db->where('jam_selesai >=', $jam_selesai);
            $this->db->group_end();
            
            $this->db->or_group_start();
                // New booking completely contains existing booking
                $this->db->where('jam_mulai >=', $jam_mulai);
                $this->db->where('jam_selesai <=', $jam_selesai);
            $this->db->group_end();
        $this->db->group_end();
        
        // Exclude current booking when editing
        if ($exclude_id) {
            $this->db->where('id !=', $exclude_id);
        }
        
        $result = $this->db->get('booking')->row();
        return $result ? true : false;
    }

    /**
     * Get available time slots for a specific date and lapangan
     */
    public function get_available_slots($lapangan_id, $tanggal_booking, $exclude_id = null) {
        $this->db->select('jam_mulai, jam_selesai');
        $this->db->where('lapangan_id', $lapangan_id);
        $this->db->where('tanggal_booking', $tanggal_booking);
        $this->db->where('status_booking !=', 'dibatalkan');
        
        if ($exclude_id) {
            $this->db->where('id !=', $exclude_id);
        }
        
        $this->db->order_by('jam_mulai', 'ASC');
        
        return $this->db->get('booking')->result();
    }

    /**
     * Get last booking code for generating new code
     */
    public function get_last_booking_code($date_prefix) {
        $this->db->select('kode_booking');
        $this->db->like('kode_booking', 'BK' . $date_prefix, 'after');
        $this->db->order_by('kode_booking', 'DESC');
        $this->db->limit(1);
        
        return $this->db->get('booking')->row();
    }

    /**
     * Get member bookings for member portal
     */
    public function get_member_bookings($member_code, $phone = null) {
        $this->db->select('
            b.*,
            l.nama_lapangan,
            l.deskripsi as lapangan_deskripsi
        ');
        $this->db->from('booking b');
        $this->db->join('lapangan l', 'b.lapangan_id = l.id', 'left');
        $this->db->where('b.member_code', $member_code);
        
        // Additional verification with phone number
        if ($phone) {
            $this->db->where('b.telepon_pemesan', $phone);
        }
        
        $this->db->order_by('b.tanggal_booking', 'DESC');
        $this->db->order_by('b.jam_mulai', 'ASC');
        
        return $this->db->get()->result();
    }

    /**
     * Get booking statistics
     */
    public function get_booking_stats($start_date = null, $end_date = null) {
        if (!$start_date) $start_date = date('Y-m-01'); // First day of current month
        if (!$end_date) $end_date = date('Y-m-d'); // Today
        
        // Total bookings
        $this->db->where('tanggal_booking >=', $start_date);
        $this->db->where('tanggal_booking <=', $end_date);
        $total_bookings = $this->db->count_all_results('booking');
        
        // Bookings by status
        $this->db->select('status_booking, COUNT(*) as count');
        $this->db->where('tanggal_booking >=', $start_date);
        $this->db->where('tanggal_booking <=', $end_date);
        $this->db->group_by('status_booking');
        $status_stats = $this->db->get('booking')->result();
        
        // Revenue (completed bookings only)
        $this->db->select('SUM(harga_total) as total_revenue');
        $this->db->where('tanggal_booking >=', $start_date);
        $this->db->where('tanggal_booking <=', $end_date);
        $this->db->where('status_booking', 'selesai');
        $revenue = $this->db->get('booking')->row();
        
        // Member vs Non-member bookings
        $this->db->select('customer_type, COUNT(*) as count, SUM(harga_total) as revenue');
        $this->db->where('tanggal_booking >=', $start_date);
        $this->db->where('tanggal_booking <=', $end_date);
        $this->db->where('status_booking', 'selesai');
        $this->db->group_by('customer_type');
        $customer_stats = $this->db->get('booking')->result();
        
        // Most popular lapangan
        $this->db->select('l.nama_lapangan, COUNT(b.id) as booking_count');
        $this->db->from('booking b');
        $this->db->join('lapangan l', 'b.lapangan_id = l.id');
        $this->db->where('b.tanggal_booking >=', $start_date);
        $this->db->where('b.tanggal_booking <=', $end_date);
        $this->db->group_by('b.lapangan_id');
        $this->db->order_by('booking_count', 'DESC');
        $this->db->limit(5);
        $popular_lapangan = $this->db->get()->result();
        
        return array(
            'total_bookings' => $total_bookings,
            'status_stats' => $status_stats,
            'total_revenue' => $revenue->total_revenue ?: 0,
            'customer_stats' => $customer_stats,
            'popular_lapangan' => $popular_lapangan
        );
    }

    /**
     * Get daily booking report
     */
    public function get_daily_report($date) {
        $this->db->select('
            b.*,
            l.nama_lapangan,
            mc.full_name as member_name,
            t.jumlah_bayar,
            t.metode_pembayaran,
            t.tanggal_transaksi
        ');
        $this->db->from('booking b');
        $this->db->join('lapangan l', 'b.lapangan_id = l.id', 'left');
        $this->db->join('member_codes mc', 'b.member_code = mc.member_code', 'left');
        $this->db->join('transaksi t', 'b.id = t.booking_id', 'left');
        $this->db->where('b.tanggal_booking', $date);
        $this->db->order_by('b.jam_mulai', 'ASC');
        
        return $this->db->get()->result();
    }

    /**
     * Get booking report with date range
     */
    public function get_booking_report($start_date, $end_date, $customer_type = null, $status = null) {
        $this->db->select('
            b.*,
            l.nama_lapangan,
            mc.full_name as member_name,
            u.full_name as created_by_name
        ');
        $this->db->from('booking b');
        $this->db->join('lapangan l', 'b.lapangan_id = l.id', 'left');
        $this->db->join('member_codes mc', 'b.member_code = mc.member_code', 'left');
        $this->db->join('users u', 'b.created_by = u.id', 'left');
        
        $this->db->where('b.tanggal_booking >=', $start_date);
        $this->db->where('b.tanggal_booking <=', $end_date);
        
        if ($customer_type) {
            $this->db->where('b.customer_type', $customer_type);
        }
        
        if ($status) {
            $this->db->where('b.status_booking', $status);
        }
        
        $this->db->order_by('b.tanggal_booking', 'DESC');
        $this->db->order_by('b.jam_mulai', 'ASC');
        
        return $this->db->get()->result();
    }

    /**
     * Get revenue report
     */
    public function get_revenue_report($start_date, $end_date, $customer_type = null) {
        $this->db->select('
            DATE(b.tanggal_booking) as tanggal,
            COUNT(b.id) as total_booking,
            SUM(b.harga_total) as total_revenue,
            SUM(CASE WHEN b.customer_type = "member" THEN b.harga_total ELSE 0 END) as member_revenue,
            SUM(CASE WHEN b.customer_type = "non_member" THEN b.harga_total ELSE 0 END) as non_member_revenue,
            SUM(CASE WHEN b.customer_type = "member" THEN 1 ELSE 0 END) as member_bookings,
            SUM(CASE WHEN b.customer_type = "non_member" THEN 1 ELSE 0 END) as non_member_bookings
        ');
        $this->db->from('booking b');
        $this->db->where('b.tanggal_booking >=', $start_date);
        $this->db->where('b.tanggal_booking <=', $end_date);
        $this->db->where('b.status_booking', 'selesai'); // Only completed bookings
        
        if ($customer_type) {
            $this->db->where('b.customer_type', $customer_type);
        }
        
        $this->db->group_by('DATE(b.tanggal_booking)');
        $this->db->order_by('tanggal', 'ASC');
        
        return $this->db->get()->result();
    }

    /**
     * Get upcoming bookings (next 7 days)
     */
    public function get_upcoming_bookings($limit = 10) {
        $today = date('Y-m-d');
        $next_week = date('Y-m-d', strtotime('+7 days'));
        
        $this->db->select('
            b.*,
            l.nama_lapangan
        ');
        $this->db->from('booking b');
        $this->db->join('lapangan l', 'b.lapangan_id = l.id', 'left');
        $this->db->where('b.tanggal_booking >=', $today);
        $this->db->where('b.tanggal_booking <=', $next_week);
        $this->db->where('b.status_booking !=', 'dibatalkan');
        $this->db->order_by('b.tanggal_booking', 'ASC');
        $this->db->order_by('b.jam_mulai', 'ASC');
        $this->db->limit($limit);
        
        return $this->db->get()->result();
    }

    /**
     * Search bookings
     */
    public function search_bookings($keyword) {
        $this->db->select('
            b.*,
            l.nama_lapangan
        ');
        $this->db->from('booking b');
        $this->db->join('lapangan l', 'b.lapangan_id = l.id', 'left');
        
        $this->db->group_start();
            $this->db->like('b.kode_booking', $keyword);
            $this->db->or_like('b.nama_pemesan', $keyword);
            $this->db->or_like('b.telepon_pemesan', $keyword);
            $this->db->or_like('b.member_code', $keyword);
        $this->db->group_end();
        
        $this->db->order_by('b.tanggal_booking', 'DESC');
        $this->db->limit(20);
        
        return $this->db->get()->result();
    }

    /**
     * Get member booking history with statistics
     */
    public function get_member_booking_history($member_code) {
        $this->db->select('
            b.*,
            l.nama_lapangan,
            t.jumlah_bayar,
            t.metode_pembayaran
        ');
        $this->db->from('booking b');
        $this->db->join('lapangan l', 'b.lapangan_id = l.id', 'left');
        $this->db->join('transaksi t', 'b.id = t.booking_id', 'left');
        $this->db->where('b.member_code', $member_code);
        $this->db->order_by('b.tanggal_booking', 'DESC');

        return $this->db->get()->result();
    }

    /**
     * Get confirmed bookings that haven't been paid
     */
    public function get_confirmed_unpaid_bookings() {
        $this->db->select('
            b.*,
            l.nama_lapangan,
            l.harga_per_jam,
            u.full_name as created_by_name,
            mc.full_name as member_name
        ');
        $this->db->from('booking b');
        $this->db->join('lapangan l', 'b.lapangan_id = l.id', 'left');
        $this->db->join('users u', 'b.created_by = u.id', 'left');
        $this->db->join('member_codes mc', 'b.member_code = mc.member_code', 'left');
        $this->db->join('transaksi t', 'b.id = t.booking_id', 'left');
        $this->db->where('b.status_booking', 'dikonfirmasi');
        $this->db->where('t.id IS NULL'); // No transaction record
        $this->db->order_by('b.tanggal_booking', 'ASC');
        $this->db->order_by('b.jam_mulai', 'ASC');

        return $this->db->get()->result();
    }
    
    public function count_bookings($start_date = null, $end_date = null, $status = null) {
        if ($start_date) {
            $this->db->where('tanggal_booking >=', $start_date);
        }
        
        if ($end_date) {
            $this->db->where('tanggal_booking <=', $end_date);
        }
        
        if ($status) {
            $this->db->where('status_booking', $status);
        }
        
        return $this->db->count_all_results('booking');
    }
    
    
}